import {
    FileDeletedEvent,
    FileDidMoveEvent,
} from "@augment-internal/sidecar-libs/src/agent/agent-edit-types";
import { DisposableService } from "@augment-internal/sidecar-libs/src/lifecycle/disposable-service";

import { APIServer, Blobs } from "./augment-api";
import { getErrmsg } from "./exceptions";
import { FeatureFlagManager, FeatureFlags } from "./feature-flags";
import { newFileReader } from "./file-reader";
import { getLogger } from "./logging";
import { ChangedFile, FileChangeType } from "./remote-agent-manager/types";
import { IgnoreSourceBuiltin, IgnoreSourceFile, IgnoreStackBuilder } from "./utils/ignore-file";
import { FullPathFilter, PathIterator } from "./utils/path-iterator";
import { makePathFilter } from "./utils/path-iterator";
import { FileType } from "./utils/types";
import * as vscode from "./vscode";
import { BlobsCheckpointManager } from "./workspace/blobs-checkpoint-manager";
import { DiskFileManager } from "./workspace/disk-file-manager";
import { FilesystemChangeTracker } from "./workspace/filesystem-change-tracker";
import { PathHandlerImpl } from "./workspace/path-handler";
import { PathMap } from "./workspace/path-map";
import { PathHandler } from "./workspace/types";

export interface BeachheadWorkspaceManager {
    workspaceRoot: string;
    getCheckpoint(): Blobs | undefined;
    createSnapshot(): number;
    getChangesSince(snapshotId: number): Promise<ChangedFile[]>;
    awaitBlobsUploaded(): Promise<void>;
    onFileDeleted: vscode.Event<FileDeletedEvent>;
    onFileDidMove: vscode.Event<FileDidMoveEvent>;
}

export class BeachheadWorkspaceManagerImpl
    extends DisposableService
    implements BeachheadWorkspaceManager
{
    private _pathMap: PathMap;
    private _pathHandler: PathHandler;
    private _diskFileManager: DiskFileManager;
    private _blobsCheckpointManager: BlobsCheckpointManager;
    private _featureFlagManager: FeatureFlagManager;
    private _folderId: number;
    private _maxUploadSizeBytes: number;
    private _pathFilter: FullPathFilter | undefined;

    // Filesystem change tracking
    private _filesystemChangeTracker: FilesystemChangeTracker | undefined;

    private _logger = getLogger("WorkspaceManager");

    private readonly _fileDeletedEmitter = this.addDisposable(
        new vscode.EventEmitter<FileDeletedEvent>()
    );
    private readonly _fileDidMoveEmitter = this.addDisposable(
        new vscode.EventEmitter<FileDidMoveEvent>()
    );

    constructor(
        private _workspaceRoot: string,
        private _apiServer: APIServer,
        featureFlags: FeatureFlags
    ) {
        super();

        this._maxUploadSizeBytes = featureFlags.maxUploadSizeBytes;
        this._featureFlagManager = new FeatureFlagManager({ initialFlags: featureFlags });

        this._pathMap = new PathMap();
        this._pathHandler = new PathHandlerImpl(this._maxUploadSizeBytes, newFileReader());
        this._diskFileManager = new DiskFileManager(
            "beachhead",
            this._apiServer,
            this._pathHandler,
            this._pathMap
        );
        this._blobsCheckpointManager = new BlobsCheckpointManager(
            this._apiServer,
            this._featureFlagManager,
            this._pathMap.onDidChangeBlobName
        );

        this._filesystemChangeTracker = undefined;
        this._folderId = this._pathMap.openSourceFolder(this.workspaceRoot, this.workspaceRoot);

        this.addDisposables(
            this._pathMap,
            this._featureFlagManager,
            this._diskFileManager,
            this._blobsCheckpointManager
        );
    }

    get workspaceRoot(): string {
        return this._workspaceRoot;
    }

    public async initialize(): Promise<void> {
        const ignoreStackBuilder = new IgnoreStackBuilder([
            new IgnoreSourceFile(".gitignore"),
            new IgnoreSourceBuiltin(this.workspaceRoot),
            new IgnoreSourceFile(".augmentignore"),
        ]);
        this._pathFilter = await makePathFilter(
            vscode.Uri.file(this.workspaceRoot),
            vscode.Uri.file(this.workspaceRoot),
            ignoreStackBuilder,
            undefined
        );

        this._filesystemChangeTracker = new FilesystemChangeTracker(
            this._workspaceRoot,
            this._pathFilter,
            this._maxUploadSizeBytes
        );
        this.addDisposable(this._filesystemChangeTracker);

        // Set up event handler for file changes to trigger blob uploads
        this.addDisposable(
            this._filesystemChangeTracker.onFileChange((change) => {
                this._handleFileChange(change);
            })
        );

        // Create path iterator and enumerate paths
        const pathIterator = new PathIterator(
            this.workspaceRoot,
            vscode.Uri.file(this.workspaceRoot),
            vscode.Uri.file(this.workspaceRoot),
            this._pathFilter
        );
        for await (const [_fileUri, relPath, fileType, acceptance] of pathIterator) {
            this._pathMap.insert(this._folderId, relPath, fileType, acceptance);
            if (fileType === FileType.file && acceptance.accepted) {
                this._diskFileManager.ingestPath(this._folderId, relPath);
            }
        }
        // To enable quick startup, don't wait for blob uploads to complete
        // Instead, we will wait for blob uploads when needed, such as before
        // Before calling codebase-retrieval tool.
        // await this._diskFileManager.awaitQuiesced();
    }

    public getCheckpoint(): Blobs | undefined {
        return this._blobsCheckpointManager.getContext();
    }

    public get onFileDeleted(): vscode.Event<FileDeletedEvent> {
        return this._fileDeletedEmitter.event;
    }

    public get onFileDidMove(): vscode.Event<FileDidMoveEvent> {
        return this._fileDidMoveEmitter.event;
    }

    /**
     * Create a snapshot of the current filesystem state for change tracking
     * @returns Snapshot ID that can be used with getChangesSince()
     */
    public createSnapshot(): number {
        // Change tracker is always available after initialization
        return this._filesystemChangeTracker?.createSnapshot() || -1;
    }

    /**
     * Get changes since snapshotId using the filesystem change tracker
     * @param snapshotId The snapshotId to get changes since
     * @returns An array of changed files since the snapshotId
     */
    public async getChangesSince(snapshotId: number): Promise<ChangedFile[]> {
        // Rebuilding the Path filter in case it was changed
        const ignoreStackBuilder = new IgnoreStackBuilder([
            new IgnoreSourceFile(".gitignore"),
            new IgnoreSourceBuiltin(this.workspaceRoot),
            new IgnoreSourceFile(".augmentignore"),
        ]);
        this._pathFilter = await makePathFilter(
            vscode.Uri.file(this.workspaceRoot),
            vscode.Uri.file(this.workspaceRoot),
            ignoreStackBuilder,
            undefined
        );
        try {
            const changes = await this._filesystemChangeTracker?.getChangesSince(snapshotId);
            if (changes) {
                return changes.filter((change) => {
                    const acceptance = this._pathFilter!.getPathInfo(
                        change.new_path,
                        FileType.file
                    );
                    return acceptance.accepted;
                });
            }
        } catch (error) {
            this._logger.error(`Error during change detection: ${getErrmsg(error)}`);
        }
        return [];
    }

    /**
     * Handle a single file change by updating path map and triggering blob upload asynchronously
     */
    private _handleFileChange(change: ChangedFile): void {
        const acceptance = this._pathFilter!.getPathInfo(change.new_path, FileType.file);
        try {
            switch (change.change_type) {
                case FileChangeType.added:
                    this._pathMap.insert(
                        this._folderId,
                        change.new_path,
                        FileType.file,
                        acceptance
                    );
                    break;
                case FileChangeType.deleted:
                    this._pathMap.remove(this._folderId, change.new_path);
                    break;
                case FileChangeType.modified:
                    // No changes to path map for modified files
                    break;
                case FileChangeType.renamed:
                    this._pathMap.remove(this._folderId, change.old_path);
                    this._pathMap.insert(
                        this._folderId,
                        change.new_path,
                        FileType.file,
                        acceptance
                    );
                    break;
            }

            if (change.change_type !== FileChangeType.deleted && acceptance.accepted) {
                this._diskFileManager.ingestPath(this._folderId, change.new_path);
            }
        } catch (error) {
            this._logger.error(`Error handling file change: ${getErrmsg(error)}`);
        }
    }

    /**
     * Wait for all blob uploads to complete
     * @returns Promise that resolves when all blob uploads are finished
     */
    public async awaitBlobsUploaded(): Promise<void> {
        await this._diskFileManager.awaitQuiesced();
    }

    /**
     * Dispose of resources including the filesystem change tracker
     */
    public dispose(): void {
        super.dispose();
    }
}
