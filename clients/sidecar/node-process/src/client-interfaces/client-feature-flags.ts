import { SidecarFlags as SidecarFlagsProto } from "$clients/sidecar/node-process/protos/sidecarrpc_pb";
import {
  IClientFeatureFlags,
  SidecarFlags as ISideCarFlags,
  MemoriesParams,
} from "@augment-internal/sidecar-libs/src/client-interfaces/feature-flags";
import { cloneDeep } from "lodash";
import { EventEmitter } from "stream";

export enum flagManagerEvents {
  FlagsChanged = "flags-changed",
}

class ClientFeatureFlags extends EventEmitter implements IClientFeatureFlags {
  private _flags = new SidecarFlagsProto({
    agentEditTool: "",
    enableChatWithTools: false,
    enableAgentMode: false,
    memoriesParamsJson: "{}",
    agentEditToolMinViewSize: 0,
    agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested",
    agentEditToolEnableFuzzyMatching: false,
    agentEditToolFuzzyMatchSuccessMessage:
      "Replacement successful. old_str and new_str were slightly modified to match the original file content.",
    agentEditToolFuzzyMatchMaxDiff: 50,
    agentEditToolFuzzyMatchMaxDiffRatio: 0.15,
    agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs: 5,
    agentEditToolInstructionsReminder: false,
    agentEditToolShowResultSnippet: true,
    agentEditToolMaxLines: 200,
    agentSaveFileToolInstructionsReminder: false,
    enableTaskList: false,
    enableSupportToolUseStart: false,
    grepSearchToolEnable: false,
    grepSearchToolTimelimitSec: 10,
    grepSearchToolOutputCharsLimit: 5000,
    grepSearchToolNumContextLines: 5,
  });

  public get flags(): ISideCarFlags {
    return cloneDeep({
      enableChatWithTools: this._flags.enableChatWithTools,
      enableAgentMode: this._flags.enableAgentMode,
      agentEditTool: this._flags.agentEditTool,
      agentEditToolMinViewSize: this._flags.agentEditToolMinViewSize,
      agentEditToolSchemaType: this._flags.agentEditToolSchemaType,
      agentEditToolEnableFuzzyMatching:
        this._flags.agentEditToolEnableFuzzyMatching,
      agentEditToolFuzzyMatchSuccessMessage:
        this._flags.agentEditToolFuzzyMatchSuccessMessage,
      agentEditToolFuzzyMatchMaxDiff:
        this._flags.agentEditToolFuzzyMatchMaxDiff,
      agentEditToolFuzzyMatchMaxDiffRatio:
        this._flags.agentEditToolFuzzyMatchMaxDiffRatio,
      agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs:
        this._flags.agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs,
      agentEditToolInstructionsReminder:
        this._flags.agentEditToolInstructionsReminder,
      agentEditToolShowResultSnippet:
        this._flags.agentEditToolShowResultSnippet,
      agentEditToolMaxLines: this._flags.agentEditToolMaxLines,
      agentSaveFileToolInstructionsReminder:
        this._flags.agentSaveFileToolInstructionsReminder,
      memoriesParams: parseMemoriesParamsJson(this._flags.memoriesParamsJson),
      enableTaskList: this._flags.enableTaskList,
      enableSupportToolUseStart: this._flags.enableSupportToolUseStart,
      grepSearchToolEnable: this._flags.grepSearchToolEnable,
      grepSearchToolTimelimitSec: this._flags.grepSearchToolTimelimitSec,
      grepSearchToolOutputCharsLimit:
        this._flags.grepSearchToolOutputCharsLimit,
      grepSearchToolNumContextLines: this._flags.grepSearchToolNumContextLines,
    });
  }

  public updateFlags(f: SidecarFlagsProto) {
    this._flags = f;
    this.emit(flagManagerEvents.FlagsChanged);
  }
}

/**
 * Parses a JSON string containing memories parameters into a simple TypeScript object
 * with primitive values.
 */
function parseMemoriesParamsJson(jsonStr: string): MemoriesParams {
  if (!jsonStr || jsonStr.trim() === "") {
    return {};
  }

  try {
    return (JSON.parse(jsonStr) || {}) as MemoriesParams;
  } catch (error) {
    console.error("Failed to parse memories params JSON:", error);
    return {};
  }
}

export const clientFeatureFlags = new ClientFeatureFlags();
