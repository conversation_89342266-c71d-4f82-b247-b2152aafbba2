import {
  Chat<PERSON><PERSON>,
  Chat<PERSON>ayload,
  ChatRequestNode,
  Exchange,
  PersonaType,
  ReplacementText,
  Rule,
} from "../chat/chat-types";
import { Chat<PERSON><PERSON><PERSON> } from "../client-interfaces/api-client-types";
import { getClientAuth, IClientAuth } from "../client-interfaces/client-auth";
import {
  getClientConfig,
  IClientConfig,
  SidecarAugmentConfig,
} from "../client-interfaces/client-config";
import { APIError, getErrmsg } from "../exceptions";
import { AugmentLogger, getLogger } from "../logging";
import { ToolDefinition } from "../tools/tool-types";
import { abortSignalAny } from "../utils/abort-signal";
import { getPropertySizes } from "../utils/object-utils";
import { retryWithBackoff, withTimeout } from "../utils/promise-utils";
import { APIStatus } from "../utils/types";
import { VCSChange } from "../vcs/watcher/types";
import {
  Blo<PERSON>,
  FetchFunction,
  InvalidCompletionURLError,
  ReadableStreamReader,
  STREAM_TIMEOUT,
} from "./types";
import {
  blobsToBlobsPayload,
  safeJsonStringify,
  toChatResult,
  toVCSChangePayload,
} from "./utils";

/**
 * The SidecarAPIServer interface defines the subset of the Augment API that
 * is implemented in the sidecar library.
 */
export interface SidecarAPIServer {
  chatStream(
    requestId: string,
    message: string,
    chatHistory: Exchange[],
    blobs: Blobs,
    userGuidedBlobs: string[],
    externalSourceIds: string[],
    model: string | undefined,
    vcsChange: VCSChange,
    recentChanges: ReplacementText[],
    contextCodeExchangeRequestId?: string,
    selectedCode?: string,
    prefix?: string,
    suffix?: string,
    pathName?: string,
    language?: string,
    sessionId?: string,
    disableAutoExternalSources?: boolean,
    userGuidelines?: string,
    workspaceGuidelines?: string,
    toolDefinitions?: ToolDefinition[],
    nodes?: ChatRequestNode[],
    mode?: ChatMode,
    agentMemories?: string,
    personaType?: PersonaType,
    rules?: Rule[],
    silent?: boolean,
    enableSupportToolUseStart?: boolean,
  ): Promise<AsyncIterable<ChatResult>>;
}

/**
 * The SidecarAPIServerImpl class implements a subset of the Augment API. It is
 * designed to be agnostic to the client and can be used by any client that
 * implements the IClientConfig and IClientAuth interfaces.
 *
 * A client-specific implmentation can can extend this class to add additional
 * API endpoints. As new endpoints are created in the future, they should be
 * added to the SidecarAPIServer interface and implemented in this class so
 * that they are available to all clients.
 */
export class SidecarAPIServerImpl implements SidecarAPIServer {
  static readonly defaultRequestTimeoutMs = 30000;

  private readonly logger: AugmentLogger = getLogger("AugmentExtensionSidecar");

  private clientConfig: IClientConfig = getClientConfig();
  private clientAuth: IClientAuth = getClientAuth();

  constructor(
    public readonly sessionId: string,
    private readonly userAgent: string,
    private readonly fetchFunction: FetchFunction,
  ) {}

  public async chatStream(
    requestId: string,
    message: string,
    chatHistory: Exchange[],
    blobs: Blobs,
    userGuidedBlobs: string[],
    externalSourceIds: string[],
    model: string | undefined,
    vcsChange: VCSChange,
    recentChanges: ReplacementText[],
    contextCodeExchangeRequestId?: string,
    selectedCode?: string,
    prefix?: string,
    suffix?: string,
    pathName?: string,
    language?: string,
    sessionId?: string,
    disableAutoExternalSources?: boolean,
    userGuidelines?: string,
    workspaceGuidelines?: string,
    toolDefinitions?: ToolDefinition[],
    nodes?: ChatRequestNode[],
    mode?: ChatMode,
    agentMemories?: string,
    personaType?: PersonaType,
    rules?: Rule[],
    silent?: boolean,
  ): Promise<AsyncIterable<ChatResult>> {
    const config = await this.clientConfig.getConfig();
    if (mode === ChatMode.agent) {
      model = model ?? config.agent.model;
    } else {
      model = model ?? config.chat.model;
    }
    const payload: ChatPayload = {
      model,
      path: pathName,
      prefix: prefix,
      selected_code: selectedCode,
      suffix: suffix,
      message: message,
      chat_history: chatHistory,
      lang: language,
      blobs: blobsToBlobsPayload(blobs),
      user_guided_blobs: userGuidedBlobs,
      context_code_exchange_request_id: contextCodeExchangeRequestId,
      vcs_change: toVCSChangePayload(vcsChange),
      recency_info_recent_changes: recentChanges,
      external_source_ids: externalSourceIds,
      disable_auto_external_sources: disableAutoExternalSources,
      user_guidelines: userGuidelines,
      workspace_guidelines: workspaceGuidelines,
      feature_detection_flags: {
        support_raw_output: true, // Added Nov 2024
      },
      tool_definitions: toolDefinitions ?? [],
      nodes: nodes ?? [], // protobuf doesn't support optional repeated fields
      mode: mode ?? ChatMode.chat,
      agent_memories: agentMemories,
      persona_type: personaType,
      rules: rules ?? [],
      silent: silent,
    };

    const startChatStream = (): Promise<AsyncIterable<ChatResult>> => {
      return this.callApiStream<ChatResult>(
        requestId,
        config,
        "chat-stream",
        payload,
        toChatResult,
        // If chat URL is not configured, the function will use the completion URL.
        config.chat.url,
        300000,
        sessionId,
        undefined,
        true,
      );
    };
    return await retryWithBackoff(startChatStream, this.logger, {
      initialMS: 250, // Initial delay is 250ms
      mult: 2, // Exponential backoff
      maxMS: 5000, // Max delay is 5s
      maxTries: 5, // Max number of retries is 5
      maxTotalMs: 5000, // Max total time spent is 5s
    });
  }

  /**
   * Call an API endpoint that returns a stream of server-side events
   * @param requestId
   * @param config
   * @param apiEndpoint
   * @param body
   * @param convert
   * @param baseURL
   * @param requestTimeoutMs
   * @returns
   */
  protected async callApiStream<T>(
    requestId: string,
    config: Readonly<SidecarAugmentConfig>,
    apiEndpoint: string,
    body?: Record<string, any>,
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    convert: (json: any) => T = (json: any) => json,
    baseURL?: string,
    requestTimeoutMs?: number,
    sessionId?: string,
    signal?: AbortSignal,
    dropAndReplaceSurrogates: boolean = false,
  ): Promise<AsyncIterable<T>> {
    const token = await this.clientAuth.getAPIToken();
    baseURL = baseURL ?? (await this.clientAuth.getCompletionURL());

    if (!baseURL) {
      throw new Error("Please configure Augment API URL");
    }
    let apiURL: URL | undefined;
    try {
      apiURL = new URL(apiEndpoint, baseURL);
    } catch (e) {
      this.logger.error("Augment API URL is invalid:", e);
      throw new InvalidCompletionURLError();
    }
    if (!apiURL.protocol.startsWith("http")) {
      throw new Error(
        "Augment API URL must start with 'http://' or 'https://'",
      );
    }

    // Step zero, prepare to do the API call
    const body_json = safeJsonStringify(
      body,
      this.logger,
      dropAndReplaceSurrogates,
    );

    const timeoutMs =
      requestTimeoutMs ?? SidecarAPIServerImpl.defaultRequestTimeoutMs;
    const timeoutSignal = AbortSignal.timeout(timeoutMs);

    // Combine the timeout signal with the abort signal if provided, so that
    // either one aborting will cancel the request.
    const combinedSignal = signal
      ? abortSignalAny([timeoutSignal, signal])
      : timeoutSignal;

    const method = "POST";
    // Step one do the api call and get respose
    let response: Response;

    try {
      const headers: { [key: string]: string } = {
        "Content-Type": "application/json",
        "User-Agent": this.userAgent,
        "x-request-id": `${requestId}`,
        "x-request-session-id": `${sessionId ?? this.sessionId}`,
      };
      if (token) {
        headers["Authorization"] = `Bearer ${token}`;
      }

      response = await withTimeout(
        this.fetchFunction(apiURL.toString(), {
          method,
          headers,
          body: body_json,
          signal: combinedSignal,
        }),
        timeoutMs,
      );
    } catch (e) {
      if (e instanceof Error) {
        this.logger.error(
          `API request ${requestId} to ${apiURL.toString()} failed: ${getErrmsg(e, true)}`,
        );
        if (e.stack) {
          this.logger.error(e.stack);
        }
        throw APIError.transientIssue(e);
      }
      throw e;
    }
    // Step 2 verify resposne
    if (!response!.ok) {
      if (response.status === 499) {
        // Don't log 499 errors as there are lots of them and they are generally not of
        // interest.
        throw new APIError(APIStatus.cancelled, "Request cancelled");
      }
      if (response.status === 401) {
        // Removing the session token will reload the extension,
        // forcing the user to sign in.
        void this.clientAuth.removeAuthSession();
      }
      if (response.status === 400 && config.enableDebugFeatures) {
        // print body of response
        this.logger.error(
          `API request ${requestId} to ${apiURL.toString()} failed: ${await response.clone().text()}`,
        );
      }
      this.logger.error(
        `API request ${requestId} to ${apiURL.toString()} ` +
          `response ${response.status}: ${response.statusText}`,
      );
      // Check if this is a "too large" error based on status code
      if (response.status === 413) {
        this.logger.debug(
          `object size is ${body ? getPropertySizes(body) : 0} `,
        );
      }

      throw await APIError.fromResponse(response);
    }
    // Step 3 read stream and convert
    const reader = response.body!.getReader();
    const logger = this.logger; // Make logger available to generator function
    async function* readerToGenerator(
      reader: ReadableStreamReader<Uint8Array>,
    ): AsyncGenerator<T, void, undefined> {
      const textDecoder = new TextDecoder();
      let textBuffer = "";
      try {
        while (true) {
          const { value, done } = await reader.read(new Uint8Array());
          if (done) {
            return;
          }
          textBuffer += textDecoder.decode(value, { stream: true });
          // Parse Newline Delimited JSON
          while (textBuffer.includes("\n")) {
            const newLineIndex = textBuffer.indexOf("\n");
            const text = textBuffer.substring(0, newLineIndex);
            textBuffer = textBuffer.substring(newLineIndex + 1);
            try {
              // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
              const json = JSON.parse(text);
              yield convert(json);
            } catch (e: any) {
              logger.error(`JSON parse failed for ${text}: ${getErrmsg(e)}`);
            }
          }
        }
      } catch (e) {
        // If the request was aborted by the signal passed to callApiStream,
        // then we should throw the AbortError with the reason provided by the signal.
        if (e instanceof DOMException && e.name === "AbortError") {
          // First we check the signal passed to callApiStream since that's a higher priority
          // than the timeout signal.
          if (signal?.aborted && typeof signal.reason === "string") {
            const reason = signal.reason;
            throw new DOMException(reason, "AbortError");
          }
          // If there was no reason provided by the external signal, then we can assume it was the timeout.
          if (timeoutSignal.aborted) {
            const reason = STREAM_TIMEOUT;
            throw new DOMException(reason, "AbortError");
          }
        }
        throw e;
      }
    }
    return readerToGenerator(reader);
  }
}
