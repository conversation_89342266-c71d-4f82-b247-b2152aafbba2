/**
 * @file task-tools.ts
 * This file contains tools for the agent to interact with the task system.
 */

import { Exchange } from "../../chat/chat-types";
import { getLogger } from "../../logging";
import { errorToolResponse, successToolResponse } from "./tool-use-response";
import { ToolBase, ToolSafety, ToolUseResponse } from "../tool-types";
import { SidecarToolType } from "./sidecar-tool-types";
import { TaskManager } from "../../agent/task/task-manager";
import { TaskUpdatedBy, TaskState } from "../../agent/task/task-types";
import {
  getMarkdownRepresentation,
  parseMarkdownToTaskTree,
  diffTaskTrees,
  taskDiffToMarkdown,
  TaskInstructionUtils,
} from "../../agent/task/task-utils";

/**
 * Tool for viewing the current conversation's task list.
 * This tool retrieves the root task for the current conversation
 * and returns its markdown representation.
 */
export class ViewTaskListTool extends ToolBase<SidecarToolType.viewTaskList> {
  private readonly _logger = getLogger("ViewTaskListTool");

  constructor(private readonly _taskManager: TaskManager) {
    super(SidecarToolType.viewTaskList, ToolSafety.Safe);
  }

  public description = TaskInstructionUtils.getToolDescriptions().viewTaskList;

  public inputSchemaJson = JSON.stringify({
    type: "object",
    properties: {},
    required: [],
  });

  public checkToolCallSafe(): boolean {
    return true; // This tool is always safe
  }

  public async call(
    _toolInput: Record<string, unknown>,
    _chatHistory: Exchange[],
    _abortSignal: AbortSignal,
  ): Promise<ToolUseResponse> {
    try {
      const rootTaskUuid = this._taskManager.getCurrentRootTaskUuid();
      if (!rootTaskUuid) {
        return errorToolResponse("No root task found.");
      }

      // Get the hydrated task tree
      const rootTask = await this._taskManager.getHydratedTask(rootTaskUuid);
      if (!rootTask) {
        return errorToolResponse(`Task with UUID ${rootTaskUuid} not found.`);
      }

      // Convert the task tree to markdown
      const markdown = getMarkdownRepresentation(rootTask);

      return successToolResponse(
        TaskInstructionUtils.formatTaskListViewResponse(markdown)
      );
    } catch (error) {
      this._logger.error("Error in ViewTaskListTool:", error);
      return errorToolResponse(
        `Failed to view task list: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }
}

/**
 * Tool for updating one or more tasks' properties.
 * This tool allows efficient updates to individual or multiple tasks without
 * requiring the entire task tree to be sent.
 *
 * Use this tool for:
 * - Changing task states (NOT_STARTED, IN_PROGRESS, CANCELLED, COMPLETE)
 * - Updating task names or descriptions
 * - Batch updates to multiple tasks efficiently
 * - Single atomic updates to avoid token overhead
 */
export class UpdateTasksTool extends ToolBase<SidecarToolType.updateTasks> {
  private readonly _logger = getLogger("UpdateTasksTool");

  constructor(private readonly _taskManager: TaskManager) {
    super(SidecarToolType.updateTasks, ToolSafety.Safe);
  }

  public description = TaskInstructionUtils.getToolDescriptions().updateTasks;

  public inputSchemaJson = JSON.stringify({
    type: "object",
    properties: {
      task_id: {
        type: "string",
        description: "The UUID of the task to update. Use this for single task updates. Either task_id or tasks array is required.",
      },
      tasks: {
        type: "array",
        description: "Array of tasks to update. Use this for multiple task updates. Each task should have a task_id and the properties to update. Either task_id or tasks array is required.",
        items: {
          type: "object",
          properties: {
            task_id: {
              type: "string",
              description: "The UUID of the task to update.",
            },
            state: {
              type: "string",
              enum: ["NOT_STARTED", "IN_PROGRESS", "CANCELLED", "COMPLETE"],
              description: "New task state. Use NOT_STARTED for [ ], IN_PROGRESS for [/], CANCELLED for [-], COMPLETE for [x].",
            },
            name: {
              type: "string",
              description: "New task name.",
            },
            description: {
              type: "string",
              description: "New task description.",
            },
          },
          required: ["task_id"],
        },
      },
      state: {
        type: "string",
        enum: ["NOT_STARTED", "IN_PROGRESS", "CANCELLED", "COMPLETE"],
        description: "New task state for single task update. Use NOT_STARTED for [ ], IN_PROGRESS for [/], CANCELLED for [-], COMPLETE for [x].",
      },
      name: {
        type: "string",
        description: "New task name for single task update.",
      },
      description: {
        type: "string",
        description: "New task description for single task update.",
      },
    },
  });

  public checkToolCallSafe(): boolean {
    return true; // This tool is always safe
  }

  public async call(
    toolInput: Record<string, unknown>,
    _chatHistory: Exchange[],
    _abortSignal: AbortSignal,
  ): Promise<ToolUseResponse> {
    try {
      // Check if this is a batch update (tasks array) or single update (task_id)
      const tasks = toolInput.tasks as Array<Record<string, unknown>> | undefined;
      const singleTaskId = toolInput.task_id as string | undefined;

      if (tasks && tasks.length > 0) {
        // Handle batch update
        return await this.handleBatchUpdate(tasks);
      } else if (singleTaskId) {
        // Handle single task update
        return await this.handleSingleUpdate(singleTaskId, toolInput);
      } else {
        return errorToolResponse("Either task_id or tasks array is required.");
      }
    } catch (error) {
      this._logger.error("Error in UpdateTasksTool:", error);
      return errorToolResponse(
        `Failed to update task(s): ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  private async handleSingleUpdate(
    taskId: string,
    toolInput: Record<string, unknown>
  ): Promise<ToolUseResponse> {
    // Check if task exists
    const existingTask = await this._taskManager.getTask(taskId);
    if (!existingTask) {
      return errorToolResponse(`Task with UUID ${taskId} not found.`);
    }

    // Build updates object
    const updates = this.buildUpdatesObject(toolInput);
    if (!updates.isValid) {
      return errorToolResponse(updates.error!);
    }

    // If no updates provided, return error
    if (Object.keys(updates.data).length === 0) {
      return errorToolResponse("At least one property (state, name, description) must be provided to update.");
    }

    // Update the task
    await this._taskManager.updateTask(taskId, updates.data, TaskUpdatedBy.AGENT);

    // Build response message using utility with actual values
    const updateDetails = Object.entries(updates.data).map(([field, value]) => `${field}: ${value}`);
    return successToolResponse(
      TaskInstructionUtils.formatSingleTaskUpdateResponse(taskId, updateDetails)
    );
  }

  private async handleBatchUpdate(
    tasks: Array<Record<string, unknown>>
  ): Promise<ToolUseResponse> {
    const results: Array<{ taskId: string; success: boolean; error?: string; updates?: Record<string, any> }> = [];

    for (const taskInput of tasks) {
      const taskId = taskInput.task_id as string;
      if (!taskId) {
        results.push({ taskId: "unknown", success: false, error: "task_id is required" });
        continue;
      }

      try {
        // Check if task exists
        const existingTask = await this._taskManager.getTask(taskId);
        if (!existingTask) {
          results.push({ taskId, success: false, error: "Task not found" });
          continue;
        }

        // Build updates object
        const updates = this.buildUpdatesObject(taskInput);
        if (!updates.isValid) {
          results.push({ taskId, success: false, error: updates.error });
          continue;
        }

        // If no updates provided, skip
        if (Object.keys(updates.data).length === 0) {
          results.push({ taskId, success: false, error: "No properties to update" });
          continue;
        }

        // Update the task
        await this._taskManager.updateTask(taskId, updates.data, TaskUpdatedBy.AGENT);
        results.push({
          taskId,
          success: true,
          updates: updates.data
        });
      } catch (error) {
        results.push({
          taskId,
          success: false,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    // Build response message using utility
    const responseMessage = TaskInstructionUtils.formatBatchTaskUpdateResponse(results);
    return successToolResponse(responseMessage);
  }

  private buildUpdatesObject(input: Record<string, unknown>): {
    isValid: boolean;
    data: Partial<{ state: TaskState; name: string; description: string }>;
    error?: string;
  } {
    const updates: Partial<{
      state: TaskState;
      name: string;
      description: string;
    }> = {};

    if (input.state) {
      const stateValue = input.state as string;
      if (Object.values(TaskState).includes(stateValue as TaskState)) {
        updates.state = stateValue as TaskState;
      } else {
        return {
          isValid: false,
          data: {},
          error: `Invalid state: ${stateValue}. Must be one of: NOT_STARTED, IN_PROGRESS, CANCELLED, COMPLETE.`
        };
      }
    }

    if (input.name) {
      updates.name = input.name as string;
    }

    if (input.description) {
      updates.description = input.description as string;
    }

    return { isValid: true, data: updates };
  }
}

/**
 * Tool for reorganizing the current conversation's task list.
 * This tool takes a markdown representation of tasks from the agent,
 * parses it back into a task tree, and applies the changes to the
 * existing task tree. Use this only for major restructuring like
 * reordering tasks, changing hierarchy, or adding many tasks at once.
 * For individual task updates (state, name, description), use update_task tool.
 */
export class ReorganizeTaskListTool extends ToolBase<SidecarToolType.reorganizeTaskList> {
  private readonly _logger = getLogger("ReorganizeTaskListTool");

  constructor(private readonly _taskManager: TaskManager) {
    super(SidecarToolType.reorganizeTaskList, ToolSafety.Safe);
  }

  public description = TaskInstructionUtils.getToolDescriptions().reorganizeTaskList;

  public inputSchemaJson = JSON.stringify({
    type: "object",
    properties: {
      markdown: {
        type: "string",
        description:
          "The markdown representation of the task list to update. Should be in the format specified by the view_tasklist tool. New tasks should have a UUID of 'NEW_UUID'. Must contain exactly one root task with proper hierarchy using dash indentation.",
      },
    },
    required: ["markdown"],
  });

  public checkToolCallSafe(): boolean {
    return true; // This tool is always safe
  }

  public async call(
    toolInput: Record<string, unknown>,
    _chatHistory: Exchange[],
    _abortSignal: AbortSignal,
  ): Promise<ToolUseResponse> {
    try {
      // Get the markdown from the tool input
      const markdown = toolInput.markdown as string;
      if (!markdown) {
        return errorToolResponse("No markdown provided.");
      }

      const rootTaskUuid = this._taskManager.getCurrentRootTaskUuid();
      if (!rootTaskUuid) {
        return errorToolResponse("No root task found.");
      }

      // Get the existing task tree
      const existingRootTask =
        await this._taskManager.getHydratedTask(rootTaskUuid);
      if (!existingRootTask) {
        return errorToolResponse(`Task with UUID ${rootTaskUuid} not found.`);
      }

      // Parse the markdown into a new task tree
      let newRootTask;
      try {
        newRootTask = parseMarkdownToTaskTree(markdown);
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : String(error);

        const helpText = TaskInstructionUtils.getMarkdownParsingHelpText();
        return errorToolResponse(`Failed to parse markdown: ${errorMessage}\n\n${helpText}`);
      }

      // Preserve the root task's UUID
      newRootTask.uuid = rootTaskUuid;

      // Generate diff before making changes
      const diff = diffTaskTrees(existingRootTask, newRootTask);
      const diffMarkdown = taskDiffToMarkdown(diff);

      // Use the TaskManager's updateHydratedTask method to handle all changes at once
      const result = await this._taskManager.updateHydratedTask(
        newRootTask,
        TaskUpdatedBy.AGENT,
      );

      // Extract counts from the result
      const {
        created: createdCount,
        updated: updatedCount,
        deleted: deletedCount,
      } = result;

      // Get the updated task tree to show the new UUIDs
      const updatedRootTask =
        await this._taskManager.getHydratedTask(rootTaskUuid);
      if (!updatedRootTask) {
        return errorToolResponse(`Failed to retrieve updated task tree.`);
      }

      // Use utility to generate markdown for created and updated tasks
      const createdAndUpdatedDiff = {
        created: diff.created,
        updated: diff.updated,
        deleted: [], // Don't show deleted tasks in this section
      };
      const createdAndUpdatedMarkdown = taskDiffToMarkdown(
        createdAndUpdatedDiff,
      );

      // Build response with task update information using utility
      const responseMessage = TaskInstructionUtils.formatBulkUpdateResponse(
        createdCount,
        updatedCount,
        deletedCount,
        diffMarkdown,
        createdAndUpdatedMarkdown
      );

      return successToolResponse(responseMessage);
    } catch (error) {
      this._logger.error("Error in UpdateTasksTool:", error);
      return errorToolResponse(
        `Failed to update task list: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }
}

/**
 * Tool for adding a new task to the task list.
 * This tool allows precise placement of new tasks in the hierarchy.
 *
 * Use this tool for:
 * - Creating new root-level tasks
 * - Adding subtasks to existing tasks
 * - Precise task placement with ordering
 */
export class AddTasksTool extends ToolBase<SidecarToolType.addTasks> {
  private readonly _logger = getLogger("AddTasksTool");

  constructor(private readonly _taskManager: TaskManager) {
    super(SidecarToolType.addTasks, ToolSafety.Safe);
  }

  public description = TaskInstructionUtils.getToolDescriptions().addTasks;

  public inputSchemaJson = JSON.stringify({
    type: "object",
    properties: {
      tasks: {
        type: "array",
        description: "Array of tasks to create. Use this for multiple task creation. Each task should have name and description. Either single task properties or tasks array is required.",
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              description: "The name of the new task.",
            },
            description: {
              type: "string",
              description: "The description of the new task.",
            },
            parent_task_id: {
              type: "string",
              description: "UUID of the parent task if this should be a subtask.",
            },
            after_task_id: {
              type: "string",
              description: "UUID of the task after which this task should be inserted.",
            },
            state: {
              type: "string",
              enum: ["NOT_STARTED", "IN_PROGRESS", "CANCELLED", "COMPLETE"],
              description: "Initial state of the task. Defaults to NOT_STARTED.",
            },
          },
          required: ["name", "description"],
        },
      },
      name: {
        type: "string",
        description: "The name of the new task for single task creation.",
      },
      description: {
        type: "string",
        description: "The description of the new task for single task creation.",
      },
      parent_task_id: {
        type: "string",
        description: "UUID of the parent task if this should be a subtask for single task creation.",
      },
      after_task_id: {
        type: "string",
        description: "UUID of the task after which this task should be inserted for single task creation.",
      },
      state: {
        type: "string",
        enum: ["NOT_STARTED", "IN_PROGRESS", "CANCELLED", "COMPLETE"],
        description: "Initial state of the task for single task creation. Defaults to NOT_STARTED.",
      },
    },
  });

  public checkToolCallSafe(): boolean {
    return true; // This tool is always safe
  }

  public async call(
    toolInput: Record<string, unknown>,
    _chatHistory: Exchange[],
    _abortSignal: AbortSignal,
  ): Promise<ToolUseResponse> {
    try {
      // Check if this is a batch creation (tasks array) or single creation
      const tasks = toolInput.tasks as Array<Record<string, unknown>> | undefined;
      const singleTaskName = toolInput.name as string | undefined;

      if (tasks && tasks.length > 0) {
        // Handle batch creation
        return await this.handleBatchCreation(tasks);
      } else if (singleTaskName) {
        // Handle single creation
        return await this.handleSingleCreation(toolInput);
      } else {
        return errorToolResponse("Either single task properties (name, description) or tasks array is required.");
      }
    } catch (error) {
      this._logger.error("Error in AddTasksTool:", error);
      return errorToolResponse(
        `Failed to add task(s): ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  private async handleSingleCreation(toolInput: Record<string, unknown>): Promise<ToolUseResponse> {
    const name = toolInput.name as string;
    const description = toolInput.description as string;
    const parentTaskId = toolInput.parent_task_id as string | undefined;
    const afterTaskId = toolInput.after_task_id as string | undefined;
    const state = (toolInput.state as TaskState) || TaskState.NOT_STARTED;

    if (!name || !description) {
      return errorToolResponse("Both name and description are required.");
    }

    // Validate state if provided
    if (toolInput.state && !Object.values(TaskState).includes(state)) {
      return errorToolResponse(
        `Invalid state: ${toolInput.state}. Must be one of: NOT_STARTED, IN_PROGRESS, CANCELLED, COMPLETE.`
      );
    }

    // If no parent specified, use current root task as parent
    let effectiveParentTaskId = parentTaskId;
    if (!effectiveParentTaskId) {
      effectiveParentTaskId = this._taskManager.getCurrentRootTaskUuid();
      if (!effectiveParentTaskId) {
        return errorToolResponse("No root task found and no parent task specified.");
      }
    }

    // Validate parent task exists
    const parentTask = await this._taskManager.getTask(effectiveParentTaskId);
    if (!parentTask) {
      return errorToolResponse(`Parent task with UUID ${effectiveParentTaskId} not found.`);
    }

    // Create the task
    const newTaskId = await this._taskManager.createTask(name, description, effectiveParentTaskId);

      // Update the state if it's not the default
      if (state !== TaskState.NOT_STARTED) {
        await this._taskManager.updateTask(newTaskId, { state }, TaskUpdatedBy.AGENT);
      }

      // Handle ordering if after_task_id is provided
      if (afterTaskId && effectiveParentTaskId) {
        const parentTaskForOrdering = await this._taskManager.getTask(effectiveParentTaskId);
        if (parentTaskForOrdering) {
          const afterIndex = parentTaskForOrdering.subTasks.indexOf(afterTaskId);
          if (afterIndex !== -1) {
            // Remove the new task from its current position
            const updatedSubTasks = parentTaskForOrdering.subTasks.filter(id => id !== newTaskId);
            // Insert it after the specified task
            updatedSubTasks.splice(afterIndex + 1, 0, newTaskId);

            await this._taskManager.updateTask(
              effectiveParentTaskId,
              { subTasks: updatedSubTasks },
              TaskUpdatedBy.AGENT
            );
          }
        }
      }

    return successToolResponse(
      TaskInstructionUtils.formatTaskCreationResponse(newTaskId, name)
    );
  }

  private async handleBatchCreation(
    tasks: Array<Record<string, unknown>>
  ): Promise<ToolUseResponse> {
    const results: Array<{ taskId?: string; taskName?: string; success: boolean; error?: string }> = [];

    for (const taskInput of tasks) {
      const name = taskInput.name as string;
      const description = taskInput.description as string;

      if (!name || !description) {
        results.push({ success: false, error: `Task missing name or description` });
        continue;
      }

      try {
        const parentTaskId = taskInput.parent_task_id as string | undefined;
        const afterTaskId = taskInput.after_task_id as string | undefined;
        const state = (taskInput.state as TaskState) || TaskState.NOT_STARTED;

        // Validate state if provided
        if (taskInput.state && !Object.values(TaskState).includes(state)) {
          results.push({ taskName: name, success: false, error: `Invalid state: ${taskInput.state}` });
          continue;
        }

        // If no parent specified, use current root task as parent
        let effectiveParentTaskId = parentTaskId;
        if (!effectiveParentTaskId) {
          effectiveParentTaskId = this._taskManager.getCurrentRootTaskUuid();
          if (!effectiveParentTaskId) {
            results.push({ taskName: name, success: false, error: "No root task found and no parent task specified" });
            continue;
          }
        }

        // Validate parent task exists
        const parentTask = await this._taskManager.getTask(effectiveParentTaskId);
        if (!parentTask) {
          results.push({ taskName: name, success: false, error: `Parent task ${effectiveParentTaskId} not found` });
          continue;
        }

        // Create the task
        const newTaskId = await this._taskManager.createTask(name, description, effectiveParentTaskId);

        // Update the state if it's not the default
        if (state !== TaskState.NOT_STARTED) {
          await this._taskManager.updateTask(newTaskId, { state }, TaskUpdatedBy.AGENT);
        }

        // Handle ordering if after_task_id is provided
        if (afterTaskId && effectiveParentTaskId) {
          const parentTaskForOrdering = await this._taskManager.getTask(effectiveParentTaskId);
          if (parentTaskForOrdering) {
            const afterIndex = parentTaskForOrdering.subTasks.indexOf(afterTaskId);
            if (afterIndex !== -1) {
              // Remove the new task from its current position
              const updatedSubTasks = parentTaskForOrdering.subTasks.filter(id => id !== newTaskId);
              // Insert it after the specified task
              updatedSubTasks.splice(afterIndex + 1, 0, newTaskId);

              await this._taskManager.updateTask(
                effectiveParentTaskId,
                { subTasks: updatedSubTasks },
                TaskUpdatedBy.AGENT
              );
            }
          }
        }

        results.push({
          taskId: newTaskId,
          taskName: name,
          success: true
        });
      } catch (error) {
        results.push({
          taskName: name,
          success: false,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    // Build response message using utility
    const responseMessage = TaskInstructionUtils.formatBatchTaskCreationResponse(results);
    return successToolResponse(responseMessage);
  }
}


