import type {
  ChatAgentFileChanges,
  EditEventSource,
} from "../../chat/chat-types";
import type {
  DiffViewDocument,
  isUntitledFileFn,
} from "../../diff-view/document";
import { TextDocumentChangeEvent } from "../agent-edit-types";
import { AugmentLogger } from "../../logging";

/**
 * Information about an aggregate checkpoint
 */
export interface AggregateCheckpointInfo {
  fromTimestamp: number;
  toTimestamp: number;
  conversationId: string;
  files: {
    /* The summary information about the changes that occurred */
    changesSummary: ChatAgentFileChanges;
    /* A document representing the changes that occurred between the previous and current checkpoint */
    changeDocument: DiffViewDocument;
  }[];
}

/**
 * Options for retrieving checkpoints
 */
export interface GetAggregateCheckpointsOptions {
  /**
   * Optional minimum timestamp (inclusive)
   */
  minTimestamp?: number;

  /**
   * Optional maximum timestamp (exclusive)
   */
  maxTimestamp?: number;
}

/**
 * Options for configuring the CheckpointManager
 */
export interface CheckpointManagerOptions {
  /**
   * Optional logger for the CheckpointManager
   */
  logger?: AugmentLogger;

  /**
   * Event handler for document change events
   */
  documentChangeHandler?: (event: TextDocumentChangeEvent) => void;

  /**
   * Used to determine if a file is untitled
   */
  isUntitled?: isUntitledFileFn;
}

export interface UpdateLatestCheckpointOptions {
  /**
   * Whether to save the changes to workspace
   */
  saveToWorkspace: boolean;

  /**
   * Optional source of the edit (defaults to UNSPECIFIED)
   */
  updateSource?: EditEventSource;
}
