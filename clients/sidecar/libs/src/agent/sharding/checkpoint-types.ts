/**
 * Type definitions for the checkpoint hydration/dehydration system.
 *
 * This file defines the three checkpoint formats used in the sharding system and
 * provides type guards for format detection. The three formats work together in
 * a well-defined flow:
 *
 * ## Checkpoint Type Relationships
 *
 * 1. CheckpointBase: Common properties shared by all checkpoint types
 *    - sourceToolCallRequestId: Unique identifier for the tool call that created the checkpoint
 *    - timestamp: When the checkpoint was created
 *    - conversationId: Which conversation the checkpoint belongs to
 *
 * 2. HydratedCheckpoint: In-memory format
 *    - Extends CheckpointBase
 *    - Contains full DiffViewDocument with original and modified code
 *    - Used for all in-memory operations (reading, modifying)
 *    - Dirty state is tracked internally by ShardData
 *
 * 3. SerializedCheckpoint: Legacy storage format
 *    - Extends CheckpointBase
 *    - Contains embedded document with original and modified code
 *    - Less efficient for large documents
 *    - Maintained for backwards compatibility
 *
 * 4. DehydratedCheckpoint: Current storage format
 *    - Extends CheckpointBase
 *    - Contains only document metadata (path information)
 *    - Document content stored separately in plugin file store
 *    - More efficient for storage and memory usage
 *
 * ## Type Conversion Flow
 *
 * - In-Memory → Storage: HydratedCheckpoint → DehydratedCheckpoint (via dehydrateCheckpoint)
 * - Storage → In-Memory: SerializedCheckpoint/DehydratedCheckpoint → HydratedCheckpoint (via hydrateCheckpoint)
 * - Format Detection: Using isSerializedCheckpoint, isDehydratedCheckpoint, isHydratedCheckpoint type guards
 */
import { DiffViewDocument } from "../../diff-view/document";
import { SerializedDocument, DocumentMetadata } from "./types";
import { EditEventSource } from "../../chat/chat-types";

/**
 * Base interface with common properties for all checkpoint types.
 */
export interface CheckpointBase {
  /** The ID of the tool call request that created this checkpoint */
  sourceToolCallRequestId: string;
  /** The timestamp when the checkpoint was created */
  timestamp: number;
  /** The conversation ID this checkpoint belongs to */
  conversationId: string;
  /** The source of the edit (agent, user, etc.) */
  editSource?: EditEventSource;
  /** The ID of the request where this checkpoint was last included (for user-triggered edits) */
  lastIncludedInRequestId?: string;
}

/**
 * In-memory representation of a checkpoint with full document content.
 * Used for all in-memory operations.
 */
export interface HydratedCheckpoint extends CheckpointBase {
  /** The document at the time of the checkpoint */
  document: DiffViewDocument;
}

/**
 * Serialized representation of a checkpoint with document content.
 * This is the legacy format used before the hydration/dehydration system.
 */
export interface SerializedCheckpoint extends CheckpointBase {
  /** The serialized document */
  document: SerializedDocument;
}

/**
 * Serialized representation of a checkpoint with document metadata.
 * This is the new format used with the hydration/dehydration system.
 */
export interface DehydratedCheckpoint extends CheckpointBase {
  /** Metadata about the document */
  documentMetadata: DocumentMetadata;
}

/**
 * Represents a serialized checkpoint in a shard.
 * Can be either a SerializedCheckpoint or a DehydratedCheckpoint.
 */
export type SerializedCheckpointInShard =
  | SerializedCheckpoint
  | DehydratedCheckpoint;

/**
 * Type guard to check if a value is a SerializedCheckpoint.
 */
export function isSerializedCheckpoint(
  value: unknown,
): value is SerializedCheckpoint {
  return (
    typeof value === "object" &&
    value !== null &&
    "document" in value &&
    typeof value.document === "object" &&
    value.document !== null &&
    "path" in value.document &&
    !("checkpointId" in value)
  );
}

/**
 * Type guard to check if a value is a DehydratedCheckpoint.
 */
export function isDehydratedCheckpoint(
  value: unknown,
): value is DehydratedCheckpoint {
  return (
    typeof value === "object" &&
    value !== null &&
    "documentMetadata" in value &&
    typeof value.documentMetadata === "object" &&
    value.documentMetadata !== null &&
    "path" in value.documentMetadata &&
    !("document" in value)
  );
}

/**
 * Type guard to check if a value is a HydratedCheckpoint.
 */
export function isHydratedCheckpoint(
  value: unknown,
): value is HydratedCheckpoint {
  return (
    typeof value === "object" &&
    value !== null &&
    "document" in value &&
    "sourceToolCallRequestId" in value &&
    "timestamp" in value &&
    "conversationId" in value
  );
}
