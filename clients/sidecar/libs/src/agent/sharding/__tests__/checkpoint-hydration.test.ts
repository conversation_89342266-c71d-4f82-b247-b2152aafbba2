import {
  hydrate<PERSON>heckpoint,
  dehydrate<PERSON>heckpoint,
  getCheckpointStoragePath,
  createDocumentMetadata,
  deleteCheckpointDocument,
  clearDocumentCache,
} from "../checkpoint-hydration";
import { HydratedCheckpoint } from "../checkpoint-types";
import {
  SerializedCheckpoint,
  DehydratedCheckpoint,
} from "../checkpoint-types";
import { DiffViewDocument } from "../../../diff-view/document";
import { QualifiedPathName } from "../../../workspace/qualified-path-name";
import { getPluginFileStore } from "../../../client-interfaces/plugin-file-store";
import { SerializedDocument } from "../types";
// DocumentMetadata is used indirectly through createDocumentMetadata

// Mock the plugin file store
jest.mock("../../../client-interfaces/plugin-file-store", () => ({
  getPluginFileStore: jest.fn(),
}));

// Mock the logger
jest.mock("../../../logging", () => ({
  getLogger: jest.fn(() => ({
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  })),
}));

// Mock the LRU cache
jest.mock("lru-cache", () => {
  const mockCache = new Map<string, unknown>();
  return {
    LRUCache: jest.fn().mockImplementation(() => ({
      has: (key: string) => mockCache.has(key),
      get: <T>(key: string): T | undefined =>
        mockCache.get(key) as T | undefined,
      set: <T>(key: string, value: T) => mockCache.set(key, value),
      delete: (key: string) => mockCache.delete(key),
      clear: () => mockCache.clear(),
    })),
  };
});

describe("Checkpoint Hydration/Dehydration", () => {
  const testPath = QualifiedPathName.from({
    rootPath: "/test",
    relPath: "file.ts",
  });

  const mockPluginFileStore = {
    saveAsset: jest.fn(),
    loadAsset: jest.fn(),
    deleteAsset: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (getPluginFileStore as jest.Mock).mockReturnValue(mockPluginFileStore);
    clearDocumentCache();

    // Reset mocks for each test
    mockPluginFileStore.loadAsset.mockReset();
    mockPluginFileStore.saveAsset.mockReset();
    mockPluginFileStore.deleteAsset.mockReset();
  });

  describe("createDocumentMetadata", () => {
    it("should create document metadata from a path", () => {
      const metadata = createDocumentMetadata(testPath);

      expect(metadata).toEqual({
        path: {
          rootPath: testPath.rootPath,
          relPath: testPath.relPath,
        },
      });
    });
  });

  describe("getCheckpointStoragePath", () => {
    it("should generate a consistent storage path", () => {
      const checkpoint: DehydratedCheckpoint = {
        sourceToolCallRequestId: "test-request-id",
        timestamp: 123456789,
        conversationId: "test-conversation",
        documentMetadata: createDocumentMetadata(testPath),
      };

      const path = getCheckpointStoragePath(checkpoint);

      expect(path).toContain("checkpoint-documents");
      expect(path).toContain(checkpoint.conversationId);
      expect(path).toContain(checkpoint.sourceToolCallRequestId);
      expect(path).toContain(checkpoint.timestamp.toString());
      expect(path).toMatch(/\.json$/);
    });
  });

  describe("hydrateCheckpoint", () => {
    it("should hydrate a dehydrated checkpoint", async () => {
      // We'll test with explicit content

      // Create a serialized document with explicit content
      const serializedDocument: SerializedDocument = {
        path: {
          rootPath: "/test",
          relPath: "file.ts",
        },
        originalCode: "original code",
        modifiedCode: "modified code",
      };

      // Create a dehydrated checkpoint
      const dehydratedCheckpoint: DehydratedCheckpoint = {
        sourceToolCallRequestId: "test-request-id",
        timestamp: 123456789,
        conversationId: "test-conversation",
        documentMetadata: createDocumentMetadata(testPath),
      };

      // Mock the loadAsset response with the serialized document
      mockPluginFileStore.loadAsset.mockResolvedValue(
        Buffer.from(JSON.stringify(serializedDocument), "utf8"),
      );

      // Hydrate the checkpoint
      const hydratedCheckpoint = await hydrateCheckpoint(
        dehydratedCheckpoint,
        testPath,
      );

      // Verify the checkpoint metadata
      expect(hydratedCheckpoint.sourceToolCallRequestId).toBe(
        dehydratedCheckpoint.sourceToolCallRequestId,
      );
      expect(hydratedCheckpoint.timestamp).toBe(dehydratedCheckpoint.timestamp);
      expect(hydratedCheckpoint.conversationId).toBe(
        dehydratedCheckpoint.conversationId,
      );

      // Verify the document content
      expect(hydratedCheckpoint.document.originalCode).toBe("original code");
      expect(hydratedCheckpoint.document.modifiedCode).toBe("modified code");

      // Verify the document path
      expect(hydratedCheckpoint.document.filePath.rootPath).toBe("/test");
      expect(hydratedCheckpoint.document.filePath.relPath).toBe("file.ts");

      // Verify the storage path was constructed correctly
      const storagePath = getCheckpointStoragePath(dehydratedCheckpoint);
      expect(mockPluginFileStore.loadAsset).toHaveBeenCalledWith(storagePath);
    });

    it("should handle missing document gracefully", async () => {
      const dehydratedCheckpoint: DehydratedCheckpoint = {
        sourceToolCallRequestId: "test-request-id",
        timestamp: 123456789,
        conversationId: "test-conversation",
        documentMetadata: createDocumentMetadata(testPath),
      };

      // Mock the loadAsset response for a missing document
      mockPluginFileStore.loadAsset.mockResolvedValue(null);

      const hydratedCheckpoint = await hydrateCheckpoint(
        dehydratedCheckpoint,
        testPath,
      );

      // Should create an empty document
      expect(hydratedCheckpoint.document).toBeDefined();
    });

    it("should use cache for subsequent hydrations", async () => {
      // Create a serialized document with explicit content
      const serializedDocument: SerializedDocument = {
        path: {
          rootPath: "/test",
          relPath: "file.ts",
        },
        originalCode: "original code",
        modifiedCode: "modified code",
      };

      // Create a dehydrated checkpoint with a unique ID for this test
      const dehydratedCheckpoint: DehydratedCheckpoint = {
        sourceToolCallRequestId: "cache-test-id",
        timestamp: 123456789,
        conversationId: "test-conversation",
        documentMetadata: createDocumentMetadata(testPath),
      };

      // Mock the loadAsset response with the serialized document
      mockPluginFileStore.loadAsset.mockResolvedValue(
        Buffer.from(JSON.stringify(serializedDocument), "utf8"),
      );

      // First call should load from storage
      await hydrateCheckpoint(dehydratedCheckpoint, testPath);
      expect(mockPluginFileStore.loadAsset).toHaveBeenCalledTimes(1);

      // Clear the mock to verify it's not called again
      mockPluginFileStore.loadAsset.mockClear();

      // Second call should use the cache
      await hydrateCheckpoint(dehydratedCheckpoint, testPath);

      // Verify the document was retrieved from cache
      expect(mockPluginFileStore.loadAsset).not.toHaveBeenCalled();
    });

    it("should hydrate a serialized checkpoint", async () => {
      const serializedCheckpoint: SerializedCheckpoint = {
        sourceToolCallRequestId: "test-request-id",
        timestamp: 123456789,
        conversationId: "test-conversation",
        document: {
          path: {
            rootPath: "/test",
            relPath: "file.ts",
          },
          originalCode: "original code",
          modifiedCode: "modified code",
        },
      };

      const hydratedCheckpoint = await hydrateCheckpoint(
        serializedCheckpoint,
        testPath,
      );

      expect(hydratedCheckpoint.sourceToolCallRequestId).toBe(
        serializedCheckpoint.sourceToolCallRequestId,
      );
      expect(hydratedCheckpoint.timestamp).toBe(serializedCheckpoint.timestamp);
      expect(hydratedCheckpoint.conversationId).toBe(
        serializedCheckpoint.conversationId,
      );
      expect(hydratedCheckpoint.document.originalCode).toBe(
        serializedCheckpoint.document.originalCode,
      );
      expect(hydratedCheckpoint.document.modifiedCode).toBe(
        serializedCheckpoint.document.modifiedCode,
      );
    });

    it("should handle invalid checkpoint format gracefully", async () => {
      const invalidCheckpoint = {
        sourceToolCallRequestId: "test-request-id",
        timestamp: 123456789,
        conversationId: "test-conversation",
      };

      // Our implementation now handles invalid checkpoints gracefully by creating an empty document
      const result = await hydrateCheckpoint(
        invalidCheckpoint as unknown as SerializedCheckpoint,
        testPath,
      );

      // Should create an empty document
      expect(result.document).toBeDefined();
    });
  });

  describe("dehydrateCheckpoint", () => {
    it("should dehydrate a dirty hydrated checkpoint", async () => {
      const document = new DiffViewDocument(
        testPath,
        "original code",
        "modified code",
        {},
      );
      const hydratedCheckpoint: HydratedCheckpoint = {
        sourceToolCallRequestId: "test-request-id",
        timestamp: 123456789,
        conversationId: "test-conversation",
        document,
      };

      // Call with isDirty=true

      const dehydratedCheckpoint = await dehydrateCheckpoint(
        hydratedCheckpoint,
        true,
      );

      expect(dehydratedCheckpoint.sourceToolCallRequestId).toBe(
        hydratedCheckpoint.sourceToolCallRequestId,
      );
      expect(dehydratedCheckpoint.timestamp).toBe(hydratedCheckpoint.timestamp);
      expect(dehydratedCheckpoint.conversationId).toBe(
        hydratedCheckpoint.conversationId,
      );
      expect(dehydratedCheckpoint.documentMetadata).toBeDefined();
      expect(mockPluginFileStore.saveAsset).toHaveBeenCalled();
    });

    it("should not store document if not dirty", async () => {
      const document = new DiffViewDocument(
        testPath,
        "original code",
        "modified code",
        {},
      );
      const hydratedCheckpoint: HydratedCheckpoint = {
        sourceToolCallRequestId: "test-request-id",
        timestamp: 123456789,
        conversationId: "test-conversation",
        document,
      };

      // Call with isDirty=false

      const dehydratedCheckpoint = await dehydrateCheckpoint(
        hydratedCheckpoint,
        false,
      );

      expect(dehydratedCheckpoint.sourceToolCallRequestId).toBe(
        hydratedCheckpoint.sourceToolCallRequestId,
      );
      expect(dehydratedCheckpoint.timestamp).toBe(hydratedCheckpoint.timestamp);
      expect(dehydratedCheckpoint.conversationId).toBe(
        hydratedCheckpoint.conversationId,
      );
      expect(dehydratedCheckpoint.documentMetadata).toBeDefined();
      expect(mockPluginFileStore.saveAsset).not.toHaveBeenCalled();
    });
  });

  describe("deleteCheckpointDocument", () => {
    it("should delete a document from the plugin file store", async () => {
      const dehydratedCheckpoint: DehydratedCheckpoint = {
        sourceToolCallRequestId: "test-request-id",
        timestamp: 123456789,
        conversationId: "test-conversation",
        documentMetadata: createDocumentMetadata(testPath),
      };

      await deleteCheckpointDocument(dehydratedCheckpoint);

      const storagePath = getCheckpointStoragePath(dehydratedCheckpoint);
      expect(mockPluginFileStore.deleteAsset).toHaveBeenCalledWith(storagePath);
    });
  });

  describe("clearDocumentCache", () => {
    it("should clear the document cache", async () => {
      const document = new DiffViewDocument(
        testPath,
        "original code",
        "modified code",
        {},
      );
      // Using a direct approach to avoid ESLint errors
      const serializedDocument: SerializedDocument = {
        path: {
          rootPath: document.filePath.rootPath,
          relPath: document.filePath.relPath,
        },
        originalCode: document.originalCode,
        modifiedCode: document.modifiedCode,
      };
      const dehydratedCheckpoint: DehydratedCheckpoint = {
        sourceToolCallRequestId: "test-request-id",
        timestamp: 123456789,
        conversationId: "test-conversation",
        documentMetadata: createDocumentMetadata(testPath),
      };

      // Mock the loadAsset response
      mockPluginFileStore.loadAsset.mockResolvedValue(
        Buffer.from(JSON.stringify(serializedDocument), "utf8"),
      );

      // First call should load from storage
      await hydrateCheckpoint(dehydratedCheckpoint, testPath);
      expect(mockPluginFileStore.loadAsset).toHaveBeenCalledTimes(1);

      // Reset the mock to verify it's called again after cache clear
      mockPluginFileStore.loadAsset.mockClear();

      // Clear the cache
      clearDocumentCache();

      // Second call should load from storage again
      await hydrateCheckpoint(dehydratedCheckpoint, testPath);
      expect(mockPluginFileStore.loadAsset).toHaveBeenCalledTimes(1);
    });
  });
});
