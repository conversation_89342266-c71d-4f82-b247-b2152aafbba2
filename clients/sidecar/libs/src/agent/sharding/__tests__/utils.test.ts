import { QualifiedPathName } from "../../../workspace/qualified-path-name";
import {
  validateShardId,
  validateManifest,
  validateShard,
  filterCheckpoints,
} from "../utils";
import type {
  ShardManifest,
  SerializedShard,
  ShardId,
  ShardMetadata,
  SerializedDocument,
} from "../types";
import type { DocumentCheckpoint } from "../../agent-edit-types";
import { DiffViewDocument } from "../../../diff-view/document";
import type { SerializedCheckpointInShard } from "../checkpoint-types";

describe("Sharding Utilities", () => {
  const testPath = QualifiedPathName.from({
    rootPath: "/test",
    relPath: "file.ts",
  });

  const createMockCheckpoint = (id: string): SerializedCheckpointInShard => {
    // Create the checkpoint data
    const checkpointData = {
      sourceToolCallRequestId: id,
      timestamp: Date.now(),
      document: new DiffViewDocument(testPath, "original", "modified", {}),
      conversationId: "test-conversation",
    };
    // Create a serialized checkpoint directly
    return {
      sourceToolCallRequestId: checkpointData.sourceToolCallRequestId,
      timestamp: checkpointData.timestamp,
      conversationId: checkpointData.conversationId,
      document: {
        path: {
          rootPath: checkpointData.document.filePath.rootPath,
          relPath: checkpointData.document.filePath.relPath,
        },
        originalCode: checkpointData.document.originalCode,
        modifiedCode: checkpointData.document.modifiedCode,
      },
    };
  };

  describe("validation", () => {
    it("should validate shard IDs", () => {
      expect(validateShardId("test-shard")).toEqual({ isValid: true });
      expect(validateShardId("")).toEqual({
        isValid: false,
        reason: "Shard ID cannot be empty",
      });
      expect(validateShardId(123 as unknown as ShardId)).toEqual({
        isValid: false,
        reason: "Shard ID must be a string",
      });
    });

    it("should validate manifests with multiple checkpoints", () => {
      const validManifest: ShardManifest = {
        version: 1,
        lastUpdated: Date.now(),
        shards: {
          "test-shard-1": {
            checkpointDocumentIds: [
              "test-conversation-1:file.ts",
              "test-conversation-2:file.ts",
            ],
            size: 2000,
            checkpointCount: 3, // Multiple checkpoints
            lastModified: Date.now(),
          },
          "test-shard-2": {
            checkpointDocumentIds: [], // Empty shard
            size: 0,
            checkpointCount: 0,
            lastModified: Date.now(),
          },
        },
      };

      expect(validateManifest(validManifest)).toEqual({ isValid: true });

      const invalidCheckpointDocumentId = {
        ...validManifest,
        shards: {
          "test-shard": {
            checkpointDocumentIds: [""], // Invalid checkpoint ID
            size: 1000,
            checkpointCount: 1,
            lastModified: Date.now(),
          },
        },
      };

      expect(validateManifest(invalidCheckpointDocumentId)).toEqual({
        isValid: false,
        reason: "Invalid checkpoint document ID in shard test-shard: ",
      });

      const invalidManifest = {
        ...validManifest,
        shards: {
          "": {
            // Invalid shard ID
            checkpointDocumentIds: ["test-conversation:file.ts"],
            size: 1000,
            checkpointCount: 1,
            lastModified: Date.now(),
          },
        },
      };

      expect(validateManifest(invalidManifest)).toEqual({
        isValid: false,
        reason: "Shard ID cannot be empty",
      });

      const invalidManifestVersion = {
        ...validManifest,
        version: "1" as unknown as number,
      };
      expect(validateManifest(invalidManifestVersion)).toEqual({
        isValid: false,
        reason: "Invalid manifest version or lastUpdated timestamp",
      });

      const invalidManifestLastUpdated = {
        ...validManifest,
        lastUpdated: "now" as unknown as number,
      };
      expect(validateManifest(invalidManifestLastUpdated)).toEqual({
        isValid: false,
        reason: "Invalid manifest version or lastUpdated timestamp",
      });

      const invalidMetadataNumbers = {
        ...validManifest,
        shards: {
          "test-shard": {
            checkpointDocumentIds: ["test-conversation:file.ts"],
            size: "1000" as unknown as number,
            checkpointCount: "1" as unknown as number,
            lastModified: "now" as unknown as number,
          },
        },
      };
      expect(validateManifest(invalidMetadataNumbers)).toEqual({
        isValid: false,
        reason: "Invalid size field for shard test-shard",
      });
    });

    it("should validate shards with multiple checkpoints", () => {
      const validShard: SerializedShard = {
        id: "test-shard",
        checkpoints: {
          "test-conversation-1:file.ts": [
            createMockCheckpoint("test1"),
            createMockCheckpoint("test2"),
            createMockCheckpoint("test3"),
          ],
          "test-conversation-2:file.ts": [createMockCheckpoint("test4")],
        },
        metadata: {
          checkpointDocumentIds: [
            "test-conversation-1:file.ts",
            "test-conversation-2:file.ts",
          ],
          size: 2000,
          checkpointCount: 4,
          lastModified: Date.now(),
        },
      };

      expect(validateShard(validShard)).toEqual({ isValid: true });

      const emptyCheckpoints: SerializedShard = {
        ...validShard,
        checkpoints: {},
        metadata: {
          checkpointDocumentIds: [],
          size: 0,
          checkpointCount: 0,
          lastModified: Date.now(),
        },
      };

      expect(validateShard(emptyCheckpoints)).toEqual({ isValid: true });

      const invalidCheckpointArray = {
        ...validShard,
        checkpoints: {
          "test-conversation:file.ts": null, // Invalid checkpoint array
        },
      };

      expect(
        validateShard(invalidCheckpointArray as unknown as SerializedShard),
      ).toEqual({
        isValid: false,
        reason: "Checkpoints for ID test-conversation:file.ts must be an array",
      });

      const invalidCheckpoint = {
        ...validShard,
        checkpoints: {
          "test-conversation:file.ts": [
            { invalid: "checkpoint" }, // Invalid checkpoint structure
          ],
        },
      };

      expect(
        validateShard(invalidCheckpoint as unknown as SerializedShard),
      ).toEqual({
        isValid: false,
        reason:
          "Invalid checkpoint common properties in ID test-conversation:file.ts",
      });

      const mismatchedMetadata = {
        ...validShard,
        metadata: {
          ...validShard.metadata,
          checkpointDocumentIds: ["nonexistent:file.ts"], // ID doesn't match checkpoints
        },
      };

      expect(validateShard(mismatchedMetadata)).toEqual({
        isValid: false,
        reason:
          "Mismatch between checkpoint IDs in metadata and actual checkpoints",
      });
    });

    it("should validate shard IDs with edge cases", () => {
      // Valid IDs
      expect(validateShardId("test-shard")).toEqual({ isValid: true });
      expect(validateShardId("a")).toEqual({ isValid: true });
      expect(validateShardId("123")).toEqual({ isValid: true });
      expect(validateShardId("!@#$%")).toEqual({ isValid: true });

      // Invalid IDs
      expect(validateShardId("")).toEqual({
        isValid: false,
        reason: "Shard ID cannot be empty",
      });
      expect(validateShardId(null as unknown as string)).toEqual({
        isValid: false,
        reason: "Shard ID must be a string",
      });
      expect(validateShardId(undefined as unknown as string)).toEqual({
        isValid: false,
        reason: "Shard ID must be a string",
      });
      expect(validateShardId({} as unknown as string)).toEqual({
        isValid: false,
        reason: "Shard ID must be a string",
      });
      expect(validateShardId(123 as unknown as string)).toEqual({
        isValid: false,
        reason: "Shard ID must be a string",
      });
    });

    it("should validate manifests with edge cases", () => {
      // Test version field
      expect(
        validateManifest({
          version: null as unknown as number,
          lastUpdated: Date.now(),
          shards: {},
        } as ShardManifest),
      ).toEqual({
        isValid: false,
        reason: "Invalid manifest version or lastUpdated timestamp",
      });

      expect(
        validateManifest({
          version: undefined as unknown as number,
          lastUpdated: Date.now(),
          shards: {},
        } as ShardManifest),
      ).toEqual({
        isValid: false,
        reason: "Invalid manifest version or lastUpdated timestamp",
      });

      expect(
        validateManifest({
          version: "1" as unknown as number,
          lastUpdated: Date.now(),
          shards: {},
        } as ShardManifest),
      ).toEqual({
        isValid: false,
        reason: "Invalid manifest version or lastUpdated timestamp",
      });

      // Test lastUpdated field
      expect(
        validateManifest({
          version: 1,
          lastUpdated: null as unknown as number,
          shards: {},
        } as ShardManifest),
      ).toEqual({
        isValid: false,
        reason: "Invalid manifest version or lastUpdated timestamp",
      });

      expect(
        validateManifest({
          version: 1,
          lastUpdated: "now" as unknown as number,
          shards: {},
        } as ShardManifest),
      ).toEqual({
        isValid: false,
        reason: "Invalid manifest version or lastUpdated timestamp",
      });

      // Test shards field
      expect(
        validateManifest({
          version: 1,
          lastUpdated: Date.now(),
          shards: null as unknown as Record<string, ShardMetadata>,
        } as ShardManifest),
      ).toEqual({
        isValid: false,
        reason: "Missing or invalid shards object",
      });

      expect(
        validateManifest({
          version: 1,
          lastUpdated: Date.now(),
          shards: undefined as unknown as Record<string, ShardMetadata>,
        } as ShardManifest),
      ).toEqual({
        isValid: false,
        reason: "Missing or invalid shards object",
      });

      // Test metadata fields
      const validMetadata: ShardMetadata = {
        checkpointDocumentIds: ["test-conversation:file.ts"],
        size: 1000,
        checkpointCount: 1,
        lastModified: Date.now(),
      };

      const testCases: Array<[string, Partial<ShardMetadata>, string]> = [
        [
          "invalid size",
          { ...validMetadata, size: null as unknown as number },
          "Invalid size field for shard test-shard",
        ],
        [
          "invalid size type",
          { ...validMetadata, size: "1000" as unknown as number },
          "Invalid size field for shard test-shard",
        ],
        [
          "invalid count",
          { ...validMetadata, checkpointCount: null as unknown as number },
          "Invalid checkpointCount field for shard test-shard",
        ],
        [
          "invalid count type",
          { ...validMetadata, checkpointCount: "1" as unknown as number },
          "Invalid checkpointCount field for shard test-shard",
        ],
        [
          "invalid timestamp",
          { ...validMetadata, lastModified: null as unknown as number },
          "Invalid lastModified field for shard test-shard",
        ],
        [
          "invalid timestamp type",
          { ...validMetadata, lastModified: "now" as unknown as number },
          "Invalid lastModified field for shard test-shard",
        ],
        [
          "invalid checkpoint IDs",
          {
            ...validMetadata,
            checkpointDocumentIds: null as unknown as string[],
          },
          "Invalid checkpointDocumentIds array for shard test-shard",
        ],
        [
          "invalid checkpoint IDs type",
          {
            ...validMetadata,
            checkpointDocumentIds: "id" as unknown as string[],
          },
          "Invalid checkpointDocumentIds array for shard test-shard",
        ],
        [
          "invalid checkpoint ID array",
          {
            ...validMetadata,
            checkpointDocumentIds: [null] as unknown as string[],
          },
          "Invalid checkpoint document ID in shard test-shard: null",
        ],
        [
          "invalid checkpoint ID format",
          { ...validMetadata, checkpointDocumentIds: [""] },
          "Invalid checkpoint document ID in shard test-shard: ",
        ],
      ];

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      for (const [_name, metadata, reason] of testCases) {
        expect(
          validateManifest({
            version: 1,
            lastUpdated: Date.now(),
            shards: {
              "test-shard": {
                ...validMetadata,
                ...metadata,
              },
            },
          } as ShardManifest),
        ).toEqual({
          isValid: false,
          reason,
        });
      }
    });

    it("should validate shards with edge cases", () => {
      const validMetadata = {
        checkpointDocumentIds: ["test-conversation:file.ts"],
        size: 0,
        checkpointCount: 0,
        lastModified: Date.now(),
      };

      // Test invalid shard ID
      expect(
        validateShard({
          id: "", // Invalid shard ID
          checkpoints: {},
          metadata: validMetadata,
        } as SerializedShard),
      ).toEqual({
        isValid: false,
        reason: "Shard ID cannot be empty",
      });

      // Test invalid metadata through manifest validation
      expect(
        validateShard({
          id: "test-shard",
          checkpoints: {},
          metadata: {
            ...validMetadata,
            size: "1000" as unknown as number, // Invalid size type
          },
        } as SerializedShard),
      ).toEqual({
        isValid: false,
        reason: "Invalid size field for shard test-shard",
      });

      // Create a properly typed checkpoint directly
      // Avoid unsafe assignments by creating the object with the correct type
      const typedCheckpoint: SerializedCheckpointInShard = {
        sourceToolCallRequestId: "test",
        timestamp: Date.now(),
        conversationId: "test-conversation",
        document: {
          path: {
            rootPath: testPath.rootPath,
            relPath: testPath.relPath,
          },
          originalCode: "original",
          modifiedCode: "modified",
        },
      };

      // Test checkpoints field
      expect(
        validateShard({
          id: "test-shard",
          checkpoints: null as unknown as Record<string, DocumentCheckpoint[]>,
          metadata: validMetadata,
        } as unknown as SerializedShard),
      ).toEqual({
        isValid: false,
        reason: "Missing or invalid checkpoints object",
      });

      expect(
        validateShard({
          id: "test-shard",
          checkpoints: {
            "": [typedCheckpoint], // Invalid checkpoint ID
          },
          metadata: validMetadata,
        } as SerializedShard),
      ).toEqual({
        isValid: false,
        reason: "Invalid checkpoint ID: ",
      });

      // Test checkpoint array
      expect(
        validateShard({
          id: "test-shard",
          checkpoints: {
            "test-conversation:file.ts":
              null as unknown as DocumentCheckpoint[],
          },
          metadata: validMetadata,
        } as unknown as SerializedShard),
      ).toEqual({
        isValid: false,
        reason: "Checkpoints for ID test-conversation:file.ts must be an array",
      });

      // Test checkpoint objects
      const invalidCheckpoints: Array<[unknown, string]> = [
        [null, "Invalid checkpoint structure in ID test-conversation:file.ts"],
        [{}, "Invalid checkpoint structure in ID test-conversation:file.ts"],
        [
          { sourceToolCallRequestId: null },
          "Invalid checkpoint structure in ID test-conversation:file.ts",
        ],
        [
          { sourceToolCallRequestId: 123 },
          "Invalid checkpoint structure in ID test-conversation:file.ts",
        ],
        [
          { sourceToolCallRequestId: "test", timestamp: null },
          "Invalid checkpoint structure in ID test-conversation:file.ts",
        ],
        [
          { sourceToolCallRequestId: "test", timestamp: "now" },
          "Invalid checkpoint structure in ID test-conversation:file.ts",
        ],
        [
          { sourceToolCallRequestId: "test", timestamp: 1000, document: null },
          "Invalid checkpoint structure in ID test-conversation:file.ts",
        ],
        [
          { sourceToolCallRequestId: "test", timestamp: 1000, document: "doc" },
          "Invalid checkpoint structure in ID test-conversation:file.ts",
        ],
      ];

      for (const [checkpoint] of invalidCheckpoints) {
        expect(
          validateShard({
            id: "test-shard",
            checkpoints: {
              "test-conversation:file.ts": [checkpoint as DocumentCheckpoint],
            },
            metadata: validMetadata,
          } as unknown as SerializedShard),
        ).toEqual({
          isValid: false,
          reason:
            "Invalid checkpoint common properties in ID test-conversation:file.ts",
        });
      }

      // Test metadata checkpoint ID mismatch
      expect(
        validateShard({
          id: "test-shard",
          checkpoints: {
            "test-conversation:file.ts": [],
          },
          metadata: {
            ...validMetadata,
            checkpointDocumentIds: ["other-conversation:file.ts"], // Different ID
          },
        } as SerializedShard),
      ).toEqual({
        isValid: false,
        reason:
          "Mismatch between checkpoint IDs in metadata and actual checkpoints",
      });
    });

    it("should validate manifest metadata objects", () => {
      // Test null/undefined metadata
      expect(
        validateManifest({
          version: 1,
          lastUpdated: Date.now(),
          shards: {
            "test-shard": null as unknown as ShardMetadata,
          },
        }),
      ).toEqual({
        isValid: false,
        reason: "Invalid metadata object for shard test-shard",
      });

      expect(
        validateManifest({
          version: 1,
          lastUpdated: Date.now(),
          shards: {
            "test-shard": undefined as unknown as ShardMetadata,
          },
        }),
      ).toEqual({
        isValid: false,
        reason: "Invalid metadata object for shard test-shard",
      });

      // Test non-object metadata
      expect(
        validateManifest({
          version: 1,
          lastUpdated: Date.now(),
          shards: {
            "test-shard": "metadata" as unknown as ShardMetadata,
          },
        }),
      ).toEqual({
        isValid: false,
        reason: "Invalid metadata object for shard test-shard",
      });

      // Test non-array checkpoint IDs
      expect(
        validateManifest({
          version: 1,
          lastUpdated: Date.now(),
          shards: {
            "test-shard": {
              checkpointDocumentIds: {} as unknown as string[],
              size: 0,
              checkpointCount: 0,
              lastModified: Date.now(),
            },
          },
        }),
      ).toEqual({
        isValid: false,
        reason: "Invalid checkpointDocumentIds array for shard test-shard",
      });
    });

    it("should validate checkpoint document fields", () => {
      const validMetadata = {
        checkpointDocumentIds: ["test-conversation:file.ts"],
        size: 0,
        checkpointCount: 1,
        lastModified: Date.now(),
      };

      // Test invalid document field
      expect(
        validateShard({
          id: "test-shard",
          checkpoints: {
            "test-conversation:file.ts": [
              {
                sourceToolCallRequestId: "test",
                timestamp: Date.now(),
                document: null as unknown as SerializedDocument,
                conversationId: "test-conversation",
              },
            ],
          },
          metadata: validMetadata,
        }),
      ).toEqual({
        isValid: false,
        reason: "Unknown checkpoint type in ID test-conversation:file.ts",
      });

      expect(
        validateShard({
          id: "test-shard",
          checkpoints: {
            "test-conversation:file.ts": [
              {
                sourceToolCallRequestId: "test",
                timestamp: Date.now(),
                document: {} as unknown as SerializedDocument,
                conversationId: "test-conversation",
              },
            ],
          },
          metadata: validMetadata,
        }),
      ).toEqual({
        isValid: false,
        reason: "Unknown checkpoint type in ID test-conversation:file.ts",
      });

      expect(
        validateShard({
          id: "test-shard",
          checkpoints: {
            "test-conversation:file.ts": [
              {
                sourceToolCallRequestId: "test",
                timestamp: Date.now(),
                document: "not an object" as unknown as SerializedDocument,
                conversationId: "test-conversation",
              },
            ],
          },
          metadata: validMetadata,
        }),
      ).toEqual({
        isValid: false,
        reason: "Unknown checkpoint type in ID test-conversation:file.ts",
      });
    });
  });

  describe("filterCheckpoints", () => {
    const createTestCheckpoint = (timestamp: number) => ({
      timestamp,
      data: `data-${timestamp}`,
    });

    const checkpoints = [
      createTestCheckpoint(1000),
      createTestCheckpoint(2000),
      createTestCheckpoint(3000),
      createTestCheckpoint(4000),
      createTestCheckpoint(5000),
    ];

    it("should filter by timestamp range", () => {
      // Keep checkpoints between 2000 (inclusive) and 4000 (exclusive)
      const filtered = filterCheckpoints(checkpoints, {
        minTimestamp: 2000,
        maxTimestamp: 4000,
      });

      expect(filtered).toHaveLength(2);
      expect(filtered.map((cp) => cp.timestamp)).toEqual([2000, 3000]);
    });

    it("should filter by index range", () => {
      // Keep checkpoints from index 1 (inclusive) to 3 (exclusive)
      const filtered = filterCheckpoints(checkpoints, {
        minIdx: 1,
        maxIdx: 3,
      });

      expect(filtered).toHaveLength(2);
      expect(filtered.map((cp) => cp.timestamp)).toEqual([2000, 3000]);
    });

    it("should handle combined timestamp and index filters", () => {
      // Keep checkpoints between 2000-4000 AND indices 1-3
      const filtered = filterCheckpoints(checkpoints, {
        minTimestamp: 2000,
        maxTimestamp: 4000,
        minIdx: 1,
        maxIdx: 3,
      });

      expect(filtered).toHaveLength(2);
      expect(filtered.map((cp) => cp.timestamp)).toEqual([2000, 3000]);
    });

    it("should handle removal mode", () => {
      // Remove (instead of keep) checkpoints between 2000-4000
      const filtered = filterCheckpoints(
        checkpoints,
        {
          minTimestamp: 2000,
          maxTimestamp: 4000,
        },
        false,
      );

      expect(filtered).toHaveLength(3);
      expect(filtered.map((cp) => cp.timestamp)).toEqual([1000, 4000, 5000]);
    });

    it("should handle empty ranges", () => {
      // No checkpoints in range
      const filtered = filterCheckpoints(checkpoints, {
        minTimestamp: 6000,
        maxTimestamp: 7000,
      });

      expect(filtered).toHaveLength(0);
    });

    it("should handle undefined bounds", () => {
      // Only min timestamp specified (keep mode)
      const filtered1 = filterCheckpoints(checkpoints, {
        minTimestamp: 3000,
      });
      expect(filtered1.map((cp) => cp.timestamp)).toEqual([3000, 4000, 5000]);

      // Only max timestamp specified (keep mode)
      const filtered2 = filterCheckpoints(checkpoints, {
        maxTimestamp: 3000,
      });
      expect(filtered2.map((cp) => cp.timestamp)).toEqual([1000, 2000]);

      // Only min index specified (keep mode)
      const filtered3 = filterCheckpoints(checkpoints, {
        minIdx: 3,
      });
      expect(filtered3.map((cp) => cp.timestamp)).toEqual([4000, 5000]);

      // Only max index specified (keep mode)
      const filtered4 = filterCheckpoints(checkpoints, {
        maxIdx: 2,
      });
      expect(filtered4.map((cp) => cp.timestamp)).toEqual([1000, 2000]);

      // Only min timestamp specified (remove mode)
      const filtered5 = filterCheckpoints(
        checkpoints,
        {
          minTimestamp: 3000,
        },
        false,
      );
      expect(filtered5.map((cp) => cp.timestamp)).toEqual([1000, 2000]);

      // Only max timestamp specified (remove mode)
      const filtered6 = filterCheckpoints(
        checkpoints,
        {
          maxTimestamp: 3000,
        },
        false,
      );
      expect(filtered6.map((cp) => cp.timestamp)).toEqual([3000, 4000, 5000]);

      // Only min index specified (remove mode)
      const filtered7 = filterCheckpoints(
        checkpoints,
        {
          minIdx: 3,
        },
        false,
      );
      expect(filtered7.map((cp) => cp.timestamp)).toEqual([1000, 2000, 3000]);

      // Only max index specified (remove mode)
      const filtered8 = filterCheckpoints(
        checkpoints,
        {
          maxIdx: 2,
        },
        false,
      );
      expect(filtered8.map((cp) => cp.timestamp)).toEqual([3000, 4000, 5000]);
    });

    it("should handle exact timestamp matches", () => {
      // Keep exactly timestamp 3000
      const filtered1 = filterCheckpoints(checkpoints, {
        minTimestamp: 3000,
        maxTimestamp: 3001,
      });
      expect(filtered1.map((cp) => cp.timestamp)).toEqual([3000]);

      // Remove exactly timestamp 3000
      const filtered2 = filterCheckpoints(
        checkpoints,
        {
          minTimestamp: 3000,
          maxTimestamp: 3001,
        },
        false,
      );
      expect(filtered2.map((cp) => cp.timestamp)).toEqual([
        1000, 2000, 4000, 5000,
      ]);
    });

    it("should handle exact index matches", () => {
      // Keep exactly index 2
      const filtered1 = filterCheckpoints(checkpoints, {
        minIdx: 2,
        maxIdx: 3,
      });
      expect(filtered1.map((cp) => cp.timestamp)).toEqual([3000]);

      // Remove exactly index 2
      const filtered2 = filterCheckpoints(
        checkpoints,
        {
          minIdx: 2,
          maxIdx: 3,
        },
        false,
      );
      expect(filtered2.map((cp) => cp.timestamp)).toEqual([
        1000, 2000, 4000, 5000,
      ]);
    });

    it("should handle combined exact matches", () => {
      // Keep checkpoint at index 2 with timestamp 3000
      const filtered1 = filterCheckpoints(checkpoints, {
        minIdx: 2,
        maxIdx: 3,
        minTimestamp: 3000,
        maxTimestamp: 3001,
      });
      expect(filtered1.map((cp) => cp.timestamp)).toEqual([3000]);

      // Remove checkpoint at index 2 with timestamp 3000
      const filtered2 = filterCheckpoints(
        checkpoints,
        {
          minIdx: 2,
          maxIdx: 3,
          minTimestamp: 3000,
          maxTimestamp: 3001,
        },
        false,
      );
      expect(filtered2.map((cp) => cp.timestamp)).toEqual([
        1000, 2000, 4000, 5000,
      ]);
    });

    it("should handle empty input", () => {
      const filtered = filterCheckpoints([], {
        minTimestamp: 1000,
        maxTimestamp: 2000,
      });

      expect(filtered).toHaveLength(0);
    });

    it("should handle no criteria specified", () => {
      // In keep mode, should keep all checkpoints when no criteria specified
      const filtered1 = filterCheckpoints(checkpoints, {});
      expect(filtered1).toHaveLength(5);
      expect(filtered1.map((cp) => cp.timestamp)).toEqual([
        1000, 2000, 3000, 4000, 5000,
      ]);

      // In remove mode, should remove all checkpoints when no criteria specified
      const filtered2 = filterCheckpoints(checkpoints, {}, false);
      expect(filtered2).toHaveLength(0);
    });

    it("should handle empty criteria object with undefined bounds", () => {
      // Keep mode with all undefined bounds should keep everything
      const filtered1 = filterCheckpoints(checkpoints, {
        minTimestamp: undefined,
        maxTimestamp: undefined,
        minIdx: undefined,
        maxIdx: undefined,
      });
      expect(filtered1).toHaveLength(5);
      expect(filtered1.map((cp) => cp.timestamp)).toEqual([
        1000, 2000, 3000, 4000, 5000,
      ]);

      // Remove mode with all undefined bounds should remove everything
      const filtered2 = filterCheckpoints(
        checkpoints,
        {
          minTimestamp: undefined,
          maxTimestamp: undefined,
          minIdx: undefined,
          maxIdx: undefined,
        },
        false,
      );
      expect(filtered2).toHaveLength(0);
    });
  });
});
