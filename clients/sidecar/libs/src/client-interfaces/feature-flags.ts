import { getLogger } from "../logging";

export interface IClientFeatureFlags {
  flags: SidecarFlags;
}

export type SidecarFlags = {
  agentEditTool: string;
  agentEditToolMinViewSize: number;
  agentEditToolSchemaType: string;
  agentEditToolEnableFuzzyMatching: boolean;
  agentEditToolFuzzyMatchSuccessMessage: string;
  agentEditToolFuzzyMatchMaxDiff: number;
  agentEditToolFuzzyMatchMaxDiffRatio: number;
  agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs: number;
  agentEditToolInstructionsReminder: boolean;
  agentEditToolShowResultSnippet: boolean;
  agentEditToolMaxLines: number;
  agentSaveFileToolInstructionsReminder: boolean;
  enableChatWithTools: boolean;
  enableAgentMode: boolean;
  memoriesParams: MemoriesParams;
  enableTaskList: boolean;
  enableSupportToolUseStart: boolean;
  grepSearchToolEnable: boolean;
  grepSearchToolTimelimitSec: number;
  grepSearchToolOutputCharsLimit: number;
  grepSearchToolNumContextLines: number;
};

export type MemoriesParams = { [key: string]: string | number | boolean };

class ClientFeatureFlagsSingleton {
  private static _instance: IClientFeatureFlags | undefined = undefined;

  static setClientFeatureFlags(ff: IClientFeatureFlags) {
    if (this._instance !== undefined) {
      const logger = getLogger("ClientFeatureFlags");
      logger.warn(
        "Attempting to initialize client feature flags when one is already configured. Keeping existing client feature flags.",
      );
      return;
    }

    this._instance = ff;
  }

  static getClientFeatureFlags(): IClientFeatureFlags {
    if (this._instance === undefined) {
      throw new Error("ClientFeatureFlags not set");
    }
    return this._instance;
  }

  static reset() {
    this._instance = undefined;
  }
}

// Some shorthand functions to make usage a bit easier
export const setLibraryClientFeatureFlags = (ff: IClientFeatureFlags) =>
  ClientFeatureFlagsSingleton.setClientFeatureFlags(ff);
export const getClientFeatureFlags = () =>
  ClientFeatureFlagsSingleton.getClientFeatureFlags();
export const resetLibraryClientFeatureFlags = () =>
  ClientFeatureFlagsSingleton.reset();
