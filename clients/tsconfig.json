{"compilerOptions": {"baseUrl": ".", "paths": {"$common-webviews/*": ["./common/webviews/*"], "$vscode/*": ["./vscode/*"], "@augment-internal/sidecar-libs/*": ["./sidecar/libs/*", "./sidecar/libs/out/*"], "@augment-internal/feature-vector-collector": ["./feature-vector-collector/src/feature-vector-collector-obfuscated.d.ts", "./feature-vector-collector/src/feature-vector-collector-obfuscated.js"], "$*": ["../*", "../bazel-bin/*"]}}}