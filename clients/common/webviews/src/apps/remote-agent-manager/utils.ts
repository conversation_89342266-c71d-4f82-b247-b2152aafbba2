/* eslint-disable @typescript-eslint/naming-convention */
import { type ButtonColor } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
import {
  type ChangedFile,
  FileChangeType,
  type RemoteAgentExchange,
  RemoteAgentStatus,
  RemoteAgentWorkspaceStatus,
} from "$vscode/src/remote-agent-manager/types";
import {
  ChatRequestNodeType,
  ChatResultNodeType,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";

export function getRelativeTimeForStr(dateStr: string): string {
  return getRelativeTime(new Date(dateStr));
}

export function getRelativeTime(date: Date): string {
  try {
    if (isNaN(date.getTime())) {
      return "Unknown time";
    }

    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffSecs / 60);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffSecs < 60) {
      return `${diffSecs}s ago`;
    } else if (diffMins < 60) {
      return `${diffMins}m ago`;
    } else if (diffHours < 24) {
      return `${diffHours}h ago`;
    } else if (diffDays < 30) {
      return `${diffDays}d ago`;
    } else {
      // Format the date for older entries
      return date.toLocaleDateString();
    }
  } catch (e) {
    console.error("Error formatting date:", e);
    return "Unknown time";
  }
}

/**
 * Starts a relative time updater that will call the setRelativeTime function
 * with the relative time string.  The interval will adjust based on the age
 * of the date.
 *
 * @param dateStr The date string to update
 * @param setRelativeTime The function to call with the relative time string
 * @returns A function to stop the updater
 *
 * Example:
 * let relativeTime = getRelativeTimeForStr(timestamp);
 * const stopUpdater = startRelativeTimeUpdater(timestamp, (time) => {
 *   relativeTime = time;
 * });
 *
 * onDestroy(() => {
 *   stopUpdater();
 * });
 */
export function startRelativeTimeUpdater(
  dateStr: string,
  setRelativeTime: (relativeTime: string) => void,
): () => void {
  let intervalMs = 1000;
  const date = new Date(dateStr);
  const interval = setInterval(() => {
    // if dateStr is more than 1m ago, update every minute
    const diffMins = Math.floor((new Date().getTime() - date.getTime()) / 1000 / 60);
    if (diffMins >= 1) {
      intervalMs = 60 * 1000;
    }
    // if dateStr is more than 1h ago, update every hour
    if (diffMins >= 60) {
      intervalMs = 60 * 60 * 1000;
    }
    // if dateStr is more than 1d ago, update every day
    if (diffMins >= 60 * 24) {
      intervalMs = 24 * 60 * 60 * 1000;
    }
    setRelativeTime(getRelativeTimeForStr(dateStr));
  }, intervalMs);

  return () => clearInterval(interval);
}

/**
 * Starts a countdown timer that will call the setCountdown function
 * with the countdown string.  The interval will adjust based on the time
 * remaining. Usage is similar to startRelativeTimeUpdater.
 *
 * @param retryAt The date to count down to
 * @param setCountdown The function to call with the countdown string
 * @returns A function to stop the updater
 */
export function startCountdown(
  retryAt: Date,
  setCountdown: (countdown: string) => void,
): () => void {
  let intervalMs = 1000;
  const interval = setInterval(() => {
    const diffMs = retryAt.getTime() - Date.now();
    if (diffMs <= 0) {
      clearInterval(interval);
      return;
    }
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffSecs / 60);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffSecs < 60) {
      setCountdown(`${diffSecs}s`);
    } else if (diffMins < 60) {
      setCountdown(`${diffMins}m ${diffSecs % 60}s`);
    } else if (diffHours < 24) {
      setCountdown(`${diffHours}h`);
    } else if (diffDays < 30) {
      setCountdown(`${diffDays}d`);
    } else {
      setCountdown(`1mo`);
    }
  }, intervalMs);

  return () => clearInterval(interval);
}

export function getStatusColor(status: RemoteAgentStatus | undefined): ButtonColor {
  if (status === undefined) return "neutral";

  switch (status) {
    case RemoteAgentStatus.agentPending:
    case RemoteAgentStatus.agentStarting:
    case RemoteAgentStatus.agentRunning:
      return "info" as const;
    case RemoteAgentStatus.agentIdle:
      return "success" as const;
    case RemoteAgentStatus.agentFailed:
      return "error" as const;
    default:
      return "neutral";
  }
}

export function getWorkspaceStatusColor(
  status: RemoteAgentWorkspaceStatus | undefined,
): ButtonColor {
  if (status === undefined) return "neutral";

  switch (status) {
    case RemoteAgentWorkspaceStatus.workspaceRunning:
      return "info" as const;
    case RemoteAgentWorkspaceStatus.workspacePausing:
    case RemoteAgentWorkspaceStatus.workspacePaused:
    case RemoteAgentWorkspaceStatus.workspaceResuming:
      return "neutral" as const;
    default:
      return "neutral";
  }
}

export function getStatusText(
  status: RemoteAgentStatus,
  workspaceStatus: RemoteAgentWorkspaceStatus,
  hasUpdates: boolean,
): string {
  if (workspaceStatus === RemoteAgentWorkspaceStatus.workspaceResuming) {
    return "Resuming";
  }
  switch (status) {
    case RemoteAgentStatus.agentStarting:
      return "Starting";
    case RemoteAgentStatus.agentRunning:
      return "Running";
    case RemoteAgentStatus.agentIdle:
      return hasUpdates ? "Unread" : "Idle";
    case RemoteAgentStatus.agentPending:
      return "Pending";
    case RemoteAgentStatus.agentFailed:
      return "Failed";
    default:
      return "Unknown";
  }
}

export function getWorkspaceStatusText(status: RemoteAgentWorkspaceStatus): string {
  switch (status) {
    case RemoteAgentWorkspaceStatus.workspaceRunning:
      return "Running";
    case RemoteAgentWorkspaceStatus.workspacePausing:
      return "Pausing";
    case RemoteAgentWorkspaceStatus.workspacePaused:
      return "Paused";
    case RemoteAgentWorkspaceStatus.workspaceResuming:
      return "Resuming";
    default:
      return "Unknown";
  }
}

/**
 * Gets net aggregate changes from a list of changed files.
 * Excludes files that have been changed but reverted to their original state.
 *
 * @param changedFiles - List of changed files to aggregate
 * @param calculateNetChanges - Whether to calculate net changes (filter out add+delete cycles)
 */
const getAggregateChangesFromFiles = (changedFiles: ChangedFile[]): ChangedFile[] => {
  return calculateNetFileChanges(changedFiles);
};

/**
 * Determines the net change type based on original and final existence states.
 */
const getNetChangeType = (originalExists: boolean, finalExists: boolean): FileChangeType => {
  if (!originalExists && finalExists) {
    return FileChangeType.added;
  } else if (originalExists && !finalExists) {
    return FileChangeType.deleted;
  } else {
    return FileChangeType.modified;
  }
};

/**
 * Calculates net changes across all file operations, filtering out add+delete cycles.
 * This is used for aggregate conversation views to show only files with net changes.
 */
const calculateNetFileChanges = (changedFiles: ChangedFile[]): ChangedFile[] => {
  // Track the evolution of each file through all changes
  const fileEvolution: Record<
    string,
    {
      originalExists: boolean;
      originalContent: string;
      finalExists: boolean;
      finalContent: string;
      finalPath: string;
      latestChange: ChangedFile;
    }
  > = {};

  for (const file of changedFiles) {
    // Determine which file path this change affects
    const filePath = file.old_path || file.new_path;

    if (!fileEvolution[filePath]) {
      // First time seeing this file - establish original state
      const originalExists = file.old_path !== "";
      fileEvolution[filePath] = {
        originalExists,
        originalContent: originalExists ? file.old_contents : "",
        finalExists: file.new_path !== "",
        finalContent: file.new_path !== "" ? file.new_contents : "",
        finalPath: file.new_path || file.old_path,
        latestChange: file,
      };
    } else {
      // Update the final state based on this change
      // old_contents is stable (don't update here)
      fileEvolution[filePath].finalExists = file.new_path !== "";
      fileEvolution[filePath].finalContent = file.new_path !== "" ? file.new_contents : "";
      fileEvolution[filePath].finalPath = file.new_path || file.old_path;
      fileEvolution[filePath].latestChange = file;

      // Special case: if this is a deletion and we haven't seen the original content yet,
      // or if this is a later deletion with different content, update the original content
      if (file.change_type === FileChangeType.deleted && file.old_contents !== "") {
        fileEvolution[filePath].originalContent = file.old_contents;
      }
    }
  }

  // Filter to only include files with net changes
  const netChanges: ChangedFile[] = [];

  for (const [filePath, evolution] of Object.entries(fileEvolution)) {
    const hasNetChange =
      evolution.originalExists !== evolution.finalExists || // Existence changed
      (evolution.originalExists &&
        evolution.finalExists &&
        evolution.originalContent !== evolution.finalContent); // Content changed

    if (hasNetChange) {
      // Construct a proper net change representation instead of using latestChange
      const netChange: ChangedFile = {
        id: evolution.latestChange.id,
        old_path: evolution.originalExists ? filePath : "",
        new_path: evolution.finalExists ? evolution.finalPath : "",
        old_contents: evolution.originalContent,
        new_contents: evolution.finalContent,
        change_type: getNetChangeType(evolution.originalExists, evolution.finalExists),
      };
      netChanges.push(netChange);
    }
  }

  return netChanges;
};

/**
 * Gets aggregate changes from a list of exchanges.
 */
export const getAggregateChanges = (chatHistory: RemoteAgentExchange[]): ChangedFile[] => {
  const changedFiles = chatHistory.flatMap((e) => e.changed_files);
  return getAggregateChangesFromFiles(changedFiles);
};

/**
 * Given a chat history and a turn index, get the most recent user message
 * preceding the turn. If the turn index refers to a user message, returns that message.
 */
export const getUserMessagePrecedingTurn = (
  chatHistory: RemoteAgentExchange[],
  turnIndex: number,
): string => {
  const indexOfLastUserMessage = getIndexOfPrecedingUserMessage(chatHistory, turnIndex);
  return chatHistory[indexOfLastUserMessage]?.exchange.request_message ?? "";
};

/**
 * Gets the index of the last user message preceding the given turn index.
 * If the turn index refers to a user message, returns that index.
 */
export const getIndexOfPrecedingUserMessage = (
  chatHistory: RemoteAgentExchange[],
  turnIndex: number,
): number => {
  // Handle out of bounds indices
  if (turnIndex < 0 || turnIndex >= chatHistory.length) {
    return -1;
  }

  // if the history at turnIndex is a user message, return that index
  if (chatHistory[turnIndex]?.exchange.request_message) {
    return turnIndex;
  }
  return chatHistory.slice(0, turnIndex).findLastIndex((e) => e.exchange.request_message);
};

/**
 * Gets the index of the next user message after the given turn index.
 * Returns chatHistory.length if there is no next user message.
 * If called on a user message, returns the index of the next user message.
 */
export const getIndexOfNextUserMessage = (
  chatHistory: RemoteAgentExchange[],
  turnIndex: number,
): number => {
  const indexOfNextUserMessageRelativeToTurn = chatHistory
    .slice(turnIndex + 1)
    .findIndex((e) => e.exchange.request_message);
  if (indexOfNextUserMessageRelativeToTurn === -1) {
    return chatHistory.length;
  }
  return turnIndex + indexOfNextUserMessageRelativeToTurn + 1;
};

/**
 * Given a turn index, get all agent exchanges between user messages.
 * A turn index can just be a small chunk of the conversation, so we need to find the
 * surrounding user messages to determine the whole "turn" that the user sees as an Augment message.
 * This returns only the agent responses, not the user messages themselves.
 */
export const getTurnList = (
  chatHistory: RemoteAgentExchange[],
  turnIndex: number,
): RemoteAgentExchange[] => {
  const indexOfPrecedingUserMessage = getIndexOfPrecedingUserMessage(chatHistory, turnIndex);
  let indexOfNextUserMessage = getIndexOfNextUserMessage(chatHistory, turnIndex);

  // If no preceding user message found, start from beginning
  const startIndex = indexOfPrecedingUserMessage === -1 ? 0 : indexOfPrecedingUserMessage + 1;

  return chatHistory.slice(startIndex, indexOfNextUserMessage);
};

/**
 * The "turnIndex" refers to a single exchange. But in a single augment message, there can be
 * multiple exchanges. This function gets all the changes for all exchanges in an Augment message
 * that came after the last user message and before the next user message.
 */
export const getAllChangesBetweenUserMessages = (
  chatHistory: RemoteAgentExchange[],
  turnIndex: number,
): ChangedFile[] => {
  // Handle out of bounds indices
  if (turnIndex < 0 || turnIndex >= chatHistory.length) {
    return [];
  }

  const indexOfPrecedingUserMessage = getIndexOfPrecedingUserMessage(chatHistory, turnIndex);

  // If there are no user messages in the conversation, return all changes
  if (indexOfPrecedingUserMessage === -1) {
    const changedFiles = chatHistory.flatMap((e) => e.changed_files);
    return getAggregateChangesFromFiles(changedFiles);
  }

  const turnList = getTurnList(chatHistory, turnIndex);
  const changedFiles = turnList.flatMap((e) => e.changed_files);
  return getAggregateChangesFromFiles(changedFiles);
};

/**
 * Sometimes we just want changes related to a specific turn. But sometimes the relevant exchanges
 * are spread out over multiple turns. This function helps us find the relevant changes for a given
 * turn.
 * It returns all changes, rather than net changes, since in the edit tool for example, we want to
 * see both the add and delete.
 */
export const getChangesForTurn = (
  chatHistory: RemoteAgentExchange[],
  turnIndex: number,
): ChangedFile[] => {
  const nextTurnIndex = getIndexOfNextUserMessage(chatHistory, turnIndex);
  const turnsToCheck = chatHistory.slice(turnIndex, nextTurnIndex);

  const toolCallAtTurn = chatHistory[turnIndex].exchange.response_nodes?.find(
    (node) => node.type === ChatResultNodeType.TOOL_USE,
  );

  if (!toolCallAtTurn) {
    return [];
  }

  const toolUseId = toolCallAtTurn.tool_use?.tool_use_id;
  if (!toolUseId) {
    return [];
  }

  // Find the turn that contains the tool result
  const toolResultTurn = turnsToCheck.find((turn) => {
    return turn.exchange.request_nodes?.some((node) => {
      return (
        node.type === ChatRequestNodeType.TOOL_RESULT &&
        node.tool_result_node?.tool_use_id === toolUseId
      );
    });
  });
  if (!toolResultTurn) {
    return [];
  }

  // We only need the changed_files from the specific turn
  // where the tool call returned some change.
  const changedFiles = toolResultTurn.changed_files;
  return changedFiles;
};
