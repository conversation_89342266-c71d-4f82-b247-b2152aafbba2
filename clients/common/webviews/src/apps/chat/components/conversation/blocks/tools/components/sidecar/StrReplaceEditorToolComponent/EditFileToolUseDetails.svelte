<script lang="ts">
  import { getChatModel } from "$common-webviews/src/apps/chat/chat-context";
  import { visibilityObserver } from "$common-webviews/src/apps/chat/components/actions/trackOnScreen";
  import {
    type ToolUseState,
    stringOrDefault,
    ToolUsePhase,
  } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import { getChangesForTurn } from "$common-webviews/src/apps/remote-agent-manager/utils";
  import Filespan from "$common-webviews/src/common/components/Filespan.svelte";
  import LanguageIcon from "$common-webviews/src/common/components/language-icons/LanguageIcon.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { getContext, onDestroy } from "svelte";
  import { getToolUseContext } from "../../../tool-context";
  import OpenFileButton from "../../OpenFileButton.svelte";
  import EditFileDiff from "./EditFileDiff.svelte";
  import LineDiffCount from "./LineDiffCount.svelte";

  const ctx = getToolUseContext();

  export let toolUseInput: Record<string, unknown> = $ctx.toolUseInput;
  export let toolUseState: ToolUseState = $ctx.toolUseState;
  export let requestId: string = $ctx.requestId;
  export let turnIndex: number = $ctx.turnIndex;
  let loaded = false;
  let isVisible = false;
  let componentElement: HTMLElement;
  let observer: ReturnType<typeof visibilityObserver> | undefined;
  const chatModel = getChatModel();
  const remoteAgentsModel = getContext<RemoteAgentsModel | undefined>(RemoteAgentsModel.key);
  $: isRemoteAgentMode = !!remoteAgentsModel?.isActive;

  $: toolUseInput = $ctx.toolUseInput;
  $: toolUseState = $ctx.toolUseState;
  $: requestId = $ctx.requestId;
  $: {
    requestId, toolUseInput, toolUseState;
    loaded = false;
  }

  let completed = toolUseState.phase === ToolUsePhase.completed;
  $: completed = toolUseState.phase === ToolUsePhase.completed;

  let contents: { originalCode: string | undefined; modifiedCode: string | undefined } | undefined =
    undefined;
  // Set up visibility observer
  $: if (componentElement) {
    observer?.destroy();
    observer = visibilityObserver(componentElement, {
      onVisible: () => {
        isVisible = true;
        void loadContents();
      },
      onHidden: () => {
        isVisible = false;
      },
      scrollTarget: document.body,
    });
  }

  function loadRemoteAgentContents() {
    if (!isRemoteAgentMode) return;
    const changedFiles = getChangesForTurn(
      $remoteAgentsModel?.currentConversation?.exchanges ?? [],
      turnIndex,
    );

    if (!changedFiles) {
      console.warn("no changed files for remote agent turn", turnIndex);
      return;
    }
    let file = changedFiles.find((f) => f.new_path === toolUseInput.path);
    if (!file) {
      // There are some cases where the toolUseInput path is something like "./services/README_1.md"
      // but the changed_file just has "services/README_1.md" so we should also check for substrings
      file = changedFiles.find((f) => (toolUseInput.path as string).includes(f.new_path));
    }
    if (!file) {
      console.warn("no changed file for path", toolUseInput.path);
      return;
    }
    contents = {
      originalCode: file.old_contents,
      modifiedCode: file.new_contents,
    };
    loaded = true;
  }

  async function loadContents() {
    if ((loaded && contents?.originalCode !== contents?.modifiedCode) || !completed || !isVisible) {
      return;
    }
    if (isRemoteAgentMode) {
      loadRemoteAgentContents();
      return;
    }
    contents = await chatModel?.extensionClient.getAgentEditContentsByRequestId(requestId);
    if (contents?.originalCode === contents?.modifiedCode) {
      console.warn("no changes for requestId", requestId);
    }
    loaded = true;
  }

  // Clean up the observer when the component is destroyed
  onDestroy(() => {
    observer?.destroy();
  });

  // Watch for phase transitions to completed
  $: if (toolUseState.phase === ToolUsePhase.completed && isVisible) {
    void loadContents();
  }
  $: filename = stringOrDefault(toolUseInput.path, "").split("/").at(-1) ?? "";
</script>

<div class="c-edit-file-tool-use-details" bind:this={componentElement}>
  <div class="c-edit-file-tool-use-details__header">
    <TextAugment size={1} color="primary" class="c-edit-file-tool-use-details__header__icon">
      <LanguageIcon {filename} />
      <Filespan filepath={filename} />
    </TextAugment>
    <div class="c-edit-file-tool-use-details__header__counts">
      <LineDiffCount {requestId} />
      <OpenFileButton path={stringOrDefault(toolUseInput.path, "")} />
    </div>
  </div>
  {#if contents}
    <EditFileDiff {...contents} path={filename} />
  {:else}
    <div class="c-edit-file-tool-use-details__loading">Loading...</div>
  {/if}
</div>

<style>
  .c-edit-file-tool-use-details {
    padding-left: var(--ds-spacing-3);
    & .diff-hidden-lines-widget {
      display: none;
    }

    & .monaco-editor,
    & .monaco-diff-editor,
    & .monaco-component {
      --vscode-focusBorder: transparent;
      --vscode-editor-background: transparent;
      --vscode-editorGutter-background: transparent;
    }
    & .noModificationsOverlay {
      display: none;
    }
  }
  .c-edit-file-tool-use-details__header {
    display: flex;
    flex-direction: row;
    flex: 1;
    align-items: center;
    gap: var(--ds-spacing-2);
    padding: var(--ds-spacing-1);
    justify-content: space-between;
  }
  .c-edit-file-tool-use-details :global(.c-edit-file-tool-use-details__header__icon) {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-2);
  }
  .c-edit-file-tool-use-details__header__counts {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-2);
  }
  .c-edit-file-tool-use-details {
    border-radius: var(--ds-radius-2);
    background-color: var(--ds-color-neutral-a2);
  }
</style>
