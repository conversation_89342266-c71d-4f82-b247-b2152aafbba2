import { host } from "$common-webviews/src/common/hosts/__mocks__/host";
import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";
import { ChatMode } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { AgentWebViewMessageType } from "@augment-internal/sidecar-libs/src/webview-messages/message-types/agent-messages";
import { ToolsWebViewMessageType } from "@augment-internal/sidecar-libs/src/webview-messages/message-types/tool-messages";
import { get } from "svelte/store";
import { beforeEach, describe, expect, test, vi } from "vitest";
import { ExchangeStatus } from "../types/chat-message";
import { AgentConversationModel } from "./agent-conversation-model";
import { ChatModel } from "./chat-model";
import { SpecialContextInputModel } from "./context-model";
import { ConversationModel } from "./conversation-model";
import { ToolsWebviewModel } from "./tools-webview-model";
import { ChatModeModel } from "./chat-mode-model";
import { RemoteAgentsModel } from "../../remote-agent-manager/models/remote-agents-model";

// Helper function to wait for state updates
async function tick() {
  await new Promise((resolve) => setTimeout(resolve, 0));
}

describe("AgentConversationModel", () => {
  let chatModel: ChatModel;
  let chatModeModel: ChatModeModel;
  let remoteAgentsModel: RemoteAgentsModel;
  let agentModel: AgentConversationModel;
  let contextModel: SpecialContextInputModel;
  let messageBroker: MessageBroker;

  beforeEach(() => {
    host.postMessage.mockImplementation((msg) => {
      // Give up main thread to force some aynchronousity
      setTimeout(() => {
        switch (msg.type) {
          case WebViewMessageType.asyncWrapper: {
            const baseMsg = msg.baseMsg;
            if (!baseMsg) {
              throw new Error("No base message given with async wrapper");
            }
            switch (baseMsg.type) {
              case WebViewMessageType.chatLoaded: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.chatInitialize,
                        data: {
                          enableAgentMode: true,
                          memoryClassificationOnFirstToken: false,
                        },
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }
              case WebViewMessageType.chatModeChanged: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.empty,
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }
              case WebViewMessageType.openConfirmationModal: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.confirmationModalResponse,
                        data: {
                          ok: true,
                        },
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }
              case WebViewMessageType.chatGetAgentOnboardingPromptRequest: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.chatGetAgentOnboardingPromptResponse,
                        data: {
                          prompt: "Onboarding prompt",
                        },
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }
              case AgentWebViewMessageType.reportAgentSessionEvent: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.empty,
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }
              case WebViewMessageType.checkAgentAutoModeApproval: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.checkAgentAutoModeApproval,
                        data: false,
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }
              case WebViewMessageType.getChatRequestIdeStateRequest: {
                // This is just mocked and not used in any of the tests below.
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.getChatRequestIdeStateResponse,
                        data: {},
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }
              case WebViewMessageType.getWorkspaceInfoRequest: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.getWorkspaceInfoResponse,
                        data: {
                          trackedFileCount: [1000, 500], // Mock tracked file counts
                        },
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }
              case AgentWebViewMessageType.setCurrentConversation:
              case ToolsWebViewMessageType.closeAllToolProcesses: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.empty,
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }
              case AgentWebViewMessageType.getEditListRequest: {
                break;
              }
              case AgentWebViewMessageType.checkHasEverUsedAgent: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: AgentWebViewMessageType.checkHasEverUsedAgentResponse,
                        data: false,
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }
              case AgentWebViewMessageType.setHasEverUsedAgent: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.empty,
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }
              case AgentWebViewMessageType.checkHasEverUsedRemoteAgent: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: AgentWebViewMessageType.checkHasEverUsedRemoteAgentResponse,
                        data: false,
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }
              case AgentWebViewMessageType.setHasEverUsedRemoteAgent: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.empty,
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }

              case WebViewMessageType.getRemoteAgentNotificationEnabledRequest: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.getRemoteAgentNotificationEnabledResponse,
                        data: {}, // Empty notification settings
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }
              case WebViewMessageType.getRemoteAgentPinnedStatusRequest: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.getRemoteAgentPinnedStatusResponse,
                        data: {}, // Empty pinned status
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }

              case WebViewMessageType.getRemoteAgentOverviewsRequest: {
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.getRemoteAgentOverviewsResponse,
                        data: { overviews: [] }, // Empty overviews
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              }

              case WebViewMessageType.getRemoteAgentStatus:
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: WebViewMessageType.asyncWrapper,
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.remoteAgentStatusResponse,
                        data: { isRemoteAgentSshWindow: false },
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;

              default:
                throw new Error(`Unexpected async message type: ${baseMsg.type}`);
            }
            break;
          }
          case WebViewMessageType.chatClearMetadata:
            break;
          case WebViewMessageType.triggerInitialOrientation:
            break;
          default:
            throw new Error(`Unexpected message type: ${msg.type}`);
        }
      }, 0);
    });
    messageBroker = new MessageBroker(host);
    contextModel = new SpecialContextInputModel();
    chatModel = new ChatModel(messageBroker, host, contextModel, {
      initialFlags: {
        doUseNewDraftFunctionality: true,
        enableBackgroundAgents: true,
      },
    });

    remoteAgentsModel = new RemoteAgentsModel({
      msgBroker: messageBroker,
      isActive: false,
      flagsModel: chatModel.flags,
      host,
      chatModel,
    });
    // Create a mock checkpoint store
    const mockCheckpointStore = {
      registerAgentConversationModel: vi.fn(),
      targetCheckpointSummary: {
        subscribe: vi.fn(() => {
          return { unsubscribe: vi.fn() };
        }),
      },
      targetCheckpointHasChanges: {
        subscribe: vi.fn(() => {
          return { unsubscribe: vi.fn() };
        }),
      },
      currentCheckpoint: {
        subscribe: vi.fn(() => {
          return { unsubscribe: vi.fn() };
        }),
      },
      createNewCheckpoint: vi.fn(),
      maybeCreateOriginalCheckpoint: vi.fn(),
    };

    // Create the agent model with the mock checkpoint store
    agentModel = new AgentConversationModel(
      chatModel,
      new ToolsWebviewModel(
        chatModel.currentConversationModel,
        chatModel.extensionClient,
        chatModel,
        undefined, // remoteAgentsModel not needed for these tests
      ),
      mockCheckpointStore as any,
    );

    chatModeModel = new ChatModeModel(chatModel, agentModel, remoteAgentsModel);

    // Initialize with a default conversation
    const defaultConv = {
      id: "default-conv",
      chatHistory: [],
      extraData: {},
      createdAtIso: new Date().toISOString(),
      lastInteractedAtIso: new Date().toISOString(),
      feedbackStates: {},
      toolUseStates: {},
      requestIds: [],
    };
    chatModel.currentConversationModel.setConversation(defaultConv);
  });

  describe("Conversation Mode Management", () => {
    test("should switch to agent mode", async () => {
      agentModel.setToAgentic();
      await tick();

      const currentConv = get(chatModel.currentConversationModel);
      expect(currentConv.extraData?.isAgentConversation).toBe(true);
      expect(get(agentModel.isCurrConversationAgentic)).toBe(true);
    });

    test("should switch back to chat mode", async () => {
      // First set to agent mode
      agentModel.setToAgentic();
      await tick();

      // Then switch back to chat
      agentModel.setToChat();
      await tick();

      const currentConv = get(chatModel.currentConversationModel);
      expect(currentConv.extraData?.isAgentConversation).toBeFalsy();
      expect(get(agentModel.isCurrConversationAgentic)).toBe(false);
    });

    // AW: I don't quite understand what this test is trying to verify.
    // skipping, but leaving it here to make sure I don't remove necessary logic.
    test.skip("should support multiple agent conversations", async () => {
      // Create first agent conversation
      agentModel.setToAgentic();
      await tick();
      const firstAgentConvId = get(chatModel.currentConversationModel).id;
      expect(get(chatModel.currentConversationModel).extraData?.isAgentConversation).toBe(true);

      // Create a new agent conversation
      chatModel.setCurrentConversation();
      await tick();

      // Should create a new agent conversation
      const secondAgentConvId = get(chatModel.currentConversationModel).id;
      expect(secondAgentConvId).not.toBe(firstAgentConvId);
      expect(get(chatModel.currentConversationModel).extraData?.isAgentConversation).toBe(true);
    });

    test("should handle switching to non-existent conversation", async () => {
      // First set to agent mode
      agentModel.setToAgentic();
      await tick();

      // Try to switch to a non-existent conversation
      const nonExistentId = "non-existent-id";
      chatModel.setCurrentConversation(nonExistentId);
      await tick();

      // Should create a new agent conversation
      const currentConv = get(chatModel.currentConversationModel);
      expect(currentConv.id).not.toBe(nonExistentId);
      expect(currentConv.extraData?.isAgentConversation).toBe(true);
      expect(currentConv.extraData?.hasDirtyEdits).toBeFalsy();
    });

    test("should handle subscription updates correctly", async () => {
      const storeSpy = vi.fn();
      agentModel.isCurrConversationAgentic.subscribe(storeSpy);

      agentModel.setToAgentic();
      await tick();
      expect(storeSpy).toHaveBeenCalledWith(true);

      agentModel.setToChat();
      await tick();
      expect(storeSpy).toHaveBeenCalledWith(false);
    });

    test("should handle switching to agent mode when no previous agent conversation exists", async () => {
      agentModel.setToAgentic();
      await tick();

      const currentConv = get(chatModel.currentConversationModel);
      expect(currentConv.extraData?.isAgentConversation).toBe(true);
      expect(get(agentModel.isCurrConversationAgentic)).toBe(true);
    });

    test("should maintain agent state when switching between agent conversations", async () => {
      // Create first agent conversation
      agentModel.setToAgentic();
      await tick();
      const firstAgentConvId = get(chatModel.currentConversationModel).id;

      // Create second agent conversation
      chatModel.setCurrentConversation();
      await tick();

      // Switch back to first agent conversation
      chatModel.currentConversationModel.setConversation({
        id: firstAgentConvId,
        chatHistory: [],
        extraData: { isAgentConversation: true },
        createdAtIso: new Date().toISOString(),
        lastInteractedAtIso: new Date().toISOString(),
        feedbackStates: {},
        toolUseStates: {},
        requestIds: [],
      });
      await tick();

      expect(get(chatModel.currentConversationModel).id).toBe(firstAgentConvId);
      expect(get(chatModel.currentConversationModel).extraData?.isAgentConversation).toBe(true);
      expect(get(agentModel.isCurrConversationAgentic)).toBe(true);
    });

    test("should create new non-agent conversation when creating a new chat thread from a non-empty agent conversation", async () => {
      // Create first agent conversation
      agentModel.setToAgentic();
      await tick();

      // Create second agent conversation by creating new conversation and setting it to agentic
      const secondConv = ConversationModel.create();
      chatModel.currentConversationModel.setConversation(secondConv);
      await tick();
      agentModel.setToAgentic();
      await tick();

      // Add a message to make the agent conversation non-empty
      chatModel.currentConversationModel.addChatItem({
        // Note: chatItemType is undefined for regular exchanges according to isChatItemExchangeWithStatus
        /* eslint-disable @typescript-eslint/naming-convention */
        request_message: "Hello agent",
        response_text: "Agent response",
        status: ExchangeStatus.success,
        request_id: "test-agent-id",
        /* eslint-enable @typescript-eslint/naming-convention */
      });
      await tick();

      // Verify the conversation is not empty
      const agentConv = get(chatModel.currentConversationModel);
      expect(agentConv.chatHistory.length).toBe(1);
      expect(ConversationModel.isEmpty(agentConv)).toBe(false);

      // Verify we have only agent conversations
      const conversations = Object.values(chatModel.conversations);
      expect(conversations.length).toBeGreaterThan(1);
      expect(conversations.every((conv) => conv.extraData?.isAgentConversation)).toBe(true);

      chatModel.setCurrentConversation(undefined);
      chatModeModel.setToChat();
      await tick();

      // Verify new non-agent conversation was created and is current
      const currentConv = get(chatModel.currentConversationModel);
      expect(currentConv.extraData?.isAgentConversation).toBe(false || undefined);
      expect(get(agentModel.isCurrConversationAgentic)).toBe(false);
    });

    test("should restore original thread when switching back if no message was sent", async () => {
      // Create a chat conversation that is empty
      const chatConv = {
        id: "chat-conv",
        chatHistory: [],
        extraData: { isAgentConversation: false },
        createdAtIso: new Date().toISOString(),
        lastInteractedAtIso: new Date().toISOString(),
        feedbackStates: {},
        toolUseStates: {},
        requestIds: [],
      };
      chatModel.currentConversationModel.setConversation(chatConv);
      await tick();
      const chatConvId = get(chatModel.currentConversationModel).id;

      // Mock the implementation of _shouldCreateNewConversation to return false
      // This simulates an empty conversation that should be restored when switching back
      const originalMethod = agentModel["_shouldCreateNewConversation"];
      agentModel["_shouldCreateNewConversation"] = vi.fn().mockReturnValue(false);

      // Switch to agent mode
      agentModel.setToAgentic();
      await tick();
      const agentConvId = get(chatModel.currentConversationModel).id;

      // Switch back to chat mode without sending any messages
      agentModel.setToChat();
      await tick();

      // Should restore the original chat conversation
      expect(get(chatModel.currentConversationModel).id).toBe(chatConvId);

      // Switch back to agent mode again without sending any messages
      agentModel.setToAgentic();
      await tick();

      // Should restore the original agent conversation
      expect(get(chatModel.currentConversationModel).id).toBe(agentConvId);

      // Restore the original method
      agentModel["_shouldCreateNewConversation"] = originalMethod;
    });
  });

  describe("Context and Chat Mode Handling", () => {
    test("should set correct chat mode when switching", async () => {
      const spy = vi.spyOn(chatModel.extensionClient, "setChatMode");

      // Switch to agent mode
      agentModel.setToAgentic();
      await tick();
      expect(spy).toHaveBeenCalledWith(ChatMode.agent);

      // Switch to chat mode
      agentModel.setToChat();
      await tick();
      expect(spy).toHaveBeenCalledWith(ChatMode.chat);
    });

    test("should handle mode changes on conversation updates", async () => {
      const spy = vi.spyOn(chatModel.extensionClient, "setChatMode");

      // Set up an agent conversation
      agentModel.setToAgentic();
      await tick();

      // Create a new regular conversation
      const regularConv = {
        id: "regular-conv",
        name: "Regular Conversation",
        chatHistory: [
          // eslint-disable-next-line @typescript-eslint/naming-convention
          { request_message: "Hello", response_text: "World", status: ExchangeStatus.success },
        ],
        extraData: { someKey: "someValue" },
        createdAtIso: new Date().toISOString(),
        lastInteractedAtIso: new Date().toISOString(),
        feedbackStates: {},
        toolUseStates: {},
        requestIds: ["request-1", "request-2"],
      };

      chatModel.currentConversationModel.setConversation(regularConv);
      await tick();

      expect(spy).toHaveBeenCalledWith(ChatMode.chat);
    });
  });

  describe("Dirty Edits Handling", () => {
    test("should initialize with clean state", async () => {
      agentModel.setToAgentic();
      await tick();

      const currentConv = get(chatModel.currentConversationModel);
      expect(currentConv.extraData?.hasDirtyEdits).toBeFalsy();
    });

    test("should handle dirty state", async () => {
      agentModel.setToAgentic();
      await tick();

      agentModel.markDirty();
      const dirtyConv = get(chatModel.currentConversationModel);
      expect(dirtyConv.extraData?.hasDirtyEdits).toBe(true);

      agentModel.markClean();
      const cleanConv = get(chatModel.currentConversationModel);
      expect(cleanConv.extraData?.hasDirtyEdits).toBe(false);
    });

    test("should allow switching when no dirty edits", async () => {
      // Set up clean agent conversation
      agentModel.setToAgentic();
      await tick();

      const agentConvId = get(chatModel.currentConversationModel).id;

      // Create and switch to a new conversation
      const newConv = {
        id: "new-conv",
        chatHistory: [],
        extraData: {},
        createdAtIso: new Date().toISOString(),
        lastInteractedAtIso: new Date().toISOString(),
        feedbackStates: {},
        toolUseStates: {},
        requestIds: [],
      };

      chatModel.currentConversationModel.setConversation(newConv);
      await tick();

      // Should have switched successfully
      const currentConv = get(chatModel.currentConversationModel);
      expect(currentConv.id).toBe(newConv.id);
      expect(currentConv.id).not.toBe(agentConvId);
    });

    test("should handle accepting edits when switching", async () => {
      // Set up agent conversation with dirty edits
      agentModel.setToAgentic();
      await tick();

      agentModel.markDirty();

      // Mock the extension client's acceptAllAgentEdits to return true
      vi.spyOn(chatModel.extensionClient, "acceptAllAgentEdits").mockResolvedValue(true);

      const newConv = {
        id: "new-conv",
        chatHistory: [],
        extraData: {},
        createdAtIso: new Date().toISOString(),
        lastInteractedAtIso: new Date().toISOString(),
        feedbackStates: {},
        toolUseStates: {},
        requestIds: [],
      };

      // Try to switch
      chatModel.currentConversationModel.setConversation(newConv);
      await tick();

      // Should have switched and cleaned up the state
      const currentConv = get(chatModel.currentConversationModel);
      expect(currentConv.id).toBe(newConv.id);
      expect(currentConv.extraData?.hasDirtyEdits).toBeFalsy();
    });

    test("should handle concurrent dirty state changes", async () => {
      agentModel.setToAgentic();
      await tick();

      // Simulate rapid dirty state changes
      agentModel.markDirty();
      agentModel.markClean();
      agentModel.markDirty();
      await tick();

      const currentConv = get(chatModel.currentConversationModel);
      expect(currentConv.extraData?.hasDirtyEdits).toBe(true);
    });

    test("should preserve dirty state when updating same conversation", async () => {
      agentModel.setToAgentic();
      await tick();

      agentModel.markDirty();
      const currentConvId = get(chatModel.currentConversationModel).id;

      // Update the same conversation
      chatModel.currentConversationModel.setConversation({
        id: currentConvId,
        chatHistory: [],
        extraData: { isAgentConversation: true, hasDirtyEdits: true },
        createdAtIso: new Date().toISOString(),
        lastInteractedAtIso: new Date().toISOString(),
        feedbackStates: {},
        toolUseStates: {},
        requestIds: [],
      });
      await tick();

      const currentConv = get(chatModel.currentConversationModel);
      expect(currentConv.id).toBe(currentConvId);
      expect(currentConv.extraData?.hasDirtyEdits).toBe(true);
    });
  });

  describe("Agent Interruption", () => {
    test("should interrupt agent when switching conversations", async () => {
      agentModel.setToAgentic();
      await tick();

      const spy = vi.spyOn(agentModel, "interruptAgent");
      agentModel.interruptAgent();

      expect(spy).toHaveBeenCalled();
    });
  });

  describe("Agent hasEverUsedAgent", () => {
    test("first load should be undefined", () => {
      expect(get(agentModel.hasEverUsedAgent)).toBe(undefined);
    });

    test("user with agent chat thread should be true", () => {
      chatModel.currentConversationModel.setConversation({
        id: "agent-conv",
        chatHistory: [],
        extraData: { isAgentConversation: true },
        createdAtIso: new Date().toISOString(),
        lastInteractedAtIso: new Date().toISOString(),
        feedbackStates: {},
        toolUseStates: {},
        requestIds: [],
      });

      // Create a mock checkpoint store
      const mockCheckpointStore = {
        registerAgentConversationModel: vi.fn(),
        targetCheckpointSummary: {
          subscribe: vi.fn(() => {
            return { unsubscribe: vi.fn() };
          }),
        },
        targetCheckpointHasChanges: {
          subscribe: vi.fn(() => {
            return { unsubscribe: vi.fn() };
          }),
        },
        currentCheckpoint: {
          subscribe: vi.fn(() => {
            return { unsubscribe: vi.fn() };
          }),
        },
        createNewCheckpoint: vi.fn(),
        maybeCreateOriginalCheckpoint: vi.fn(),
      };

      // Create the agent model with the mock checkpoint store
      const testAgentModel = new AgentConversationModel(
        chatModel,
        new ToolsWebviewModel(
          chatModel.currentConversationModel,
          chatModel.extensionClient,
          chatModel,
          undefined, // remoteAgentsModel not needed for these tests
        ),
        mockCheckpointStore as any,
      );
      expect(get(testAgentModel.hasEverUsedAgent)).toBe(true);
    });

    test("user without agent chat thread and no context should be false", async () => {
      const spy = vi
        .spyOn(chatModel.extensionClient, "checkHasEverUsedAgent")
        .mockResolvedValue(false);

      expect(get(agentModel.hasEverUsedAgent)).toBe(undefined);

      await agentModel.refreshHasEverUsedAgent();

      expect(get(agentModel.hasEverUsedAgent)).toBe(false);

      expect(spy).toHaveBeenCalled();
    });

    test("user without agent chat thread and context should be true", async () => {
      const spy = vi
        .spyOn(chatModel.extensionClient, "checkHasEverUsedAgent")
        .mockResolvedValue(true);

      expect(get(agentModel.hasEverUsedAgent)).toBe(undefined);

      await agentModel.refreshHasEverUsedAgent();

      expect(get(agentModel.hasEverUsedAgent)).toBe(true);

      expect(spy).toHaveBeenCalled();
    });
  });
});
