# Feature Vector Documentation

This document describes all the features collected in the VSCode extension's feature vector system. The feature vector is used to create a unique fingerprint of the user's development environment.

## Overview

The feature vector collector gathers various system and environment information to create a comprehensive profile. Each feature is canonicalized using SHA-256 hashing before being included in the vector.

## Feature Vector Components

### Index 0: vscode
- **Description**: VSCode version
- **Collection Method**: `vscode.version`
- **Example**: "1.85.0"

### Index 1: machineId
- **Description**: VSCode's unique identifier for the computer
- **Collection Method**: `vscode.env.machineId`
- **Example**: "a1b2c3d4-e5f6-7890-abcd-ef1234567890"
- **Note**: This is VSCode's own machine identifier, different from the OS-level machine ID

### Index 2: os
- **Description**: Operating system type
- **Collection Method**: `os.type()`
- **Example**: "Linux", "Darwin", "Windows_NT"

### Index 3: cpu
- **Description**: CPU model name
- **Collection Method**: `os.cpus()[0].model`
- **Example**: "Intel(R) Core(TM) i7-9750H CPU @ 2.60GHz"

### Index 4: memory
- **Description**: Total system memory in bytes
- **Collection Method**: `os.totalmem().toString()`
- **Example**: "17179869184"

### Index 5: numCpus
- **Description**: Number of CPU cores
- **Collection Method**: `os.cpus().length.toString()`
- **Example**: "8"

### Index 6: hostname
- **Description**: System hostname
- **Collection Method**: `os.hostname()`
- **Example**: "developer-laptop"

### Index 7: arch
- **Description**: System architecture
- **Collection Method**: `os.machine()`
- **Example**: "x86_64"

### Index 8: username
- **Description**: Current user's username
- **Collection Method**: `os.userInfo().username`
- **Example**: "johndoe"

### Index 9: macAddresses
- **Description**: External network interface MAC addresses (sorted)
- **Collection Method**: `os.networkInterfaces()` - filters out internal interfaces
- **Example**: ["00:11:22:33:44:55", "aa:bb:cc:dd:ee:ff"]

### Index 10: osRelease
- **Description**: Operating system release version
- **Collection Method**: `os.release()`
- **Example**: "5.15.0-91-generic"

### Index 11: kernelVersion
- **Description**: Operating system kernel version
- **Collection Method**: `os.version()`
- **Example**: "#101-Ubuntu SMP Tue Nov 14 13:30:08 UTC 2023"

### Index 12: checksum
- **Description**: SHA-256 checksum of all other features
- **Collection Method**: Calculated from all other feature values
- **Example**: SHA-256 hash string

### Index 13: telemetryDevDeviceId
- **Description**: VSCode's stored version of the OS device ID
- **Collection Method**: `context.globalState.get('telemetry.devDeviceId')`
- **Example**: "dev-device-id-1234567890abcdef"
- **Note**: This is VSCode's stored copy of the OS machine ID. Abusers may clear this field in their exploits, so comparison with the actual osMachineId field (index 16) is crucial for security

### Index 14: requestId
- **Description**: Unique request identifier (changes each collection)
- **Collection Method**: `crypto.randomUUID()`
- **Example**: "550e8400-e29b-41d4-a716-************"

### Index 15: randomHash
- **Description**: Random 16-byte hash (changes each collection)
- **Collection Method**: `crypto.randomBytes(16).toString("hex")`
- **Example**: "a1b2c3d4e5f67890abcdef1234567890"

### Index 16: osMachineId
- **Description**: Operating system machine ID
- **Collection Method**: `node-machine-id` package - `machineIdSync()`
- **Example**: "3ae401f10dd34caeaf3c349cc383953a2b304930e00b4bb5c46223cd9fdce23c"
- **Note**: This is the actual OS-level machine ID, different from VSCode's machineId

### Index 17: homeDirectoryIno
- **Description**: Inode number of user's home directory
- **Collection Method**: `fs.lstat(os.homedir()).ino`
- **Example**: "22708891"
- **Note**: An inode is a unique identifier for a file/directory within a filesystem. It persists even if the directory is renamed

### Index 18: projectRootIno
- **Description**: Inode number of workspace root directory
- **Collection Method**: `fs.lstat(workspaceFolder.uri.fsPath).ino`
- **Example**: "23007814"
- **Note**: Combined with filesystem ID, this creates a unique identifier for the project location

### Index 19: gitUserEmail
- **Description**: Git user email from repository configuration
- **Collection Method**: `git config user.email` (falls back to global config)
- **Example**: "<EMAIL>"

### Index 20: sshPublicKey
- **Description**: User's SSH public key
- **Collection Method**: Reads from `~/.ssh/id_rsa.pub` (with fallbacks to `id_ed25519.pub` and `id_ecdsa.pub`)
- **Example**: "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAAB... user@hostname"
- **Note**: Contains the full public key including type, base64-encoded key data, and optional comment

### Index 21: gpuInfo
- **Description**: Graphics controller information
- **Collection Method**: `systeminformation` package - `si.graphics()`
- **Format**: "vendor model vramMB" (semicolon-separated for multiple GPUs)
- **Example**: "NVIDIA Corporation RTX 3080 10240MB"
- **Note**: Includes vendor, model, and VRAM size for each graphics controller

### Index 22: timezone
- **Description**: System timezone
- **Collection Method**: `systeminformation` package - `si.time().timezone`
- **Example**: "America/New_York", "UTC+0000"
- **Note**: Returns the system's configured timezone

### Index 23: diskLayout
- **Description**: Physical disk layout information
- **Collection Method**: `systeminformation` package - `si.diskLayout()`
- **Format**: "type size" (semicolon-separated for multiple disks)
- **Example**: "NVMe 512110190592; SSD 1000204886016"
- **Note**: Type can be HD, SSD, or NVMe; size is in bytes

### Index 24: systemInfo
- **Description**: Hardware system information
- **Collection Method**: `systeminformation` package - `si.system()`
- **Format**: "manufacturer model version"
- **Example**: "Dell Inc. XPS 15 9570 1.0"
- **Note**: Provides manufacturer, model/product name, and version

### Index 25: biosInfo
- **Description**: BIOS/UEFI firmware information
- **Collection Method**: `systeminformation` package - `si.bios()`
- **Format**: "vendor version releaseDate"
- **Example**: "Dell Inc. 1.21.0 06/15/2021"
- **Note**: Includes BIOS vendor, version, and release date

### Index 26: baseboardInfo
- **Description**: Motherboard/baseboard information
- **Collection Method**: `systeminformation` package - `si.baseboard()`
- **Format**: "manufacturer model version"
- **Example**: "ASUSTeK COMPUTER INC. ROG STRIX B550-F GAMING Rev 1.xx"
- **Note**: Provides motherboard manufacturer, model, and version

### Index 27: chassisInfo
- **Description**: Computer chassis/enclosure information
- **Collection Method**: `systeminformation` package - `si.chassis()`
- **Format**: "manufacturer type version"
- **Example**: "Dell Inc. Notebook", "MSI Desktop"
- **Note**: Type indicates form factor (e.g., Notebook, Desktop, Tower)

## Implementation Details

### Canonicalization
All feature values (except arrays) are canonicalized using SHA-256 hashing before being stored in the feature vector. This ensures consistent formatting and privacy protection.

### Error Handling
All collection methods include error handling that returns empty strings when data cannot be collected. This ensures the feature vector can still be generated even if some information is unavailable.

### Async Collection
The `createFeatures()` function is asynchronous to support file system operations and external command execution. Features are collected in parallel where possible for performance.

### Dependencies
- **node-machine-id**: For collecting OS-level machine ID
- **systeminformation**: For collecting detailed hardware information
- **Built-in Node.js modules**: crypto, fs, os, path, child_process

## Usage

```typescript
import { createFeatures } from './feature-vector-collector';

// In VSCode extension context
const features = await createFeatures(context);
const featureVector = features.toVector();
```

## Security Considerations

### telemetryDevDeviceId vs osMachineId
The `telemetryDevDeviceId` (index 13) is VSCode's stored copy of the OS device ID in globalState. This value can be cleared by malicious actors attempting to evade detection. The `osMachineId` (index 16) is the actual OS-level machine ID retrieved directly from the system. Comparing these two values is crucial for security:
- If they match: Normal operation
- If telemetryDevDeviceId is empty/missing: Possible first run or cleared data
- If they differ: Potential tampering or system changes

## Privacy Considerations

- All values are hashed using SHA-256 before transmission
- SSH public keys are included (public information by design)
- No private keys or sensitive credentials are collected
- Git user email is from local configuration only
- Hardware information is used for system fingerprinting only
