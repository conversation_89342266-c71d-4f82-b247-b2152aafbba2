import { createFeatures, FeatureVector } from "@augment-internal/feature-vector-collector";
import { MetricsReporter } from "@augment-internal/sidecar-libs/src/metrics/metrics-reporter";
import * as vscode from "vscode";

import { APIServer } from "../augment-api";
import { getLogger } from "../logging";

export class FeatureVectorReporter extends MetricsReporter<FeatureVector> {
    public static defaultMaxRecords = 10000;
    public static defaultBatchSize = 1;
    public static defaultUploadMsec = 10000;
    private _logger = getLogger("FeatureVectorReporter");

    constructor(
        private _apiServer: APIServer,
        private _context: vscode.ExtensionContext,
        maxRecords?: number,
        uploadMs?: number,
        batchSize?: number
    ) {
        super(
            "FeatureVectorReporter",
            maxRecords ?? FeatureVectorReporter.defaultMaxRecords,
            uploadMs ?? FeatureVectorReporter.defaultUploadMsec,
            batchSize ?? FeatureVectorReporter.defaultBatchSize
        );
    }

    // reportEvent reports a user next edit event.
    public reportVector(): void {
        const featureVector = createFeatures(this._context);
        super.report(featureVector.toVector());
    }

    protected performUpload(batch: FeatureVector[]): Promise<void> {
        if (batch.length > 1) {
            this._logger.debug(`unexpected batch size: ${batch.length}`);
            return Promise.resolve();
        }
        return this._apiServer.logFeatureVector(batch[0]);
    }
}
