import { APIServer } from "$vscode/src/augment-api";
import { AugmentConfigListener } from "$vscode/src/augment-config-listener";
import { GuidelinesWatcher } from "$vscode/src/chat/guidelines-watcher";
import { FeatureFlagManager } from "$vscode/src/feature-flags";
import { WorkTimer } from "$vscode/src/metrics/work-timer";
import { AugmentGlobalState } from "$vscode/src/utils/context";
import { PanelWebviewBase } from "$vscode/src/utils/panel-webview-base";
import { createAsyncMsgHandlerFromWebview } from "$vscode/src/utils/webviews/messaging-helper";
import { ToolConfigStore } from "$vscode/src/webview-panels/stores/tool-config-store";
import { WebviewManager } from "$vscode/src/webview-providers/webview-manager";
import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";
import { WorkspaceManager } from "$vscode/src/workspace/workspace-manager";

import * as vscode from "vscode";

import { SharedWebviewStore } from "../stores/shared-webview-store";
import { ChatHomeWebviewState, SHARED_AGENT_STORE_NAME } from "./common-webview-store";
import { RemoteAgentsMessenger } from "./remote-agents-messenger";

interface IRemoteAgentHomePanelDeps {
    extensionUri: vscode.Uri;
    apiServer: APIServer;
    workTimer: WorkTimer;
    globalState: AugmentGlobalState;
    toolConfigStore: ToolConfigStore;
    configListener: AugmentConfigListener;
    guidelinesWatcher: GuidelinesWatcher;
    workspaceManager: WorkspaceManager;
    extensionContext: vscode.ExtensionContext;
    featureFlagManager: FeatureFlagManager;
}

export class RemoteAgentHomePanel extends PanelWebviewBase {
    /**
     * Track the current panel. We only allow a single home panel to exist at a time.
     */
    public static currentPanel: RemoteAgentHomePanel | undefined;
    public static title = "Remote Agents";
    public static messengerId = "remote-agent-home-panel-messenger";

    private readonly _panel: vscode.WebviewPanel;

    private _sharedStore: SharedWebviewStore<ChatHomeWebviewState>;
    private _remoteAgentsMessenger: RemoteAgentsMessenger;

    constructor(private readonly _deps: IRemoteAgentHomePanelDeps) {
        const panel = vscode.window.createWebviewPanel(
            "remote agent home",
            RemoteAgentHomePanel.title,
            vscode.ViewColumn.Active,
            {
                retainContextWhenHidden: true,
                enableScripts: true,
            }
        );
        super("remote-agent-home.html", panel.webview);
        this._panel = panel;

        const { extensionUri } = this._deps;
        this._panel.iconPath = {
            light: vscode.Uri.joinPath(extensionUri, "media", "panel-icon-light.svg"),
            dark: vscode.Uri.joinPath(extensionUri, "media", "panel-icon-dark.svg"),
        };

        this._panel.onDidDispose(() => {
            WebviewManager.getInstance().broadcastMessage({
                type: WebViewMessageType.closeRemoteAgentHomePanel,
            });
            this.dispose();
        });

        this.addDisposables(
            this._panel,
            new vscode.Disposable(() => {
                RemoteAgentHomePanel.currentPanel = undefined;
            })
        );

        const asyncMsgHandler = createAsyncMsgHandlerFromWebview(
            this._panel.webview,
            this._deps.workTimer
        );
        this.addDisposable(asyncMsgHandler);

        this._remoteAgentsMessenger = new RemoteAgentsMessenger(
            this._deps.apiServer,
            this._deps.extensionUri,
            this._deps.workTimer,
            this._deps.globalState,
            this._deps.toolConfigStore,
            this._deps.configListener,
            this._deps.guidelinesWatcher,
            this._deps.workspaceManager,
            this._deps.extensionContext,
            this._deps.featureFlagManager
        );
        this._remoteAgentsMessenger.register(asyncMsgHandler, this._panel.webview);
        this.addDisposable(this._remoteAgentsMessenger);

        this._sharedStore =
            SharedWebviewStore.getStore<ChatHomeWebviewState>(SHARED_AGENT_STORE_NAME);
        this._sharedStore.subscribe(
            RemoteAgentHomePanel.messengerId,
            this._panel.webview,
            asyncMsgHandler
        );
        this.addDisposable(
            new vscode.Disposable(() => {
                if (this._sharedStore) {
                    this._sharedStore.updateState((state) => {
                        if (!state) {
                            return;
                        }
                        return {
                            ...state,
                            activeWebviews: state.activeWebviews.filter((view) => view !== "home"),
                        };
                    });
                    this._sharedStore.unsubscribe(RemoteAgentHomePanel.messengerId);
                }
            })
        );

        void this.loadHTML(extensionUri);
    }

    public static createOrShow(deps: IRemoteAgentHomePanelDeps) {
        WebviewManager.getInstance().broadcastMessage({
            type: WebViewMessageType.showRemoteAgentHomePanel,
        });
        if (RemoteAgentHomePanel.currentPanel) {
            RemoteAgentHomePanel.currentPanel._panel.reveal(vscode.ViewColumn.Active);
            return;
        }
        RemoteAgentHomePanel.currentPanel = new RemoteAgentHomePanel(deps);
    }

    public static close() {
        const webviewManager = WebviewManager.getInstance();
        webviewManager.broadcastMessage({
            type: WebViewMessageType.closeRemoteAgentHomePanel,
        });
        if (RemoteAgentHomePanel.currentPanel) {
            RemoteAgentHomePanel.currentPanel._panel.dispose();
        }
    }
}
