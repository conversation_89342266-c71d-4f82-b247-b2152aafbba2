import { AggregateCheckpointManager } from "@augment-internal/sidecar-libs/src/agent/checkpoint/aggregate-checkpoint-manager";
import { Blobs } from "@augment-internal/sidecar-libs/src/api/types";
import {
    limitChatHistoryTruncate,
    limitChatHistoryWindow,
} from "@augment-internal/sidecar-libs/src/chat/chat-truncation";
import {
    ChatMode,
    type ChatRequestNode,
    ChatRequestNodeType,
    EditEventSource,
    Exchange,
    ImageFormatType,
    PersonaType,
    ReplacementText,
    Rule,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { checkpointToEditEventsNode } from "@augment-internal/sidecar-libs/src/chat/checkpoint-to-edit-events";
import { ChatResult } from "@augment-internal/sidecar-libs/src/client-interfaces/api-client-types";
import { ToolDefinition } from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { VCSChange } from "@augment-internal/sidecar-libs/src/vcs/watcher/types";
import { IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
import * as vscode from "vscode";

import type { APIServer } from "../augment-api";
import { AssetManager } from "../client-interfaces/asset-manager";
import { FeatureFlagManager } from "../feature-flags";
import { getLogger } from "../logging";
import { IAugmentGlobalState, WriteBackCacheKey } from "../utils/context";
import CopyableGenerator from "../utils/copyable-generator";
import { DisposableService } from "../utils/disposable-service";
import { getDiagnostics } from "../utils/editor";
import { isExtensionVersionGte } from "../utils/environment";
import { relativePathName } from "../utils/path-utils";
import { type RecentItems } from "../utils/recent-items";
import { makeReplacementText } from "../utils/replacement-text";
import { type SelectedCodeDetails } from "../utils/types";
import { OrderedWorkQueue } from "../utils/work-queue";
import type {
    ChatGetStreamRequest,
    ChatModelReply,
    ChatRatingDoneMessage,
    ChatRatingMessage,
    GetDiagnosticsRequest,
    GetDiagnosticsResponse,
    ResolveWorkspaceFileChunkRequest,
    ResolveWorkspaceFileChunkResponse,
    SaveChatDoneMessage,
    SaveChatMessage,
    WorkspaceFileChunk,
} from "../webview-providers/webview-messages";
import { WebViewMessageType } from "../webview-providers/webview-messages";
import { WorkspaceManager } from "../workspace/workspace-manager";
import { pathRangeToFileDetails } from "./fuzzy-fs-searcher";
import { FuzzySymbolSearcher } from "./fuzzy-symbol-searcher";
import { WriteBackCache } from "./write-back-cache";

// Note(yuri): we add `PreferenceParams` and `PreferenceState` just to encapsulate
// preference-related logic and not entangle it too much with the rest of the code.
export interface PreferenceParams {
    useOptionB: boolean;
    disableAutoExternalSources?: boolean;
}

export class PreferenceState {
    public _selectedCodeReferenceRequestId: string | undefined = undefined;
    public _selectedCodeReferenceRequestIdOptionB: string | undefined = undefined;
    public _selectedCode: string | undefined = undefined;
    public _filePath: string | undefined = undefined;

    _maybeUpdateSelectedCodeReferenceRequestId(
        request: ChatRequest,
        selectedCodeDetails?: SelectedCodeDetails | null | undefined,
        optionB?: boolean
    ): string | undefined {
        let refReqId = optionB
            ? this._selectedCodeReferenceRequestIdOptionB
            : this._selectedCodeReferenceRequestId;
        if (refReqId === undefined && selectedCodeDetails) {
            if (optionB) {
                this._selectedCodeReferenceRequestIdOptionB = request.requestId;
            } else {
                this._selectedCodeReferenceRequestId = request.requestId;
            }
            refReqId = ChatModel._newRequestReferenceId;
        }

        return refReqId;
    }
}

export default class ChatModel extends DisposableService {
    private _chatStreams = new Map<string, CopyableGenerator<ChatModelReply>>();

    // This is the request ID of the request that initially referenced the currently
    // selected code. If the selection changes, this is reset to undefined, since there is
    // no existing request ID referencing the new selection. Once a new request is sent,
    // this is set to the new request ID.
    // See //services/api_proxy:public_api.proto
    public static _newRequestReferenceId: string = "new";
    private _selectedCodeReferenceRequestId: string | undefined;
    public _preferenceState: PreferenceState = new PreferenceState();
    public selectionCache: WriteBackCache<SelectedCodeDetails>;

    constructor(
        private readonly _globalState: IAugmentGlobalState,
        private _apiServer: APIServer,
        private _workspaceManager: WorkspaceManager,
        private _recentChats: RecentItems<ChatRequest>,
        private readonly _fuzzySymbolSearcher: FuzzySymbolSearcher,
        private readonly _assetManager: AssetManager,
        private readonly _featureFlagManager: FeatureFlagManager,
        private readonly _checkpointManager?: AggregateCheckpointManager
    ) {
        super();
        this.selectionCache = new WriteBackCache(
            this._globalState,
            WriteBackCacheKey.requestIdSelectionMetadata,
            { lru: { max: 1000 } }
        );
        this.addDisposables(
            vscode.window.onDidChangeTextEditorSelection(() => {
                this._selectedCodeReferenceRequestId = undefined;
            }),
            { dispose: () => this._chatStreams.clear() },
            this.selectionCache
        );
    }

    getDiagnostics = (_request: GetDiagnosticsRequest): GetDiagnosticsResponse => {
        return {
            type: WebViewMessageType.getDiagnosticsResponse,
            data: getDiagnostics(),
        };
    };

    /**
     * Checks if the selected code request ID is in the chat history of the request.
     * - If there is no current referenced ID, but a selection exists, the request ID
     *  is updated to the new request ID and "new" is returned
     * - Otherwise, we return the tracked request ID. Note that it does not matter if the request
     *  ID is in the history or not -- the prompt formatter will check that condition and format
     *  the prompt appropriately
     *
     * @param request ChatRequest
     * @returns string | undefined
     */
    private _maybeUpdateSelectedCodeReferenceRequestId(
        request: ChatRequest,
        selectedCodeDetails?: SelectedCodeDetails | null | undefined
    ): string | undefined {
        let refReqId = this._selectedCodeReferenceRequestId;
        // If there is no current referenced ID, but a selection exists, the request ID
        // is updated to the new request ID and "new" is returned
        if (refReqId === undefined && selectedCodeDetails) {
            this._selectedCodeReferenceRequestId = request.requestId;
            refReqId = ChatModel._newRequestReferenceId;
            void this.selectionCache.set(request.requestId, selectedCodeDetails);
        }
        return refReqId;
    }

    private async _getVCSChange(): Promise<VCSChange> {
        if (this._workspaceManager !== undefined) {
            return await this._workspaceManager.getVCSChange();
        } else {
            return { commits: [], workingDirectory: [] };
        }
    }

    async chat(
        request: ChatRequest,
        selection?: SelectedCodeDetails | null | undefined,
        preferenceParams?: PreferenceParams
    ): Promise<ChatResult> {
        this._recentChats.addItem(request);
        const logger = getLogger("ChatModel");
        logger.debug(`Sending chat request with ID: ${request.requestId}`);

        let refReqId;
        if (preferenceParams === undefined) {
            // Active when preference collection is disabled
            refReqId = this._maybeUpdateSelectedCodeReferenceRequestId(request, selection);
        } else {
            // Active when preference collection is enabled
            refReqId = this._preferenceState._maybeUpdateSelectedCodeReferenceRequestId(
                request,
                selection,
                preferenceParams.useOptionB
            );
            if (selection?.selectedCode) {
                this._preferenceState._selectedCode = selection.selectedCode;
                this._preferenceState._filePath = selection.path;
            }
            if (request.selectedCode) {
                this._preferenceState._selectedCode = request.selectedCode;
                this._preferenceState._filePath = request.pathName;
            }
        }

        const vcsChange = await this._getVCSChange();
        const recentChanges: ReplacementText[] = this._getRecentChanges();

        const response = await this._apiServer.chat(
            request.requestId,
            request.message,
            request.chatHistory,
            request.blobs,
            request.userGuidedBlobs,
            request.externalSourceIds,
            request.modelId,
            vcsChange,
            recentChanges,
            refReqId,
            selection?.selectedCode || request.selectedCode,
            selection?.prefix || request.prefix,
            selection?.suffix || request.suffix,
            selection?.path || request.pathName,
            selection?.language || request.language,
            undefined, // sessionId
            undefined, // disableAutoExternalSources
            request.userGuidelines,
            request.workspaceGuidelines,
            request.toolDefinitions,
            request.nodes,
            request.mode,
            request.agentMemories,
            request.personaType
        );
        response.workspaceFileChunks = mergeAndFilterWorkspaceFileChunks(
            response.workspaceFileChunks || []
        );
        return response;
    }

    public getReferenceReqId(): string | undefined {
        return this._selectedCodeReferenceRequestId;
    }

    /**
     * Stop following the chat stream for the specified request ID
     * @param requestId
     */
    cancelChatStream = (requestId: string): void => {
        // If there is an active stream in the extension, we cancel it now
        // to avoid waiting for a response
        const chatStream = this._chatStreams.get(requestId);
        chatStream?.cancel();
    };

    async *getChatStream(msg: ChatGetStreamRequest): AsyncGenerator<ChatModelReply> {
        const chatStream = this._chatStreams.get(msg.data.requestId);
        if (chatStream) {
            yield* chatStream.copy();
        } else {
            throw new Error(`Chat stream with ID ${msg.data.requestId} not found`);
        }
    }

    deleteChatStream = (requestId: string): void => {
        this._chatStreams.get(requestId)?.cancel();
        this._chatStreams.delete(requestId);
    };

    async *chatStream(
        request: ChatRequest,
        selectedCodeDetails?: SelectedCodeDetails | null | undefined,
        preferenceParams?: PreferenceParams
    ): AsyncIterable<ChatModelReply> {
        this._recentChats.addItem(request);
        const chatStream = this._chatStreams.get(request.requestId);
        yield* chatStream?.copy() ??
            this.startChatStream(request, selectedCodeDetails, preferenceParams);
    }

    private _getRecentChanges(): ReplacementText[] {
        const workspaceContext = this._workspaceManager.getContext();
        return makeReplacementText(workspaceContext.recentChunks);
    }

    /**
     * Get the details of the files that the workspace knows about and those that it doesn't
     *  so we can display it in the frontend
     *
     * @param msg: ResolveWorkspaceFileChunkRequest
     * @returns
     */
    resolveWorkspaceFileChunk = async (
        msg: ResolveWorkspaceFileChunkRequest
    ): Promise<ResolveWorkspaceFileChunkResponse> => {
        const chunk: WorkspaceFileChunk = msg.data;
        const pathName: IQualifiedPathName | undefined = this._workspaceManager.getAllPathNames(
            chunk.blobName
        )[0];
        if (!pathName) {
            throw new Error(`File chunk with blobName=${chunk.blobName} not found`);
        }
        return {
            type: WebViewMessageType.resolveWorkspaceFileChunkResponse,
            data: await pathRangeToFileDetails(pathName, chunk),
        };
    };

    private async *startChatStream(
        request: ChatRequest,
        selectedCodeDetails?: SelectedCodeDetails | null | undefined,
        preferenceParams?: PreferenceParams
    ): AsyncIterable<ChatModelReply> {
        const requestId = request.requestId;
        let chatStream: CopyableGenerator<ChatModelReply> | undefined =
            this._chatStreams.get(requestId);
        if (chatStream) {
            throw new Error(`Chat stream with ID ${requestId} already exists`);
        } else {
            const logger = getLogger("ChatModel");
            logger.debug(`Sending chat stream request with ID: ${request.requestId}`);
            let refReqId;
            if (preferenceParams === undefined) {
                // Active when preference collection is disabled
                refReqId = this._maybeUpdateSelectedCodeReferenceRequestId(
                    request,
                    selectedCodeDetails
                );
            } else {
                // Active when preference collection is enabled
                refReqId = this._preferenceState._maybeUpdateSelectedCodeReferenceRequestId(
                    request,
                    selectedCodeDetails,
                    preferenceParams.useOptionB
                );
                if (selectedCodeDetails?.selectedCode) {
                    this._preferenceState._selectedCode = selectedCodeDetails.selectedCode;
                    this._preferenceState._filePath = selectedCodeDetails.path;
                }
                if (request.selectedCode) {
                    this._preferenceState._selectedCode = request.selectedCode;
                    this._preferenceState._filePath = request.pathName;
                }
            }
            const vcsChange = await this._getVCSChange();
            const recentChanges = this._getRecentChanges();

            const pathName = selectedCodeDetails?.path || request.pathName;
            const qpn = pathName
                ? this._workspaceManager.getAllQualifiedPathNames(pathName)[0]
                : undefined;

            const prefix = selectedCodeDetails?.prefix || request.prefix;
            const suffix = selectedCodeDetails?.suffix || request.suffix;
            const language = selectedCodeDetails?.language || request.language;
            const selectedCode = selectedCodeDetails?.selectedCode || request.selectedCode;

            // Some request nodes may need to be hydrated with data from the extension, such as image ID nodes
            const hydratedHistory = await this.hydrateChatHistory(request.chatHistory);
            const history = this.limitChatHistory(hydratedHistory);
            const nodes = request.nodes ? await this.hydrateRequestNodes(request.nodes) : [];

            // Attach out-of-band user edits to the current request, if any exist
            if (this._checkpointManager && !request.silent) {
                // Check if this is a tool response message by looking for TOOL_RESULT nodes
                const isToolResponse = nodes.some(
                    (node) => node.type === ChatRequestNodeType.TOOL_RESULT
                );

                // For current user messages (not tool responses), associate unseen edits with this request
                if (!isToolResponse) {
                    const userEdits =
                        await this._checkpointManager.pullAllUserModifiedChanges(requestId);
                    if (userEdits.files.length > 0) {
                        const node = checkpointToEditEventsNode(
                            0,
                            userEdits,
                            () => ({}),
                            EditEventSource.USER_EDIT
                        );
                        nodes.push(node);
                    }
                }
            }

            const folderRoot = qpn
                ? this._workspaceManager.getFolderRoot(qpn?.absPath)
                : this._workspaceManager.getMostRecentlyChangedFolderRoot();
            const repoRoot = folderRoot
                ? this._workspaceManager.getRepoRootForFolderRoot(folderRoot)
                : undefined;
            const relativeFolderRoot = relativePathName(repoRoot ?? "", folderRoot ?? "");
            if (relativeFolderRoot !== "" && relativeFolderRoot !== ".") {
                // See the comment in `handleNonRootWorkspacePath` for why this is
                // necessary.
                request = handleNonRootWorkspacePath(request, relativeFolderRoot);
            }

            const baseStream = await this._apiServer.chatStream(
                request.requestId,
                request.message,
                history,
                request.blobs,
                request.userGuidedBlobs,
                request.externalSourceIds,
                request.modelId,
                vcsChange,
                recentChanges,
                refReqId,
                selectedCode,
                prefix,
                suffix,
                pathName,
                language,
                preferenceParams && preferenceParams.useOptionB
                    ? this._apiServer.sessionId + "-B"
                    : undefined,
                preferenceParams?.disableAutoExternalSources,
                request.userGuidelines,
                request.workspaceGuidelines,
                request.toolDefinitions,
                nodes,
                request.mode,
                request.agentMemories,
                request.personaType,
                request.rules,
                request.silent,
                isExtensionVersionGte(
                    this._featureFlagManager.currentFlags.vscodeSupportToolUseStartMinVersion ?? ""
                )
            );

            // Parse out the current workspace file chunk we are looking at, if it exists
            // We use this for symbol pre-loading and sources resolution
            let currFileChunk: WorkspaceFileChunk | undefined;
            if (selectedCodeDetails?.path && qpn) {
                currFileChunk = {
                    charStart: prefix?.length ?? 0,
                    charEnd: (prefix?.length ?? 0) + (selectedCode?.length ?? 0),
                    blobName: this._workspaceManager.getBlobName(qpn) ?? "",
                };
            } else {
                currFileChunk = undefined;
            }

            // This is where we actually handle the data coming from the backend and
            // make it copyable -- we add the sources here so all downstream
            // consumers of this chat stream will receive the merged stream.
            chatStream = new CopyableGenerator(
                processIncomingChatStream(
                    requestId,
                    currFileChunk,
                    baseStream,
                    async (chunk: WorkspaceFileChunk): Promise<WorkspaceFileChunk> => {
                        try {
                            const fileResponse = await this.resolveWorkspaceFileChunk({
                                type: WebViewMessageType.resolveWorkspaceFileChunkRequest,
                                data: chunk,
                            });

                            // Warm up the symbol cache for this blob name. Don't need to block on it.
                            void this._fuzzySymbolSearcher.warmupCache(chunk.blobName);
                            return { ...chunk, file: fileResponse.data };
                        } catch {
                            return chunk;
                        }
                    }
                )
            );
            this._chatStreams.set(requestId, chatStream);
            yield* chatStream.copy();
        }
    }

    public sendFeedback = async (msg: ChatRatingMessage): Promise<ChatRatingDoneMessage> => {
        await this._apiServer.chatFeedback(msg.data);
        return {
            type: WebViewMessageType.chatRatingDone,
            data: msg.data,
        };
    };

    public saveConversation = async (msg: SaveChatMessage): Promise<SaveChatDoneMessage> => {
        const logger = getLogger("ChatModel");
        let response = await this._apiServer.saveChat(
            msg.data.conversationId,
            msg.data.chatHistory,
            msg.data.title
        );
        logger.debug(`Saved chat with api server: ${response.url}`);
        return {
            type: WebViewMessageType.saveChatDone,
            data: response,
        };
    };

    private hydrateRequestNodes = async (nodes: ChatRequestNode[]): Promise<ChatRequestNode[]> => {
        const logger = getLogger("ChatModel");
        const hydratedNodes = await Promise.all(
            nodes.map(async (node) => {
                // The backend expects a base64 image, so we need to convert the image_id to an image
                if (node.type === ChatRequestNodeType.IMAGE_ID) {
                    if (!node.image_id_node) {
                        logger.error("Invalid image_id_node: missing image_id_node");
                        return node;
                    }
                    const imageId = node.image_id_node.image_id;
                    const buffer = await this._assetManager.loadAsset(imageId ?? "");
                    if (!buffer) {
                        logger.error(`Failed to load asset ${imageId}`);
                        // If the extension does not have the image, let the backend handle it.
                        return node;
                    }
                    const data = Buffer.from(buffer)
                        .toString("base64")
                        .replace(/^data:.*?;base64,/, "");
                    return {
                        type: ChatRequestNodeType.IMAGE,
                        id: node.id,
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        image_node: {
                            // eslint-disable-next-line @typescript-eslint/naming-convention
                            image_data: data,
                            format: ImageFormatType.PNG,
                        },
                    };
                }
                // Handle checkpoint reference nodes by hydrating them with actual diff information
                else if (
                    node.type === ChatRequestNodeType.CHECKPOINT_REF &&
                    this._checkpointManager
                ) {
                    // Get the checkpoint reference information
                    const checkpointRef = node.checkpoint_ref_node;
                    if (!checkpointRef) {
                        return undefined;
                    }

                    const requestId = checkpointRef.request_id;
                    const fromTimestamp = checkpointRef.from_timestamp;
                    const toTimestamp = checkpointRef.to_timestamp;

                    // If we have valid checkpoint reference information
                    try {
                        // First try to get the checkpoint by request ID
                        let checkpoint =
                            await this._checkpointManager.getCheckpointByRequestId(requestId);

                        // If that fails, try to get it by timestamp range
                        if (!checkpoint) {
                            checkpoint = await this._checkpointManager.getAggregateCheckpoint({
                                minTimestamp: fromTimestamp,
                                maxTimestamp: toTimestamp,
                            });
                        }

                        // If we found a checkpoint, create a new edit events node with the actual diff information
                        if (checkpoint && checkpoint.files.length > 0) {
                            // Pass the source information directly from the checkpoint_ref_node
                            // This is set in the webview when creating the dehydrated checkpoint node
                            return checkpointToEditEventsNode(
                                node.id,
                                checkpoint,
                                () => {
                                    // Get blob names for the document if needed
                                    return {
                                        beforeBlobName: undefined,
                                        afterBlobName: undefined,
                                    };
                                },
                                checkpointRef.source
                            );
                        }
                        return undefined;
                    } catch (error) {
                        logger.error(
                            `Failed to hydrate checkpoint reference node: ${String(error)}`
                        );
                    }
                }
                return node;
            })
        );
        return hydratedNodes.filter((node) => node !== undefined);
    };

    /**
     * Hydrate the chat history by hydrating each request node in the history
     *
     * Hydrating is processing nodes and adding relevant data or information that
     * it may need.
     *
     * @param history The chat history to hydrate
     * @returns The hydrated chat history
     */
    public hydrateChatHistory = async (history: Exchange[]) => {
        const outOfBandEdits = await this._checkpointManager?.getAllUserModifiedChanges();
        const result = await Promise.all(
            history.map(async (exchange) => {
                if (exchange.request_nodes) {
                    exchange.request_nodes = await this.hydrateRequestNodes(exchange.request_nodes);

                    // Add edit nodes for historical messages that have associated edits
                    const userEdits = outOfBandEdits?.get(exchange.request_id);
                    if (
                        this._checkpointManager &&
                        exchange.request_id &&
                        userEdits &&
                        userEdits.files.length > 0
                    ) {
                        const editNode = checkpointToEditEventsNode(
                            exchange.request_nodes.length,
                            userEdits,
                            () => ({}),
                            EditEventSource.USER_EDIT
                        );
                        exchange.request_nodes.push(editNode);
                    }

                    // Update the IDs to be consecutive
                    for (let i = 0; i < exchange.request_nodes.length; i++) {
                        exchange.request_nodes[i].id = i;
                    }
                }
                return exchange;
            })
        );
        return result;
    };

    public limitChatHistory = (history: Exchange[]) => {
        if (
            isExtensionVersionGte(
                this._featureFlagManager.currentFlags.vscodeChatStablePrefixTruncationMinVersion
            )
        ) {
            return limitChatHistoryTruncate(history);
        }
        return limitChatHistoryWindow(history);
    };
}

export type ChatRequest = {
    requestId: string;
    message: string;
    chatHistory: Exchange[];
    userGuidedBlobs: string[];
    externalSourceIds: string[];
    blobs: Blobs;
    selectedCode?: string;
    prefix?: string;
    suffix?: string;
    pathName?: string;
    language?: string;
    modelId?: string | undefined;
    userGuidelines?: string;
    workspaceGuidelines?: string;
    toolDefinitions: ToolDefinition[];
    nodes?: ChatRequestNode[];
    mode?: ChatMode;
    agentMemories?: string;
    agentTasks?: string;
    personaType?: PersonaType;
    rules?: Rule[];
    silent?: boolean;
};

// Utilities used only in this file

/**
 * Processes an incoming chat stream and merges workspace file chunks
 * As context is processed, we merge it with the context we know about, and
 * only yield new chunks at the very end
 * @param stream
 */
export async function* processIncomingChatStream(
    requestId: string,
    currFileChunk: WorkspaceFileChunk | undefined,
    stream: AsyncIterable<ChatResult>,
    resolveChunk: (chunk: WorkspaceFileChunk) => Promise<WorkspaceFileChunk>
): AsyncGenerator<ChatModelReply> {
    const unyieldedChunks: Set<WorkspaceFileChunk> = new Set();
    // original, resolved
    let fileChunkQueue: [WorkspaceFileChunk, WorkspaceFileChunk][] = [];
    const resolveWorkQueue: OrderedWorkQueue<WorkspaceFileChunk> = new OrderedWorkQueue(
        async (chunk: WorkspaceFileChunk | undefined): Promise<void> => {
            if (chunk === undefined) {
                return;
            }
            const pair: [WorkspaceFileChunk, WorkspaceFileChunk] = [
                chunk,
                await resolveChunk(chunk),
            ];
            fileChunkQueue.push(pair);
        }
    );

    // Add the current file chunk to the unyielded chunks and queue it for resolution
    if (currFileChunk) {
        unyieldedChunks.add(currFileChunk);
        resolveWorkQueue.insert(currFileChunk);
    }
    void resolveWorkQueue.kick();

    for await (const chunk of stream) {
        // Get merged + filtered chunks, and queue them for resolution
        const chunksToResolve = mergeAndFilterWorkspaceFileChunks(chunk.workspaceFileChunks ?? []);
        chunksToResolve.forEach((chunk: WorkspaceFileChunk) => {
            unyieldedChunks.add(chunk);
            resolveWorkQueue.insert(chunk);
        });
        // Kick the work queue to start resolving chunks
        void resolveWorkQueue.kick();

        // Yield all resolved chunks from the queue and remove them from unyielded chunks
        const chunksToYield = [...fileChunkQueue];
        chunksToYield.forEach(([original, _]: [WorkspaceFileChunk, WorkspaceFileChunk]) => {
            unyieldedChunks.delete(original);
        });
        fileChunkQueue = [];

        yield {
            type: WebViewMessageType.chatModelReply,
            data: {
                text: chunk.text,
                requestId,
                streaming: true,
                workspaceFileChunks: chunksToYield.map(([_, resolved]) => resolved),
                nodes: chunk.nodes,
            },
        };
    }
    const mergedChunks = await Promise.all([
        ...mergeAndFilterWorkspaceFileChunks([...unyieldedChunks]).map(resolveChunk),
    ]);
    yield {
        type: WebViewMessageType.chatModelReply,
        data: {
            text: "",
            requestId,
            streaming: true,
            workspaceFileChunks: mergedChunks,
        },
    };
}

/**
 * Merges and filters workspace file chunks
 * Removes all chunks that are empty, and merges adjacent chunks
 *
 * @param chunks
 * @returns
 */
export function mergeAndFilterWorkspaceFileChunks(
    chunks: WorkspaceFileChunk[]
): WorkspaceFileChunk[] {
    // Group chunks by blob name
    const blobNameToChunks: Map<string, WorkspaceFileChunk[]> = new Map();
    for (const chunk of chunks) {
        // Skip empty chunks
        if (chunk.charStart >= chunk.charEnd) {
            continue;
        }
        const chunks = blobNameToChunks.get(chunk.blobName) ?? [];
        chunks.push(chunk);
        blobNameToChunks.set(chunk.blobName, chunks);
    }

    const mergedChunks: WorkspaceFileChunk[] = [];
    for (const [_, chunks] of blobNameToChunks) {
        // Order chunks by start position and merge adjacent ones
        const newChunks = chunks
            .sort((a, b) => a.charStart - b.charStart)
            .reduce((acc: WorkspaceFileChunk[], chunk: WorkspaceFileChunk) => {
                const prevChunk = acc[acc.length - 1];
                // If chunks overlap, merge them
                if (prevChunk && prevChunk.charEnd + 1 >= chunk.charStart) {
                    prevChunk.charEnd = chunk.charEnd;
                    // Otherwise, add a new one
                } else {
                    acc.push(chunk);
                }
                return acc;
            }, []);
        mergedChunks.push(...newChunks);
    }
    return mergedChunks;
}

const AGENT_NON_WORKSPACE_ROOT_MEMORY =
    "- The user is working from the directory `${relPath}`.\n\
- When the user mentions a file name or when viewing output from shell commands, it is likely relative to `${relPath}`.\n\
- When creating, deleting, viewing or editing files, first try prepending `${relPath}` to the path.\n\
- When running shell commands, do not prepend `${relPath}` to the path.\n";

// NOTE(arun): This is a gnarly hack to handle the case where the user is
// in a subdirectory of their repository: our retrieval and read/write file
// tools all use paths relative to the repository root and we need to tell
// the model how to handle this. Unfortunately, the backend doesn't know
// anything about the users workspace; in the medium term, we need to share
// this information with the backend so we can handle it there. For now,
// we are hacking it into agent memories.
// One consequence of this approach is that when users do change folder roots, we will
// end up blowing out their cache. We've discussed adding the path change as a new node
// in the conversation history.
export function handleNonRootWorkspacePath(request: ChatRequest, relPath: string) {
    if (!request.agentMemories) {
        request.agentMemories = "";
    } else {
        request.agentMemories += "\n";
    }
    request.agentMemories += AGENT_NON_WORKSPACE_ROOT_MEMORY.replaceAll("${relPath}", relPath);
    return request;
}
