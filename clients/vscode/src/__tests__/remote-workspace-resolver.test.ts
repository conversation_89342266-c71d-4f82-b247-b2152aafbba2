import * as vscode from "vscode";

import { mockFSUtils } from "../__mocks__/fs-utils";
import { resetMockWorkspace } from "../__mocks__/vscode-mocks";
import { APIServer } from "../augment-api";
import { FeatureFlagManager } from "../feature-flags";
import { RemoteAgent, RemoteAgentStatus } from "../remote-agent-manager/types";
import { RemoteWorkspaceResolver } from "../remote-workspace-resolver";
import { ListRemoteAgentsResponse } from "./../remote-agent-manager/types";

describe("RemoteWorkspaceResolver", () => {
    let apiServer: APIServer;
    let featureFlagManager: FeatureFlagManager;
    let remoteWorkspaceResolver: RemoteWorkspaceResolver;
    let mockRemoteAgents: RemoteAgent[];
    let mockListRemoteAgentsResponse: ListRemoteAgentsResponse;

    // Setup before each test
    beforeEach(() => {
        jest.useFakeTimers();
        resetMockWorkspace();
        mockFSUtils.reset();

        // Mock API Server
        apiServer = {
            listRemoteAgents: jest.fn(),
        } as unknown as APIServer;

        // Mock Feature Flag Manager
        featureFlagManager = {
            currentFlags: {
                vscodeBackgroundAgentsMinVersion: "1.0.0",
            },
            subscribe: jest.fn().mockReturnValue({ dispose: jest.fn() }),
        } as unknown as FeatureFlagManager;

        // Mock remote agents
        /* eslint-disable @typescript-eslint/naming-convention */
        mockRemoteAgents = [
            {
                remote_agent_id: "test-agent-id",
                session_summary: "Test Agent Session",
                is_setup_script_agent: false,
                workspace_setup: {
                    starting_files: {
                        github_commit_ref: {
                            repository_url: "https://github.com/test/repo",
                            git_ref: "main",
                        },
                    },
                },
                status: RemoteAgentStatus.agentIdle,
                started_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                turn_summaries: [],
                workspace_status: 1,
                expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
            },
        ];

        mockListRemoteAgentsResponse = {
            remote_agents: mockRemoteAgents,
            max_remote_agents: 100,
            max_active_remote_agents: 10,
        };
        /* eslint-enable @typescript-eslint/naming-convention */

        // Mock the API response
        (apiServer.listRemoteAgents as jest.Mock).mockResolvedValue(mockListRemoteAgentsResponse);

        // Create the resolver instance
        remoteWorkspaceResolver = RemoteWorkspaceResolver.initialize(apiServer, featureFlagManager);
    });

    afterEach(() => {
        remoteWorkspaceResolver.dispose();
        RemoteWorkspaceResolver["_instance"] = undefined;
        jest.useRealTimers();
    });

    describe("initialization", () => {
        test("should create a singleton instance", () => {
            const instance1 = RemoteWorkspaceResolver.initialize(apiServer, featureFlagManager);
            const instance2 = RemoteWorkspaceResolver.initialize(apiServer, featureFlagManager);

            expect(instance1).toBe(instance2);
            expect(RemoteWorkspaceResolver.getInstance()).toBe(instance1);
        });
    });

    describe("getAgentIdFromWorkspaceName", () => {
        test("should extract agent ID from workspace name", () => {
            // Mock workspace name
            Object.defineProperty(vscode.workspace, "name", {
                get: jest.fn(() => "workspace [SSH: test-agent-id]"),
                configurable: true,
            });

            const result = remoteWorkspaceResolver["getAgentIdFromWorkspaceName"]();
            expect(result).toBe("test-agent-id");
            expect(remoteWorkspaceResolver["_agentIdFromWorkspace"]).toBe("test-agent-id");
        });

        test("should return empty string if no match found", () => {
            // Mock workspace name with no agent ID
            Object.defineProperty(vscode.workspace, "name", {
                get: jest.fn(() => "workspace"),
                configurable: true,
            });

            const result = remoteWorkspaceResolver["getAgentIdFromWorkspaceName"]();
            expect(result).toBe("");
            expect(remoteWorkspaceResolver["_agentIdFromWorkspace"]).toBe("");
        });
    });

    describe("checkIfRemoteAgent", () => {
        test("should identify a remote agent workspace", async () => {
            // Mock workspace name
            Object.defineProperty(vscode.workspace, "name", {
                get: jest.fn(() => "workspace [SSH: test-agent-id]"),
                configurable: true,
            });

            // Mock the updateVSCodeSettings method
            remoteWorkspaceResolver["updateVSCodeSettings"] = jest
                .fn()
                .mockResolvedValue(undefined);

            const result = await remoteWorkspaceResolver["checkIfRemoteAgent"]();

            expect(result).toBe(true);
            expect(remoteWorkspaceResolver.isRemoteAgent()).toBe(true);
            expect(remoteWorkspaceResolver.getRemoteAgentId()).toBe("test-agent-id");
            expect(remoteWorkspaceResolver.getRemoteAgent()).toEqual(mockRemoteAgents[0]);
            expect(remoteWorkspaceResolver["updateVSCodeSettings"]).toHaveBeenCalled();
        });

        test("should return false if no agent ID in workspace name", async () => {
            // Mock workspace name with no agent ID
            Object.defineProperty(vscode.workspace, "name", {
                get: jest.fn(() => "workspace"),
                configurable: true,
            });

            const result = await remoteWorkspaceResolver["checkIfRemoteAgent"]();

            expect(result).toBe(false);
            expect(remoteWorkspaceResolver.isRemoteAgent()).toBe(false);
        });
    });

    describe("static methods", () => {
        test("static isRemoteAgent should return instance value", () => {
            remoteWorkspaceResolver["_isRemoteAgent"] = true;
            expect(RemoteWorkspaceResolver.isRemoteAgent()).toBe(true);

            remoteWorkspaceResolver["_isRemoteAgent"] = false;
            expect(RemoteWorkspaceResolver.isRemoteAgent()).toBe(false);
        });

        test("static getRemoteAgentId should return instance value", () => {
            remoteWorkspaceResolver["_remoteAgentId"] = "test-id";
            remoteWorkspaceResolver["_isRemoteAgent"] = true;
            expect(RemoteWorkspaceResolver.getRemoteAgentId()).toBe("test-id");
        });
    });

    describe("executeWithTimeout", () => {
        test("should resolve with the result if promise completes before timeout", async () => {
            const promise = Promise.resolve("success");
            const result = await remoteWorkspaceResolver["executeWithTimeout"](
                promise,
                1000,
                "Test timeout"
            );
            expect(result).toBe("success");
        });
    });

    describe("getRemoteAgentSettings", () => {
        test("should return settings with agent information", () => {
            remoteWorkspaceResolver["_remoteAgentId"] = "test-agent-id";

            // Create a properly typed RemoteAgent
            /* eslint-disable @typescript-eslint/naming-convention */
            remoteWorkspaceResolver["_remoteAgent"] = {
                remote_agent_id: "test-agent-id",
                session_summary: "Test Agent Session",
                is_setup_script_agent: false,
                status: RemoteAgentStatus.agentIdle,
                started_at: "2023-01-01T00:00:00Z",
                updated_at: "2023-01-01T00:00:00Z",
                turn_summaries: [],
                workspace_status: 1,
                expires_at: "2023-01-01T00:00:00Z",
            };
            /* eslint-enable @typescript-eslint/naming-convention */

            const settings = remoteWorkspaceResolver["getRemoteAgentSettings"]();

            // Check that the settings object has the expected properties
            expect(Object.keys(settings)).toContain("window.title");
            expect(settings["window.title"]).toContain("Test Agent Session");
        });
    });

    describe("updateSettings", () => {
        test("should try editRemoteSettingsFile first", async () => {
            // Mock the methods
            remoteWorkspaceResolver["editRemoteSettingsFile"] = jest
                .fn()
                .mockResolvedValue(undefined);
            remoteWorkspaceResolver["updateSettingsUsingCommand"] = jest
                .fn()
                .mockResolvedValue(undefined);

            await remoteWorkspaceResolver["updateSettings"]();

            expect(remoteWorkspaceResolver["editRemoteSettingsFile"]).toHaveBeenCalled();
            expect(remoteWorkspaceResolver["updateSettingsUsingCommand"]).not.toHaveBeenCalled();
        });
    });

    describe("editRemoteSettingsFile", () => {
        test("should update remote settings file", async () => {
            // Mock getRemoteSettingsFilePath
            remoteWorkspaceResolver["getRemoteSettingsFilePath"] = jest
                .fn()
                .mockReturnValue("/home/<USER>/.vscode-server/data/Machine/settings.json");

            // Mock vscode.workspace.fs
            // Since vscode.workspace.fs is a read-only property, we need to mock it differently
            const mockFs = {
                readFile: jest
                    .fn()
                    .mockResolvedValue(Buffer.from(JSON.stringify({ existingSetting: "value" }))),
                writeFile: jest.fn().mockResolvedValue(undefined),
            };
            Object.defineProperty(vscode.workspace, "fs", { value: mockFs, configurable: true });

            // Store references to the mock functions for assertions later
            const mockReadFile = mockFs.readFile;
            const mockWriteFile = mockFs.writeFile;

            // Mock window.showInformationMessage
            vscode.window.showInformationMessage = jest.fn().mockResolvedValue(undefined);

            // Set up the resolver state
            remoteWorkspaceResolver["_remoteAgentId"] = "test-agent-id";
            /* eslint-disable @typescript-eslint/naming-convention */
            remoteWorkspaceResolver["_remoteAgent"] = {
                remote_agent_id: "test-agent-id",
                session_summary: "Test Agent Session",
                is_setup_script_agent: false,
                workspace_setup: {
                    starting_files: {
                        github_commit_ref: {
                            repository_url: "https://github.com/test/repo",
                            git_ref: "main",
                        },
                    },
                },
                status: RemoteAgentStatus.agentIdle,
                started_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                turn_summaries: [],
                workspace_status: 1,
                expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
            };
            /* eslint-enable @typescript-eslint/naming-convention */

            await remoteWorkspaceResolver["editRemoteSettingsFile"]();

            // Verify the file was read and written
            expect(mockReadFile).toHaveBeenCalled();
            expect(mockWriteFile).toHaveBeenCalled();

            // Verify the content written contains both existing and new settings
            const writeCall = mockWriteFile.mock.calls[0];
            const writtenContent = Buffer.from(writeCall[1]).toString();
            const parsedContent = JSON.parse(writtenContent);

            expect(parsedContent).toHaveProperty("existingSetting", "value");
            expect(Object.keys(parsedContent)).toContain("window.title");
        });
    });
});
