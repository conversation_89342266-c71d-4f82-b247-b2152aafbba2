export declare function sha256(input: Uint8Array): string;
type vscodeCollector = {
    version: string;
    env: {
        machineId: string;
    };
};
export type FeatureVector = Record<number, string>;
export declare class Features {
    readonly vscode: string;
    readonly machineId: string;
    readonly os: string;
    readonly cpu: string;
    readonly memory: string;
    readonly numCpus: string;
    readonly hostname: string;
    readonly arch: string;
    readonly username: string;
    readonly macAddresses: string[];
    readonly osRelease: string;
    readonly kernelVersion: string;
    private _textEncoder;
    constructor(vscode: string, machineId: string, os: string, cpu: string, memory: string, numCpus: string, hostname: string, arch: string, username: string, macAddresses: string[], osRelease: string, kernelVersion: string);
    calculateChecksum(vector: FeatureVector): string;
    canonicalize(s: string): string;
    canonicalizeArray(array: string[]): string;
    toVector(): FeatureVector;
}
export declare function createFeatures(vscode: vscodeCollector): Features;
export {};