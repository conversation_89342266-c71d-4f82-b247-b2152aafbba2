package com.augmentcode.intellij.preferences
import com.augmentcode.intellij.webviews.AbstractWebviewMessagingService
import com.augmentcode.intellij.webviews.AugmentWebview
import com.augmentcode.intellij.webviews.AugmentWebviewStateKey
import com.augmentcode.intellij.webviews.WebviewFactory
import com.augmentcode.intellij.webviews.serializeProtoToJson
import com.augmentcode.rpc.ChatModelReply
import com.augmentcode.rpc.ChatStreamDoneMessage
import com.intellij.ide.plugins.UIComponentFileEditor
import com.intellij.ide.plugins.UIComponentVirtualFile
import com.intellij.openapi.fileEditor.FileEditorManagerKeys
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Disposer
import java.awt.BorderLayout
import javax.swing.JPanel

/**
 * UI component which creates and owns the preferences webview and is opened as an editor tab.
 */
class AugmentPreferencesWebviewEditorVirtualFile(
  private val project: Project,
  val preferencesMessageHandler: AbstractWebviewMessagingService,
) : UIComponentVirtualFile(
    PREFERENCES_VIRTUAL_FILE_NAME,
    null,
  ) {
  companion object {
    const val PREFERENCES_VIRTUAL_FILE_NAME = "Augment Preferences"
    const val ENTRY_FILE_PATH = "preference.html"
  }

  init {
    putUserData(FileEditorManagerKeys.FORBID_TAB_SPLIT, true)
  }

  var webview: AugmentWebview? = null

  override fun createContent(editor: UIComponentFileEditor): Content {
    Disposer.register(editor) {
      println("disposing preferences webview")
      webview = null
    }
    return Content {
      // Create a webview with a load handler for preferences
      webview =
        WebviewFactory.create(
          project,
          ENTRY_FILE_PATH,
          AugmentWebviewStateKey.PREFERENCES_STATE,
          preferencesMessageHandler,
          editor,
        )

      JPanel(BorderLayout()).apply {
        webview?.let { add(it.browser.component, BorderLayout.CENTER) }
      }
    }
  }

  fun postChatStreamingMessage(message: ChatModelReply) {
    webview?.postMessage(serializeProtoToJson(message))
  }

  fun postStreamDone() {
    val message = ChatStreamDoneMessage.newBuilder().build()
    webview?.postMessage(serializeProtoToJson(message))
  }
}
