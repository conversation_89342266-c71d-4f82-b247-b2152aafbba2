package com.augmentcode.intellij.preferences

import com.augmentcode.intellij.chat.ChatWebviewMessageClient
import com.augmentcode.intellij.pluginstate.PluginStateService
import com.augmentcode.intellij.webviews.AbstractWebviewMessagingService
import com.augmentcode.intellij.webviews.preferences.PreferencesUtils.comparisonNotAvailableMessage
import com.augmentcode.intellij.webviews.preferences.PreferencesUtils.hasSufficientComparableModels
import com.augmentcode.rpc.AsyncWrapper
import com.augmentcode.rpc.AugmentChatEntry
import com.augmentcode.rpc.ChatModelReply
import com.augmentcode.rpc.ChatModelReplyData
import com.augmentcode.rpc.ChatUserMessageRequest
import com.augmentcode.rpc.PreferenceInput
import com.augmentcode.rpc.PreferenceNotifyRequest
import com.augmentcode.rpc.PreferencePair
import com.augmentcode.rpc.PreferenceResultRequest
import com.augmentcode.rpc.PreferencesInitializeResponse
import com.augmentcode.rpc.PreferencesLoadedRequest
import com.augmentcode.rpc.WebviewPreferencesServiceGrpcKt
import com.google.protobuf.Any
import com.google.protobuf.Empty
import com.google.protobuf.Message
import com.intellij.notification.Notification
import com.intellij.notification.NotificationType
import com.intellij.notification.Notifications
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Disposer
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlin.coroutines.cancellation.CancellationException

/**
 * Wrapper for launch of preferences webview and handling of result
 */
class PreferenceLauncher(
  private val project: Project,
  private val cs: CoroutineScope,
  private val chatUserMessageRequest: ChatUserMessageRequest,
  private val requestId: String,
) : Disposable {
  private val logger = thisLogger()
  val chatService = ChatWebviewMessageClient(project)
  private lateinit var preferencesVirtualFile: AugmentPreferencesWebviewEditorVirtualFile
  private val loadedDeferred = CompletableDeferred<Unit>()
  private var resultDeferred = CompletableDeferred<PreferenceResultRequest>()

  /**
   * Implement request+response pairs from intellij-preferences.proto
   * Rather than make these instance methods on PreferenceLauncher, we make them instance methods on
   * a nested class. This allows us to inherit from WebviewPreferencesServiceGrpcKt to enforce consistency
   * with intellij-preferences.proto.
   */
  val preferenceProtoImpl =
    object : WebviewPreferencesServiceGrpcKt.WebviewPreferencesServiceCoroutineImplBase() {
      private val logger = thisLogger()

      override suspend fun preferenceNotify(request: PreferenceNotifyRequest): Empty {
        logger.info("Preference notification: ${request.message}")
        invokeLater {
          Notifications.Bus.notify(
            Notification("augment.notifications", "Preference Panel", request.message, NotificationType.INFORMATION),
            project,
          )
        }
        return Empty.getDefaultInstance()
      }

      override suspend fun preferencesLoaded(request: PreferencesLoadedRequest): PreferencesInitializeResponse {
        logger.info("Preferences webview loaded")
        loadedDeferred.complete(Unit)
        return PreferencesInitializeResponse.newBuilder()
          .setData(buildPreferencesInput(chatUserMessageRequest))
          .build()
      }

      override suspend fun preferenceResult(request: PreferenceResultRequest): Empty {
        logger.info("Preference result received: ${request.data}")
        resultDeferred.complete(request)
        return Empty.getDefaultInstance()
      }
    }

  /**
   * Route messages from preference webview to the appropriate service.
   * Rather than make the methods herein instance methods on PreferenceLauncher, we make them instance methods on
   * a nested class inheriting from AbstractWebviewMessagingService. AbstractWebviewMessagingService gives us
   * compatibility with existing webview messaging code.
   */
  val messagingHandler =
    object : AbstractWebviewMessagingService(project, cs) {
      private val logger = thisLogger()

      override suspend fun processAsyncMessage(request: AsyncWrapper): Flow<Message> {
        val baseMessage = request.baseMsg
        val response =
          processSyncMessage(baseMessage).map { responseMsg ->
            AsyncWrapper.newBuilder()
              .setRequestId(request.requestId)
              .setBaseMsg(Any.pack(responseMsg))
              .build()
          }
        return response
      }

      override fun dispose() {}

      override suspend fun processSyncMessage(request: Any): Flow<Message> {
        return when {
          request.`is`(PreferenceNotifyRequest::class.java) -> {
            val message = request.unpack(PreferenceNotifyRequest::class.java)
            flowOf(preferenceProtoImpl.preferenceNotify(message))
          }
          request.`is`(PreferencesLoadedRequest::class.java) -> {
            val message = request.unpack(PreferencesLoadedRequest::class.java)
            flowOf(preferenceProtoImpl.preferencesLoaded(message))
          }
          request.`is`(PreferenceResultRequest::class.java) -> {
            val message = request.unpack(PreferenceResultRequest::class.java)
            flowOf(preferenceProtoImpl.preferenceResult(message))
          }
          else -> {
            logger.warn("Unknown preferences message: $request")
            emptyFlow()
          }
        }
      }
    }

  override fun dispose() {
    if (!resultDeferred.isCompleted) {
      resultDeferred.cancel()
    }
    if (!loadedDeferred.isCompleted) {
      loadedDeferred.cancel()
    }
    Disposer.dispose(messagingHandler)

    // Close the preferences file if it's open
    invokeLater {
      if (::preferencesVirtualFile.isInitialized) {
        FileEditorManager.getInstance(project).closeFile(preferencesVirtualFile)
      }
    }
  }

  suspend fun launchComparison(): Flow<ChatModelReply> {
    logger.info("Launching preferences")

    if (!hasSufficientComparableModels()) {
      return flowOf(comparisonNotAvailableMessage(requestId))
    }

    // Create and register disposal of the preferences webview
    preferencesVirtualFile =
      AugmentPreferencesWebviewEditorVirtualFile(
        project,
        messagingHandler,
      )

    // Open the preferences panel and return the deferred containing the selected response
    val editorManager = FileEditorManager.getInstance(project)
    invokeLater {
      logger.info("Opening preferences file")
      editorManager.openFile(preferencesVirtualFile, true, true)
    }

    // Wait for panel to load before streaming chat responses
    try {
      loadedDeferred.await()
    } catch (e: CancellationException) {
      logger.warn("Failed to load preferences")
      return emptyFlow() // todo(diehuxx): send error reply
    }

    // Stream chat responses
    val (modelIdA, modelIdB) = selectModelPair() ?: return flowOf(comparisonNotAvailableMessage(requestId))
    val (chatResponseA, _) = streamResponses(modelIdA, modelIdB)

    // Wait for user to submit result
    val result =
      try {
        resultDeferred.await()
      } catch (e: Exception) {
        logger.warn("Failed to get preference result")
        return flowOf(chatResponseA)
      }

    logger.info("Result received: $result")
    // TODO: Select chat response based on result
    // TODO: Send selected response to api
    invokeLater {
      logger.info("Closing preferences file")
      editorManager.closeFile(preferencesVirtualFile)
    }

    // Default to chat stream A for now until we get the selection logic working
    return flowOf(chatResponseA)
  }

  /**
   * Streams competing chat responses to preferences webview and returns two chat replies
   * that can be sent to the chat webview after preference selection is done.
   */
  private suspend fun streamResponses(
    modelIdA: String,
    modelIdB: String,
  ): Pair<ChatModelReply, ChatModelReply> {
    val chatResponseA = chatService.chatUserMessage(chatUserMessageRequest, modelIdA)
    val chatResponseB = chatService.chatUserMessage(chatUserMessageRequest, modelIdB)

    // Collect responses into a single chat reply each
    var textA = ""
    var textB = ""
    var replyDataA: ChatModelReplyData? = null
    var replyDataB: ChatModelReplyData? = null
    val jobA =
      cs.launch {
        chatResponseA.collect { reply ->
          textA += reply.data.text
          replyDataA = reply.data
        }
      }
    val jobB =
      cs.launch {
        chatResponseB.collect { reply ->
          textB += reply.data.text
          replyDataB = reply.data
        }
      }

    // Wait for both to complete
    jobA.join()
    jobB.join()

    // Send both responses and done message to the preferences webview
    val chatReplyA =
      ChatModelReply.newBuilder()
        .setData(
          replyDataA?.let {
            it.toBuilder()
              .setText(textA)
              .setStreaming(false)
              .build()
          },
        )
        .setStream("A")
        .build()
    val chatReplyB =
      ChatModelReply.newBuilder()
        .setData(
          replyDataB?.let {
            it.toBuilder()
              .setText(textB)
              .setStreaming(false)
              .build()
          },
        )
        .setStream("B")
        .build()
    preferencesVirtualFile.postChatStreamingMessage(chatReplyA)
    preferencesVirtualFile.postChatStreamingMessage(chatReplyB)
    preferencesVirtualFile.postStreamDone()

    return Pair(chatReplyA, chatReplyB)
  }

  private fun selectModelPair(): Pair<String, String>? {
    val eloConfig = PluginStateService.instance.context?.flags?.eloModelConfiguration ?: return null

    val highPriorityThreshold = eloConfig.highPriorityThreshold ?: 0.0

    return if (Math.random() < highPriorityThreshold) {
      selectHighPriorityModels(eloConfig.highPriorityModels)
    } else {
      selectRegularBattleModels(eloConfig.regularBattleModels)
    }
  }

  private fun selectHighPriorityModels(highPriorityModels: List<kotlin.Any>?): Pair<String, String>? {
    if (highPriorityModels.isNullOrEmpty()) return null

    // Check if it's an array of pairs or flat array
    return if (highPriorityModels.isNotEmpty() && highPriorityModels[0] is List<*>) {
      // Array of pairs - select a random pair
      val pairs = highPriorityModels.filterIsInstance<List<String>>()
      val randomPair = pairs.randomOrNull()
      if (randomPair != null && randomPair.size >= 2) {
        Pair(randomPair[0], randomPair[1])
      } else {
        null
      }
    } else {
      // Flat array - select two random models
      val models = highPriorityModels.filterIsInstance<String>()
      if (models.size >= 2) {
        val shuffled = models.shuffled()
        Pair(shuffled[0], shuffled[1])
      } else {
        null
      }
    }
  }

  private fun selectRegularBattleModels(regularBattleModels: List<String>?): Pair<String, String>? {
    if (regularBattleModels.isNullOrEmpty() || regularBattleModels.size < 2) return null

    val modelA = regularBattleModels.random()
    var modelB: String
    do {
      modelB = regularBattleModels.random()
    } while (modelB == modelA && regularBattleModels.size > 1)

    return Pair(modelA, modelB)
  }

  private fun buildPreferencesInput(chatUserMessageRequest: ChatUserMessageRequest): PreferenceInput {
    val text = chatUserMessageRequest.data.text
    return PreferenceInput.newBuilder()
      .setType("Chat")
      .setData(
        PreferencePair.newBuilder()
          .setA(
            AugmentChatEntry.newBuilder()
              .setMessage(text)
              .setResponse("")
              .build(),
          )
          .setB(
            AugmentChatEntry.newBuilder()
              .setMessage(text)
              .setResponse("")
              .build(),
          )
          .build(),
      )
      .build()
  }
}
