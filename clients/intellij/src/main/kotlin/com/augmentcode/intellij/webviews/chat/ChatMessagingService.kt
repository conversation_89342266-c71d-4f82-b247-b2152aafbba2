package com.augmentcode.intellij.webviews.chat

import com.augmentcode.api.OnboardingSessionEventName
import com.augmentcode.chat.DerivedStateName
import com.augmentcode.chat.UIOnlyActionName
import com.augmentcode.common.webviews.protos.SaveChatRequest
import com.augmentcode.intellij.auth.AugmentCredentials
import com.augmentcode.intellij.auth.AugmentOAuthService
import com.augmentcode.intellij.chat.AugmentChatToolWindow
import com.augmentcode.intellij.chat.ChatWebviewMessageClient
import com.augmentcode.intellij.index.AugmentRoot
import com.augmentcode.intellij.memories.MemoriesService
import com.augmentcode.intellij.metrics.ClientMetricsReporter
import com.augmentcode.intellij.metrics.OnboardingSessionEventReporter
import com.augmentcode.intellij.preferences.PreferenceLauncher
import com.augmentcode.intellij.pluginstate.PluginContext
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.pluginstate.PluginStateListener
import com.augmentcode.intellij.pluginstate.PluginStateService
import com.augmentcode.intellij.settings.AgentState
import com.augmentcode.intellij.sidecar.SidecarService
import com.augmentcode.intellij.sidecar.clientinterfaces.PluginFileStore
import com.augmentcode.intellij.sidecar.tools.AugmentTerminalInfo
import com.augmentcode.intellij.syncing.AugmentSyncingPermissionTracker
import com.augmentcode.intellij.webviews.AbstractWebviewMessagingService
import com.augmentcode.intellij.webviews.ConfirmationModal
import com.augmentcode.intellij.webviews.preferences.PreferencesUtils.preferencesEnabled
import com.augmentcode.rpc.*
import com.augmentcode.rpc.GetWorkspaceInfoRequest
import com.augmentcode.rpc.GetWorkspaceInfoResponse
import com.augmentcode.rpc.GetWorkspaceInfoResponseData
import com.augmentcode.sidecar.rpc.chat.ChatHistoryItem
import com.augmentcode.sidecar.rpc.chat.ChatRequestIdeState
import com.augmentcode.sidecar.rpc.chat.TerminalInfo
import com.augmentcode.sidecar.rpc.chat.WorkspaceFolderInfo
import com.augmentcode.sidecar.rpc.tools.ToolCallResponse
import com.google.protobuf.*
import com.google.protobuf.Any
import com.intellij.ide.BrowserUtil
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.serviceOrNull
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Disposer
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import java.util.concurrent.CompletableFuture

/**
 * This class will process messages from a webview and route them to the chat service (which
 * is the protobuf defined service).
 */
@Service(Service.Level.PROJECT)
class ChatMessagingService(
  private val project: Project,
  private val cs: CoroutineScope,
) : AbstractWebviewMessagingService(project, cs), Disposable {
  private val chatService = ChatWebviewMessageClient(project)
  private val permissionTracker = AugmentSyncingPermissionTracker.getInstance(project)

  private val clientMetricsReporter: ClientMetricsReporter = ClientMetricsReporter.getInstance(project)
  private val onboardingSessionEventReporter = OnboardingSessionEventReporter.getInstance(project)
  private var preferenceLauncher: PreferenceLauncher? = null

  private val logger = thisLogger()
  private var signInFuture: CompletableFuture<AugmentCredentials>? = null

  companion object {
    fun getInstance(project: Project): ChatMessagingService {
      return project.getService(ChatMessagingService::class.java)
    }
  }

  init {
    PluginStateService.instance.subscribe(
      project.messageBus.connect(this),
      object : PluginStateListener {
        override fun onStateChange(
          context: PluginContext?,
          state: PluginState,
        ) {
          runBlocking {
            if (state === PluginState.ENABLED) {
              onboardingSessionEventReporter.reportOnboardingSessionEvent(OnboardingSessionEventName.SignedIn)
            }

            val appToLoad = getAppToLoad()

            // post message to webview
            val publisher: ChatWebviewMessageBus = ChatWebviewMessageBus.syncPublisher(project)
            publisher.postMessageToWebview(
              MainPanelDisplayApp.newBuilder()
                .setData(appToLoad).build(),
            )
          }
        }
      },
    )
  }

  override suspend fun processSyncMessage(request: Any): Flow<Message> {
    when {
      request.`is`(OpenFileRequest::class.java) -> {
        chatService.openFile(request.unpack(OpenFileRequest::class.java))
      }

      request.`is`(OpenMemoriesFileRequest::class.java) -> {
        // Open the memories file in the editor
        MemoriesService.getInstance(project).openMemories()
        flowOf(
          Empty.getDefaultInstance(),
        )
      }

      request.`is`(ChatCreateFileRequest::class.java) -> {
        chatService.chatCreateFile(request.unpack(ChatCreateFileRequest::class.java))
      }

      request.`is`(ChatSmartPasteRequest::class.java) -> {
        chatService.chatSmartPaste(request.unpack(ChatSmartPasteRequest::class.java))
      }

      request.`is`(OpenSettingsPageRequest::class.java) -> {
        chatService.openSettingsPage(request.unpack(OpenSettingsPageRequest::class.java))
        return flowOf(Empty.getDefaultInstance())
      }

      request.`is`(MainPanelLoaded::class.java) -> {
        return flow {
          val appToLoad = getAppToLoad()
          emit(MainPanelDisplayApp.newBuilder().setData(appToLoad).build())
        }
      }

      request.`is`(MainPanelPerformAction::class.java) -> {
        val performAction = request.unpack(MainPanelPerformAction::class.java)
        when (performAction.data) {
          "cancel-sign-in" -> {
            signInFuture?.cancel(true)
            signInFuture = null

            return flowOf(
              MainPanelActions.newBuilder()
                .addData(DerivedStateName.SIGN_IN_REQUIRED.value)
                .build(),
            )
          }

          "sign-in" -> {
            invokeLater {
              signInFuture?.cancel(true)
              signInFuture = AugmentOAuthService.instance.authorize()
            }
            return flowOf(
              MainPanelActions.newBuilder()
                .addData(UIOnlyActionName.SIGN_IN_IN_PROGRESS.value)
                .build(),
            )
          }

          "grant-sync-permission" -> {
            permissionTracker.enableSyncing()
            return flowOf(
              MainPanelActions.newBuilder()
                .addData(DerivedStateName.ALL_ACTIONS_COMPLETE.value)
                .build(),
            )
          }
        }
      }

      request.`is`(SignInLoaded::class.java) -> {
        return flowOf(
          SignInLoadedResponse.newBuilder()
            .setData(
              SignInLoadedResponseData.newBuilder()
                .setClient("intellij")
                .build(),
            )
            .build(),
        )
      }

      request.`is`(UsedChat::class.java) -> {
        onboardingSessionEventReporter.reportOnboardingSessionEvent(OnboardingSessionEventName.UsedChat)
      }

      request.`is`(UsedSlashAction::class.java) -> {
        onboardingSessionEventReporter.reportOnboardingSessionEvent(OnboardingSessionEventName.UsedSlashAction)
      }

      request.`is`(AugmentLinkMessage::class.java) -> {
        val linkMessage = request.unpack(AugmentLinkMessage::class.java)
        logger.info("Augment link message: ${linkMessage.data}")
        BrowserUtil.browse(linkMessage.data)
      }

      else -> {
        logger.warn("Unknown message: $request")
      }
    }
    return emptyFlow()
  }

  override suspend fun processAsyncMessage(request: AsyncWrapper): Flow<Message> {
    return when {
      request.baseMsg.`is`(ChatLoadedRequest::class.java) ->
        flow {
          if (PluginStateService.instance.state == PluginState.ENABLED) {
            if (permissionTracker.needsSyncingPermission()) {
              emit(
                MainPanelActions.newBuilder()
                  .addData(DerivedStateName.SYNCING_PERMISSION_NEEDED.value)
                  .build(),
              )
            } else {
              emit(
                MainPanelActions.newBuilder()
                  .addData(DerivedStateName.ALL_ACTIONS_COMPLETE.value)
                  .build(),
              )
            }
          } else {
            emit(
              MainPanelActions.newBuilder()
                .addData(DerivedStateName.SIGN_IN_REQUIRED.value)
                .build(),
            )
          }
          // Send guidelines state to webview when chat is loaded
          sendGuidelinesStateToWebview()
          val response = chatService.chatLoaded(request.baseMsg.unpack(ChatLoadedRequest::class.java))
          emit(
            AsyncWrapper.newBuilder()
              .setRequestId(request.requestId)
              .setBaseMsg(Any.pack(response))
              .build(),
          )
        }

      request.baseMsg.`is`(ChatUserMessageRequest::class.java) -> {
        val result: Flow<ChatModelReply> =
          if (!preferencesEnabled()) {
            // Do actual chat
            chatService.chatUserMessage(request.baseMsg.unpack(ChatUserMessageRequest::class.java))
          } else {
            if (preferenceLauncher != null) {
              Disposer.dispose(preferenceLauncher!!)
              preferenceLauncher = null
            }
            preferenceLauncher =
              PreferenceLauncher(project, cs, request.baseMsg.unpack(ChatUserMessageRequest::class.java), request.requestId)
            val result = preferenceLauncher!!.launchComparison()
            if (preferenceLauncher != null) {
              Disposer.dispose(preferenceLauncher!!)
              preferenceLauncher = null
            }
            result
          }

        wrapFlow(
          request.requestId,
          result,
        )
      }

      request.baseMsg.`is`(ChatUserCancelRequest::class.java) -> {
        val response = chatService.chatUserCancel(request.baseMsg.unpack(ChatUserCancelRequest::class.java))
        flowOf(
          AsyncWrapper.newBuilder()
            .setRequestId(request.requestId)
            .setBaseMsg(Any.pack(response))
            .build(),
        )
      }

      request.baseMsg.`is`(FindFolderRequest::class.java) -> {
        val response = chatService.findFolder(request.baseMsg.unpack(FindFolderRequest::class.java))
        flowOf(
          AsyncWrapper.newBuilder()
            .setRequestId(request.requestId)
            .setBaseMsg(Any.pack(response))
            .build(),
        )
      }

      request.baseMsg.`is`(FindFileRequest::class.java) -> {
        val response = chatService.findFile(request.baseMsg.unpack(FindFileRequest::class.java))
        flowOf(
          AsyncWrapper.newBuilder()
            .setRequestId(request.requestId)
            .setBaseMsg(Any.pack(response))
            .build(),
        )
      }

      request.baseMsg.`is`(ResolveFileRequest::class.java) -> {
        val response = chatService.resolveFile(request.baseMsg.unpack(ResolveFileRequest::class.java))
        flowOf(
          AsyncWrapper.newBuilder()
            .setRequestId(request.requestId)
            .setBaseMsg(Any.pack(response))
            .build(),
        )
      }

      request.baseMsg.`is`(FindExternalSourcesRequest::class.java) -> {
        val response = chatService.findExternalSources(request.baseMsg.unpack(FindExternalSourcesRequest::class.java))
        flowOf(
          AsyncWrapper.newBuilder()
            .setRequestId(request.requestId)
            .setBaseMsg(Any.pack(response))
            .build(),
        )
      }

      request.baseMsg.`is`(FindRecentlyOpenedFilesRequest::class.java) -> {
        val response = chatService.findRecentlyOpenedFiles(request.baseMsg.unpack(FindRecentlyOpenedFilesRequest::class.java))
        flowOf(
          AsyncWrapper.newBuilder()
            .setRequestId(request.requestId)
            .setBaseMsg(Any.pack(response))
            .build(),
        )
      }

      request.baseMsg.`is`(FindSymbolRequest::class.java) -> {
        val response = chatService.findSymbol(request.baseMsg.unpack(FindSymbolRequest::class.java))
        flowOf(
          AsyncWrapper.newBuilder()
            .setRequestId(request.requestId)
            .setBaseMsg(Any.pack(response))
            .build(),
        )
      }

      request.baseMsg.`is`(FindSymbolRegexRequest::class.java) -> {
        val response = chatService.findSymbolRegex(request.baseMsg.unpack(FindSymbolRegexRequest::class.java))
        flowOf(
          AsyncWrapper.newBuilder()
            .setRequestId(request.requestId)
            .setBaseMsg(Any.pack(response))
            .build(),
        )
      }

      request.baseMsg.`is`(OpenConfirmationModal::class.java) -> {
        val data = request.baseMsg.unpack(OpenConfirmationModal::class.java).data
        val result = ConfirmationModal.showAndGetAsync(project, data)

        flowOf(
          AsyncWrapper.newBuilder()
            .setRequestId(request.requestId)
            .setBaseMsg(
              Any.pack(
                ConfirmationModalResponse.newBuilder()
                  .setData(
                    ConfirmationModalResponseData.newBuilder()
                      .setOk(result).build(),
                  ).build(),
              ),
            )
            .build(),
        )
      }

      request.baseMsg.`is`(ChatRatingMessage::class.java) -> {
        val response = chatService.chatFeedback(request.baseMsg.unpack(ChatRatingMessage::class.java))
        flowOf(
          AsyncWrapper.newBuilder()
            .setRequestId(request.requestId)
            .setBaseMsg(Any.pack(response))
            .build(),
        )
      }

      request.baseMsg.`is`(SaveChatRequest::class.java) -> {
        val response = chatService.saveChat(request.baseMsg.unpack(SaveChatRequest::class.java))

        flowOf(
          AsyncWrapper.newBuilder()
            .setRequestId(request.requestId)
            .setBaseMsg(Any.pack(response))
            .build(),
        )
      }

      request.baseMsg.`is`(ReportWebviewClientMetricRequest::class.java) -> {
        val data = request.baseMsg.unpack(ReportWebviewClientMetricRequest::class.java)
        clientMetricsReporter.reportWebviewClientMetric(
          data.webviewName.name,
          data.clientMetric,
          data.value,
        )
        emptyFlow()
      }

      request.baseMsg.`is`(GetDiagnosticsRequest::class.java) -> {
        val response = chatService.getDiagnostics(request.baseMsg.unpack(GetDiagnosticsRequest::class.java))

        flowOf(
          AsyncWrapper.newBuilder()
            .setRequestId(request.requestId)
            .setBaseMsg(Any.pack(response))
            .build(),
        )
      }

      request.baseMsg.`is`(ToolCheckSafeWebViewRequest::class.java) -> {
        val baseMsg = request.baseMsg.unpack(ToolCheckSafeWebViewRequest::class.java)
        val isSafe = project.serviceOrNull<SidecarService>()?.checkToolSafety(baseMsg.data.name, baseMsg.data.input) ?: false
        flowOf(
          AsyncWrapper.newBuilder()
            .setRequestId(request.requestId)
            .setBaseMsg(
              Any.pack(
                ToolCheckSafeWebViewResponse.newBuilder()
                  .setData(
                    ToolCheckSafeResponseData.newBuilder()
                      .setIsSafe(isSafe)
                      .build(),
                  )
                  .build(),
              ),
            )
            .build(),
        )
      }

      request.baseMsg.`is`(ToolCallWebViewRequest::class.java) -> {
        val baseMsg = request.baseMsg.unpack(ToolCallWebViewRequest::class.java)
        val data = baseMsg.data
        val result =
          try {
            project.serviceOrNull<SidecarService>()?.callTool(
              data.name,
              data.requestId,
              data.toolUseId,
              data.input,
              data.chatHistoryList.map {
                ChatHistoryItem.newBuilder()
                  .setRequestId(it.requestId)
                  .setRequestMessage(it.requestMessage)
                  .setResponseText(it.responseText)
                  .build()
              },
              data.conversationId,
            )
          } catch (ex: Throwable) {
            logger.error("Failed to call '${data.name}' tool", ex)
            ToolCallResponse.newBuilder()
              .setText(ex.message ?: "IDE error")
              .setIsError(true)
              .build()
          }
        if (result == null) {
          return flowOf(
            AsyncWrapper.newBuilder()
              .setRequestId(request.requestId)
              .setBaseMsg(
                Any.pack(
                  ToolCallWebViewResponse.newBuilder()
                    .setData(
                      ToolCallWebViewResponseData.newBuilder()
                        .setText("Unable to run the tool")
                        .setIsError(true)
                        .build(),
                    )
                    .build(),
                ),
              )
              .build(),
          )
        }

        flowOf(
          AsyncWrapper.newBuilder()
            .setRequestId(request.requestId)
            .setBaseMsg(
              Any.pack(
                ToolCallWebViewResponse.newBuilder()
                  .setData(
                    ToolCallWebViewResponseData.newBuilder()
                      .setText(result.text)
                      .setIsError(result.isError)
                      .setRequestId(data.requestId)
                      .build(),
                  )
                  .build(),
              ),
            )
            .build(),
        )
      }

      request.baseMsg.`is`(ToolCancelRunWebViewRequest::class.java) -> {
        val baseMsg = request.baseMsg.unpack(ToolCancelRunWebViewRequest::class.java)
        project.serviceOrNull<SidecarService>()?.cancelToolRun(baseMsg.data.requestId, baseMsg.data.toolUseId)
        flowOf(
          AsyncWrapper.newBuilder()
            .setRequestId(request.requestId)
            .setBaseMsg(
              Any.pack(
                ToolCancelRunWebViewResponse.getDefaultInstance(),
              ),
            )
            .build(),
        )
      }

      request.baseMsg.`is`(CheckToolExistsRequest::class.java) -> {
        val baseMsg = request.baseMsg.unpack(CheckToolExistsRequest::class.java)
        val exists = project.serviceOrNull<SidecarService>()?.toolExists(baseMsg.toolName) ?: false
        flowOf(
          AsyncWrapper.newBuilder()
            .setRequestId(request.requestId)
            .setBaseMsg(
              Any.pack(
                CheckToolExistsResponse.newBuilder()
                  .setExists(exists)
                  .build(),
              ),
            )
            .build(),
        )
      }

      request.baseMsg.`is`(ChatModeChangedWebViewRequest::class.java) -> {
        val baseMsg = request.baseMsg.unpack(ChatModeChangedWebViewRequest::class.java)
        logger.info("Chat mode changed to ${baseMsg.data.mode}")
        project.serviceOrNull<SidecarService>()?.changeChatMode(baseMsg.data.mode)
        flowOf(
          AsyncWrapper.newBuilder()
            .setRequestId(request.requestId)
            .setBaseMsg(
              Any.pack(
                Empty.getDefaultInstance(),
              ),
            )
            .build(),
        )
      }

      request.baseMsg.`is`(CheckAgentAutoModeApprovalRequest::class.java) -> {
        val response =
          CheckAgentAutoModeApprovalResponse.newBuilder()
            .setData(AgentState.instance.agentAutoModeApproved)
            .build()
        flowOf(
          AsyncWrapper.newBuilder()
            .setRequestId(request.requestId)
            .setBaseMsg(Any.pack(response))
            .build(),
        )
      }

      request.baseMsg.`is`(SetAgentAutoModeApprovedRequest::class.java) -> {
        val baseMsg = request.baseMsg.unpack(SetAgentAutoModeApprovedRequest::class.java)
        AgentState.instance.agentAutoModeApproved = baseMsg.data
        flowOf(
          AsyncWrapper.newBuilder()
            .setRequestId(request.requestId)
            .setBaseMsg(
              Any.pack(
                Empty.getDefaultInstance(),
              ),
            )
            .build(),
        )
      }

      request.baseMsg.`is`(GetWorkspaceInfoRequest::class.java) -> {
        // Always return 99999 files because this is currently only used to determine the persona type
        // when tracked files is greater than some small threshold
        val response =
          GetWorkspaceInfoResponse.newBuilder()
            .setData(
              GetWorkspaceInfoResponseData.newBuilder()
                .addTrackedFileCount(99999)
                .build(),
            )
            .build()
        flowOf(
          AsyncWrapper.newBuilder()
            .setRequestId(request.requestId)
            .setBaseMsg(Any.pack(response))
            .build(),
        )
      }

      request.baseMsg.`is`(GetIDEStateNodeRequest::class.java) -> {
        val stateBuilder = ChatRequestIdeState.newBuilder()

        // TODO: This should probably handle multiple roots
        val currentDir = AugmentRoot.findActiveProjectRoot(project)
        if (currentDir != null) {
          val rootDir = AugmentRoot.findRootForProjectFile(project, currentDir)
          stateBuilder.addWorkspaceFolders(
            WorkspaceFolderInfo.newBuilder()
              .setRepositoryRoot(rootDir?.path ?: currentDir.path)
              .setFolderRoot(currentDir.path)
              .build(),
          )
        }
        val activeTerminal = AugmentTerminalInfo.findActiveTerminal(project)
        if (activeTerminal != null) {
          stateBuilder.setCurrentTerminal(
            TerminalInfo.newBuilder()
              .setTerminalId(activeTerminal.terminalId.toInt())
              .setCurrentWorkingDirectory(activeTerminal.workingDirectory)
              .build(),
          )
        }
        val response = GetIDEStateNodeResponse.newBuilder().setData(stateBuilder).build()
        flowOf(
          AsyncWrapper.newBuilder()
            .setRequestId(request.requestId)
            .setBaseMsg(Any.pack(response))
            .build(),
        )
      }

      request.baseMsg.`is`(ChatSaveImageRequest::class.java) -> {
        val saveImageRequest = request.baseMsg.unpack(ChatSaveImageRequest::class.java)
        val fileData = saveImageRequest.data
        val filename = fileData.filename
        val data = java.util.Base64.getDecoder().decode(fileData.data)

        // Save the image using PluginFileStore
        val fileStore = PluginFileStore(project)
        fileStore.saveAsset(filename, data)

        val response = ChatSaveImageResponse.newBuilder().setData(filename).build()

        flowOf(
          AsyncWrapper.newBuilder()
            .setRequestId(request.requestId)
            .setBaseMsg(Any.pack(response))
            .build(),
        )
      }

      request.baseMsg.`is`(ChatLoadImageRequest::class.java) -> {
        val loadImageRequest = request.baseMsg.unpack(ChatLoadImageRequest::class.java)
        val filename = loadImageRequest.data

        // Load the image using PluginFileStore
        val fileStore = PluginFileStore(project)
        val imageData = fileStore.loadAsset(filename)

        val responseBuilder = ChatLoadImageResponse.newBuilder()
        if (imageData != null) {
          val base64Data = java.util.Base64.getEncoder().encodeToString(imageData)
          responseBuilder.setData(base64Data)
        }

        val response = responseBuilder.build()

        flowOf(
          AsyncWrapper.newBuilder()
            .setRequestId(request.requestId)
            .setBaseMsg(Any.pack(response))
            .build(),
        )
      }

      request.baseMsg.`is`(ChatDeleteImageRequest::class.java) -> {
        val deleteImageRequest = request.baseMsg.unpack(ChatDeleteImageRequest::class.java)
        val filename = deleteImageRequest.data

        // Delete the image using PluginFileStore
        val fileStore = PluginFileStore(project)
        fileStore.deleteAsset(filename)

        val response = ChatDeleteImageResponse.newBuilder().build()
        flowOf(
          AsyncWrapper.newBuilder()
            .setRequestId(request.requestId)
            .setBaseMsg(Any.pack(response))
            .build(),
        )
      }

      else -> {
        logger.warn("Unknown async message: $request")
        flowOf(
          AsyncWrapper.newBuilder()
            .setRequestId(request.requestId)
            .setError("Unsupported async message type: ${request.baseMsg.typeUrl}")
            .build(),
        )
      }
    }
  }

  private suspend fun getAppToLoad(): String {
    var appToLoad = getAppToLoadInternal()
    if (appToLoad == AugmentChatToolWindow.CHAT_APP) {
      safelyAwaitSidecar()
      // If sidecar takes a long time to load for any reason, the state may be different by the time we return
      // a result, so check again.
      // Example: User sign's in, we wait for sidecar to start, during which the user signs out, when sidecar is then
      // cancelled and the deferred is cancelled, we return load chat, when it should be sign in.
      appToLoad = getAppToLoadInternal()
    }
    return appToLoad
  }

  /**
   * Waits for sidecar to be ready, but does not throw if the deferred is cancelled.
   */
  private suspend fun safelyAwaitSidecar() {
    try {
      // We don't have a got way to mock sidecar service at the moment, so skip this in tests for now.
      if (!ApplicationManager.getApplication().isUnitTestMode) {
        // Get SidecarService and wait for it to be ready
        project.serviceOrNull<SidecarService>()?.ready()?.await()
      }
    } catch (e: Throwable) {
      logger.info("Sidecar ready deferred was cancelled", e)
    }
  }

  private fun getAppToLoadInternal(): String {
    // TODO: UNINITIALIZED & INITIALIZING should be shown as a loading state in the webview
    // TODO: FAILED AND GET_MODEL_INFO_FAILED should be shown as an error state in the webview
    return when (PluginStateService.instance.state) {
      // When enabled, load the chat app
      PluginState.ENABLED -> {
        if (permissionTracker.needsSyncingPermission()) {
          AugmentChatToolWindow.AWAITING_SYNCING_PERMISSION_APP
        } else {
          AugmentChatToolWindow.CHAT_APP
        }
      }

      // Otherwise load sign in app
      PluginState.SIGN_IN_REQUIRED, PluginState.GET_MODEL_INFO_FAILED, PluginState.UNINITIALIZED,
      PluginState.INITIALIZING, PluginState.FAILED,
      -> AugmentChatToolWindow.SIGN_IN_APP
    }
  }

  fun sendGuidelinesStateToWebview() {
    chatService.sendGuidelinesStateToWebview()
  }

  override fun dispose() {
    clientMetricsReporter.dispose()
    onboardingSessionEventReporter.dispose()
    if (preferenceLauncher != null) {
      Disposer.dispose(preferenceLauncher!!)
      preferenceLauncher = null
    }
  }
}
