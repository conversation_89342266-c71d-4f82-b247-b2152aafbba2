package com.augmentcode.intellij.sentry

import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.pluginstate.PluginStateService
import com.augmentcode.intellij.settings.PluginVersionProvider
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.FeatureFlagsTestUtil
import com.augmentcode.intellij.testutils.waitForAssertion
import com.intellij.openapi.util.Disposer
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import io.mockk.*
import io.sentry.Breadcrumb
import io.sentry.Sentry
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

/**
 * Comprehensive tests for SentryService covering both unit testing and integration testing scenarios.
 *
 * Unit tests focus on service initialization, feature flag controls, and basic functionality with mocked dependencies.
 * Integration tests verify cross-service interactions with SentryProjectMetadataService and ProjectManager.
 */
@RunWith(JUnit4::class)
class SentryServiceTest : AugmentBasePlatformTestCase() {
  private lateinit var mockVersionProvider: PluginVersionProvider
  private lateinit var mockGlobalExceptionHandler: SentryGlobalExceptionHandler

  override fun setUp() {
    super.setUp()

    // Create mocks for dependencies that can't be easily replaced via feature flags
    mockVersionProvider = mockk(relaxed = true)
    mockGlobalExceptionHandler = mockk(relaxed = true)

    // Setup default version provider behavior
    every { mockVersionProvider.currentPluginVersion() } returns "1.0.0"

    // Register the mock version provider
    application.registerOrReplaceServiceInstance(
      PluginVersionProvider::class.java,
      mockVersionProvider,
      testRootDisposable,
    )

    // Register the mock SentryGlobalExceptionHandler as a service
    application.registerOrReplaceServiceInstance(
      SentryGlobalExceptionHandler::class.java,
      mockGlobalExceptionHandler,
      testRootDisposable,
    )
  }

  /**
   * Helper method to set up feature flags for Sentry testing.
   * Uses FeatureFlagsTestUtil to properly mock feature flag API calls.
   */
  private fun setupSentryFeatureFlags(
    sentryEnabled: Boolean = false,
    pluginErrorSamplingRate: Double = 0.0,
    pluginTraceSamplingRate: Double = 0.0,
  ) {
    FeatureFlagsTestUtil.setupFeatureFlags(
      testRootDisposable,
      mapOf(
        "intellij_enable_sentry" to sentryEnabled,
        "intellij_plugin_error_sampling_rate" to pluginErrorSamplingRate,
        "intellij_plugin_trace_sampling_rate" to pluginTraceSamplingRate,
      ),
    )
  }

  @Test
  fun testInitialize_whenSentryEnabled() {
    // Given - Set up feature flags using FeatureFlagsTestUtil FIRST
    setupSentryFeatureFlags(
      sentryEnabled = true,
      pluginErrorSamplingRate = 1.0,
      pluginTraceSamplingRate = 1.0,
    )

    waitForAssertion({
      assertNotNull(PluginStateService.instance.context)
    })

    mockkStatic(Sentry::class)
    every { Sentry.init(any<Sentry.OptionsConfiguration<io.sentry.SentryOptions>>()) } just Runs

    // Reset mocks since mock global service can be called by other services
    clearAllMocks()

    // When - Create service instance (initialize() is called automatically)
    val sentryService = SentryService()
    waitForAssertion({
      assertTrue(sentryService.isInitialized)
    })

    // Then
    verify(exactly = 1) { Sentry.init(any<Sentry.OptionsConfiguration<io.sentry.SentryOptions>>()) }
    verify(exactly = 1) { mockGlobalExceptionHandler.install() }
  }

  @Test
  fun testInitialize_whenSentryDisabled() {
    // Given - Set up feature flags using FeatureFlagsTestUtil FIRST
    setupSentryFeatureFlags(sentryEnabled = false)

    mockkStatic(Sentry::class)
    every { Sentry.init(any<Sentry.OptionsConfiguration<io.sentry.SentryOptions>>()) } just Runs

    // When - Create service instance (initialize() is called automatically)
    SentryService()
    waitForAssertion({
      assertNotNull(PluginStateService.instance.context)
    })

    // Then
    verify(exactly = 0) { Sentry.init(any<Sentry.OptionsConfiguration<io.sentry.SentryOptions>>()) }
  }

  @Test
  fun testCaptureException_whenSentryEnabled() {
    // Given - Set up feature flags using FeatureFlagsTestUtil FIRST
    setupSentryFeatureFlags(
      sentryEnabled = true,
      pluginErrorSamplingRate = 1.0,
      pluginTraceSamplingRate = 1.0,
    )

    // Mock ProjectManager to avoid ClassCastException
    val mockProjectManager = mockk<com.intellij.openapi.project.ProjectManager>(relaxed = true)
    mockkStatic(com.intellij.openapi.project.ProjectManager::class)
    every { com.intellij.openapi.project.ProjectManager.getInstance() } returns mockProjectManager
    every { mockProjectManager.openProjects } returns emptyArray()

    mockkStatic(Sentry::class)
    every { Sentry.init(any<Sentry.OptionsConfiguration<io.sentry.SentryOptions>>()) } just Runs
    every { Sentry.captureException(any()) } returns mockk()
    every { Sentry.addBreadcrumb(any<Breadcrumb>()) } returns Unit

    // When - Create service instance (initialize() is called automatically)
    val sentryService = SentryService()
    waitForAssertion({
      assertTrue(sentryService.isInitialized)
    })

    val exception = RuntimeException("Test exception")
    sentryService.captureException(exception)

    // Then
    verify(exactly = 1) { Sentry.captureException(exception) }
  }

  @Test
  fun testCaptureException_whenSentryDisabled() {
    // Given - Set up feature flags using FeatureFlagsTestUtil FIRST
    setupSentryFeatureFlags(sentryEnabled = false)

    mockkStatic(Sentry::class)
    every { Sentry.captureException(any()) } returns mockk()

    // When - Create service instance (initialize() is called automatically)
    val sentryService = SentryService()
    // Sentry will never be initialized since feature flag is false
    waitForAssertion({
      assertNotNull(PluginStateService.instance.context)
    })

    val exception = RuntimeException("Test exception")
    sentryService.captureException(exception)

    // Then
    verify(exactly = 0) { Sentry.captureException(any()) }
  }

  @Test
  fun testAddBreadcrumb_whenSentryEnabled() {
    // Given - Set up feature flags using FeatureFlagsTestUtil FIRST
    setupSentryFeatureFlags(sentryEnabled = true)

    mockkStatic(Sentry::class)
    every { Sentry.init(any<Sentry.OptionsConfiguration<io.sentry.SentryOptions>>()) } just Runs
    every { Sentry.addBreadcrumb(any<Breadcrumb>()) } just Runs

    // When - Create service instance (initialize() is called automatically)
    val sentryService = SentryService()
    waitForAssertion({
      assertTrue(sentryService.isInitialized)
    })

    sentryService.addBreadcrumb("Test breadcrumb", "test-category")

    // Then
    verify(exactly = 1) { Sentry.addBreadcrumb(any<Breadcrumb>()) }
  }

  @Test
  fun testAddBreadcrumb_whenSentryDisabled() {
    // Given - Set up feature flags using FeatureFlagsTestUtil FIRST
    setupSentryFeatureFlags(sentryEnabled = false)

    mockkStatic(Sentry::class)
    every { Sentry.addBreadcrumb(any<Breadcrumb>()) } just Runs

    // When - Create service instance (initialize() is called automatically)
    val sentryService = SentryService()
    waitForAssertion({
      assertNotNull(PluginStateService.instance.context)
    })

    sentryService.addBreadcrumb("Test breadcrumb", "test-category")

    // Then
    verify(exactly = 0) { Sentry.addBreadcrumb(any<Breadcrumb>()) }
  }

  @Test
  fun testInitialize_multipleCallsOnlyInitializeOnce() {
    // Given - Set up feature flags using FeatureFlagsTestUtil FIRST
    setupSentryFeatureFlags(
      sentryEnabled = true,
      pluginErrorSamplingRate = 1.0,
      pluginTraceSamplingRate = 1.0,
    )

    mockkStatic(Sentry::class)
    every { Sentry.init(any<Sentry.OptionsConfiguration<io.sentry.SentryOptions>>()) } just Runs

    clearAllMocks()

    // When - Create service instance (initialize() is called automatically once)
    val sentryService = SentryService()
    waitForAssertion({
      assertTrue(sentryService.isInitialized)
    })

    // Then - should only initialize once (from constructor, explicit call should be ignored)
    verify(exactly = 1) { Sentry.init(any<Sentry.OptionsConfiguration<io.sentry.SentryOptions>>()) }
    verify(exactly = 1) { mockGlobalExceptionHandler.install() }
  }

  @Test
  fun testInitializeAndDispose_globalExceptionHandlerLifecycle() {
    // Given - Set up feature flags using FeatureFlagsTestUtil FIRST
    setupSentryFeatureFlags(
      sentryEnabled = true,
      pluginErrorSamplingRate = 1.0,
      pluginTraceSamplingRate = 1.0,
    )

    waitForAssertion({
      assertNotNull(PluginStateService.instance.context)
    })

    mockkStatic(Sentry::class)
    every { Sentry.init(any<Sentry.OptionsConfiguration<io.sentry.SentryOptions>>()) } just Runs
    every { Sentry.close() } just Runs

    clearAllMocks()

    // When - Create service instance (initialize() is called automatically) and then dispose
    val sentryService = SentryService()
    waitForAssertion({
      assertTrue(sentryService.isInitialized)
    })

    // Verify startup cycle
    waitForAssertion({
      verify(exactly = 1) { Sentry.init(any<Sentry.OptionsConfiguration<io.sentry.SentryOptions>>()) }
      verify(exactly = 1) { mockGlobalExceptionHandler.install() }
    })

    Disposer.dispose(sentryService)

    // Then - verify the disposal lifecycle
    verify(exactly = 1) { mockGlobalExceptionHandler.uninstall() }
    verify(exactly = 1) { Sentry.close() }
  }

  @Test
  fun testAddMetricsBreadcrumb_whenSentryEnabled() {
    // Given - Set up feature flags using FeatureFlagsTestUtil FIRST
    setupSentryFeatureFlags(sentryEnabled = true)

    mockkStatic(Sentry::class)
    every { Sentry.init(any<Sentry.OptionsConfiguration<io.sentry.SentryOptions>>()) } just Runs
    every { Sentry.addBreadcrumb(any<Breadcrumb>()) } just Runs

    val metricName = "test_metric"
    val metricValue = 42L
    val tags = mapOf("tenant_name" to "test-tenant", "is_remote_gateway" to "false")

    // When - Create service instance (initialize() is called automatically)
    val sentryService = SentryService()
    waitForAssertion({
      assertTrue(sentryService.isInitialized)
    })

    sentryService.addMetricsBreadcrumb(metricName, metricValue, tags)

    // Then
    verify(exactly = 1) {
      Sentry.addBreadcrumb(
        match<Breadcrumb> { breadcrumb ->
          breadcrumb.category == "metrics" &&
            breadcrumb.message == "Metric: $metricName = $metricValue" &&
            breadcrumb.getData("metric_name") == metricName &&
            breadcrumb.getData("metric_value") == metricValue.toString() &&
            breadcrumb.getData("tag_tenant_name") == "test-tenant" &&
            breadcrumb.getData("tag_is_remote_gateway") == "false"
        },
      )
    }
  }

  @Test
  fun testAddMetricsBreadcrumb_whenSentryDisabled() {
    // Given - Set up feature flags using FeatureFlagsTestUtil FIRST
    setupSentryFeatureFlags(sentryEnabled = false)

    mockkStatic(Sentry::class)
    every { Sentry.addBreadcrumb(any<Breadcrumb>()) } just Runs

    // When - Create service instance (initialize() is called automatically)
    val sentryService = SentryService()
    waitForAssertion({
      assertNotNull(PluginStateService.instance.context)
    })

    sentryService.addMetricsBreadcrumb("test_metric", 42L, emptyMap())

    // Then
    verify(exactly = 0) { Sentry.addBreadcrumb(any<Breadcrumb>()) }
  }

  @Test
  fun testAddMetricsBreadcrumb_withEmptyTags() {
    // Given - Set up feature flags using FeatureFlagsTestUtil FIRST
    setupSentryFeatureFlags(sentryEnabled = true)

    mockkStatic(Sentry::class)
    every { Sentry.init(any<Sentry.OptionsConfiguration<io.sentry.SentryOptions>>()) } just Runs
    every { Sentry.addBreadcrumb(any<Breadcrumb>()) } just Runs

    // When - Create service instance (initialize() is called automatically)
    val sentryService = SentryService()
    waitForAssertion({
      assertTrue(sentryService.isInitialized)
    })

    sentryService.addMetricsBreadcrumb("test_metric", 100.5, emptyMap())

    // Then
    verify(exactly = 1) {
      Sentry.addBreadcrumb(
        match<Breadcrumb> { breadcrumb ->
          breadcrumb.category == "metrics" &&
            breadcrumb.getData("metric_name") == "test_metric" &&
            breadcrumb.getData("metric_value") == "100.5"
        },
      )
    }
  }

  @Test
  fun testDispose() {
    // Create service instance and dispose it
    val sentryService = SentryService()

    mockkStatic(Sentry::class)
    every { Sentry.close() } just Runs

    clearAllMocks()

    // Dispose of service
    Disposer.dispose(sentryService)

    // Then
    verify(exactly = 1) { mockGlobalExceptionHandler.uninstall() }
    verify(exactly = 1) { Sentry.close() }
  }

  @Test
  fun testFeatureFlagSamplingRates() {
    // Given - Set up feature flags with specific sampling rates
    setupSentryFeatureFlags(
      sentryEnabled = true,
      pluginErrorSamplingRate = 0.5,
      pluginTraceSamplingRate = 0.25,
    )

    waitForAssertion({
      assertNotNull(PluginStateService.instance.context)
    })

    // Then - Verify that the feature flags are properly read
    val ctx = PluginStateService.instance.context!!
    assertTrue("Sentry should be enabled via feature flag", ctx.flags.sentryEnabled)
    assertEquals("Plugin error sampling rate should match", 0.5, ctx.flags.pluginErrorSamplingRate, 0.001)
    assertEquals("Plugin trace sampling rate should match", 0.25, ctx.flags.pluginTraceSamplingRate, 0.001)
  }

  @Test
  fun testInitialize_withDifferentSamplingRates() {
    // Given - Set up feature flags with different sampling rates FIRST
    setupSentryFeatureFlags(
      sentryEnabled = true,
      pluginErrorSamplingRate = 0.8,
      pluginTraceSamplingRate = 0.1,
    )

    mockkStatic(Sentry::class)
    every { Sentry.init(any<Sentry.OptionsConfiguration<io.sentry.SentryOptions>>()) } just Runs

    clearAllMocks()

    // When - Create service instance (initialize() is called automatically)
    val sentryService = SentryService()
    waitForAssertion({
      assertTrue(sentryService.isInitialized)
    })

    // Then - Verify initialization occurred and sampling rates are accessible
    verify(exactly = 1) { Sentry.init(any<Sentry.OptionsConfiguration<io.sentry.SentryOptions>>()) }
    verify(exactly = 1) { mockGlobalExceptionHandler.install() }

    // Verify sampling rates are correctly set
    waitForAssertion({
      assertNotNull(PluginStateService.instance.context)
    })
    val ctx = PluginStateService.instance.context!!
    assertEquals("Plugin error sampling rate should be 0.8", 0.8, ctx.flags.pluginErrorSamplingRate, 0.001)
    assertEquals("Plugin trace sampling rate should be 0.1", 0.1, ctx.flags.pluginTraceSamplingRate, 0.001)
  }

  @Test
  fun testInitialize_whenDevelopmentEnvironment() {
    // Given - Set up feature flags with Sentry enabled FIRST
    setupSentryFeatureFlags(sentryEnabled = true)

    // Mock version provider to return a snapshot version (development environment)
    // This needs to be set up before the service is created
    val developmentVersionProvider = mockk<PluginVersionProvider>(relaxed = true)
    every { developmentVersionProvider.currentPluginVersion() } returns "1.0.0-snapshot"

    // Register the development version provider
    application.registerOrReplaceServiceInstance(
      PluginVersionProvider::class.java,
      developmentVersionProvider,
      testRootDisposable,
    )

    mockkStatic(Sentry::class)
    every { Sentry.init(any<Sentry.OptionsConfiguration<io.sentry.SentryOptions>>()) } just Runs

    // When - Create service instance (initialize() is called automatically)
    SentryService()
    waitForAssertion({
      assertNotNull(PluginStateService.instance.context)
    })

    // Then - Sentry should NOT be initialized in development environment
    verify(exactly = 0) { Sentry.init(any<Sentry.OptionsConfiguration<io.sentry.SentryOptions>>()) }
  }

  @Test
  fun testInitialize_whenProductionEnvironmentAndFeatureFlagEnabled() {
    // Given - Set up feature flags with Sentry enabled FIRST
    setupSentryFeatureFlags(sentryEnabled = true)

    // The default mock version provider already returns "1.0.0" (production version)
    mockkStatic(Sentry::class)
    every { Sentry.init(any<Sentry.OptionsConfiguration<io.sentry.SentryOptions>>()) } just Runs

    clearAllMocks()

    // When - Create service instance (initialize() is called automatically)
    val sentryService = SentryService()
    waitForAssertion({
      assertTrue(sentryService.isInitialized)
    })

    // Then - Sentry should be initialized in production environment with feature flag enabled
    verify(exactly = 1) { Sentry.init(any<Sentry.OptionsConfiguration<io.sentry.SentryOptions>>()) }
    verify(exactly = 1) { mockGlobalExceptionHandler.install() }
  }

  @Test
  fun testInitialize_whenProductionEnvironmentButFeatureFlagDisabled() {
    // Given - Set up feature flags with Sentry disabled FIRST
    setupSentryFeatureFlags(sentryEnabled = false)

    // The default mock version provider already returns "1.0.0" (production version)
    mockkStatic(Sentry::class)
    every { Sentry.init(any<Sentry.OptionsConfiguration<io.sentry.SentryOptions>>()) } just Runs

    // When - Create service instance (initialize() is called automatically)
    SentryService()
    waitForAssertion({
      assertNotNull(PluginStateService.instance.context)
    })

    clearAllMocks()

    // Then - Sentry should NOT be initialized when feature flag is disabled
    verify(exactly = 0) { Sentry.init(any<Sentry.OptionsConfiguration<io.sentry.SentryOptions>>()) }
    verify(exactly = 0) { mockGlobalExceptionHandler.install() }
  }

  @Test
  fun testInitialize_whenNullPluginVersion() {
    // Given - Set up feature flags with Sentry enabled FIRST
    setupSentryFeatureFlags(sentryEnabled = true)

    // Mock version provider to return null (should default to production)
    // This needs to be set up before the service is created
    val nullVersionProvider = mockk<PluginVersionProvider>(relaxed = true)
    every { nullVersionProvider.currentPluginVersion() } returns null

    // Register the null version provider
    application.registerOrReplaceServiceInstance(
      PluginVersionProvider::class.java,
      nullVersionProvider,
      testRootDisposable,
    )
    mockkStatic(Sentry::class)
    every { Sentry.init(any<Sentry.OptionsConfiguration<io.sentry.SentryOptions>>()) } just Runs

    // When - Create service instance (initialize() is called automatically)
    val sentryService = SentryService()
    waitForAssertion({
      assertTrue(sentryService.isInitialized)
    })

    // Then - Sentry should be initialized (null version defaults to production)
    verify(exactly = 1) { Sentry.init(any<Sentry.OptionsConfiguration<io.sentry.SentryOptions>>()) }
  }

  private fun setupProjectMetadataServiceMock(): SentryProjectMetadataService {
    val mockMetadataService = mockk<SentryProjectMetadataService>(relaxed = true)
    val testMetrics =
      MetricsData(
        memoryMetrics = MemoryMetrics(heapAvailableMB = 1024, heapUtilizedMB = 512),
        repositoryMetrics = RepositoryMetrics(gitTrackedFiles = 100, indexedFiles = 50),
        systemTags = mapOf("tenant_name" to "test-tenant", "is_remote_gateway" to "false"),
      )
    every { mockMetadataService.latestMetrics } returns testMetrics

    myFixture.project.registerOrReplaceServiceInstance(
      SentryProjectMetadataService::class.java,
      mockMetadataService,
      testRootDisposable,
    )
    return mockMetadataService
  }

  @Test
  fun testCaptureExceptionWithProjectIsolation() {
    setupSentryFeatureFlags(sentryEnabled = true)
    mockkStatic(Sentry::class)
    every { Sentry.init(any<Sentry.OptionsConfiguration<io.sentry.SentryOptions>>()) } just Runs
    every { Sentry.captureException(any<Throwable>()) } returns mockk()

    setupProjectMetadataServiceMock()

    val sentryService = SentryService()
    waitForAssertion({
      assertTrue(sentryService.isInitialized)
    })

    val testException = RuntimeException("Test exception")
    sentryService.captureException(testException, myFixture.project)

    verify(exactly = 1) { Sentry.captureException(testException) }
  }

  @Test
  fun testCaptureExceptionWithoutProjectContext() {
    setupSentryFeatureFlags(sentryEnabled = true)
    mockkStatic(Sentry::class)
    every { Sentry.init(any<Sentry.OptionsConfiguration<io.sentry.SentryOptions>>()) } just Runs
    every { Sentry.captureException(any<Throwable>()) } returns mockk()

    val mockProjectManager = mockk<com.intellij.openapi.project.ProjectManager>(relaxed = true)
    mockkStatic(com.intellij.openapi.project.ProjectManager::class)
    every { com.intellij.openapi.project.ProjectManager.getInstance() } returns mockProjectManager
    every { mockProjectManager.openProjects } returns arrayOf(myFixture.project)

    setupProjectMetadataServiceMock()
    val sentryService = SentryService()
    waitForAssertion({
      assertTrue(sentryService.isInitialized)
    })

    val testException = RuntimeException("Test exception")

    sentryService.captureException(testException)

    verify(exactly = 1) { Sentry.captureException(testException) }
  }

  @Test
  fun testCreateSentryConfigForWebview() {
    FeatureFlagsTestUtil.setupFeatureFlags(
      testRootDisposable,
      mapOf(
        "intellij_enable_sentry" to true,
        "intellij_webview_error_sampling_rate" to 0.5,
        "intellij_webview_trace_sampling_rate" to 0.3,
      ),
    )

    every { mockVersionProvider.currentPluginVersion() } returns "1.2.3"

    val sentryService = SentryService()
    waitForAssertion({
      assertTrue(sentryService.isInitialized)
    })

    val config = sentryService.createSentryConfigForWebview()

    assertTrue(
      "Config should contain webview DSN",
      config.contains("dsn: 'https://<EMAIL>/4509294232207360'"),
    )
    assertTrue("Config should contain release version", config.contains("release: 'augment-intellij-webview@1.2.3'"))
    assertTrue("Config should contain production environment", config.contains("environment: 'production'"))
    assertTrue("Config should contain error sample rate", config.contains("errorSampleRate: 0.5"))
    assertTrue("Config should contain trace sample rate", config.contains("tracesSampleRate: 0.3"))
    assertTrue("Config should contain sendDefaultPii false", config.contains("sendDefaultPii: false"))
    assertTrue("Config should contain debug false for production", config.contains("debug: false"))
    assertTrue("Config should contain IntelliJ version tag", config.contains("'intellij.version':"))
    assertTrue("Config should contain plugin version tag", config.contains("'plugin.version': '1.2.3'"))

    waitForAssertion({
      assertNotNull(PluginStateService.instance.context)
    })

    val ctx = PluginStateService.instance.context!!
    assertTrue("Sentry should be enabled via feature flag", ctx.flags.sentryEnabled)

    assertEquals(
      "Webview error sampling rate should match",
      0.5,
      ctx.flags.webviewErrorSamplingRate,
      0.001,
    )
    assertEquals(
      "Webview trace sampling rate should match",
      0.3,
      ctx.flags.webviewTraceSamplingRate,
      0.001,
    )
  }
}
