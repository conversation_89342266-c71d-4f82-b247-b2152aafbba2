@file:Suppress("DEPRECATION") // We use MockAugmentAPI

package com.augmentcode.intellij.chat

import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.mock.MockAugmentAPI
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.rpc.ChatSmartPasteData
import com.augmentcode.rpc.ChatSmartPasteRequest
import com.augmentcode.rpc.FileDetails
import com.augmentcode.rpc.FindSymbolRequest
import com.augmentcode.rpc.FindSymbolRequestData
import com.augmentcode.rpc.ISearchScopeArgs
import com.intellij.testFramework.IndexingTestUtil
import com.intellij.testFramework.TestDataPath
import com.intellij.testFramework.fixtures.BasePlatformTestCase
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
@TestDataPath("\$CONTENT_ROOT/src/test/testData")
class FromChatInteractionsTest : BasePlatformTestCase() {
  override fun getTestDataPath() = "src/test/testData/chat"

  override fun setUp() {
    super.setUp()
    AugmentSettings.instance.modelName = null

    val mockAPI = MockAugmentAPI()
    application.registerOrReplaceServiceInstance(
      AugmentAPI::class.java,
      mockAPI,
      testRootDisposable,
    )
  }

  @Test
  fun testSmartPasteInsert() {
    myFixture.configureByFile("smartPasteInsert.go")
    val request =
      ChatSmartPasteRequest
        .newBuilder()
        .setData(
          ChatSmartPasteData
            .newBuilder()
            .setGeneratedCode("print(\"Hello from Augment!\")")
            .build(),
        ).build()
    runBlocking {
      ChatWebviewMessageClient(project)
        .chatSmartPaste(request)
    }
    myFixture.checkResultByFile("smartPasteInsert.expected.go")
  }

  @Test
  fun testSmartPasteReplace() {
    myFixture.configureByFile("smartPasteReplace.go")
    val request =
      ChatSmartPasteRequest
        .newBuilder()
        .setData(
          ChatSmartPasteData
            .newBuilder()
            .setGeneratedCode("print(\"Hello from Augment!\")")
            .build(),
        ).build()
    runBlocking {
      ChatWebviewMessageClient(project)
        .chatSmartPaste(request)
    }
    myFixture.checkResultByFile("smartPasteReplace.expected.go")
  }

  @Test
  fun testFindSymbol() {
    myFixture.configureByFile("AwesomeExample.xml")
    IndexingTestUtil.waitUntilIndexesAreReady(myFixture.project)
    val response =
      runBlocking {
        ChatWebviewMessageClient(project)
          .findSymbol(
            FindSymbolRequest
              .newBuilder()
              .setData(
                FindSymbolRequestData
                  .newBuilder()
                  .setQuery("awesomeElement1")
                  .build(),
              ).build(),
          )
      }
    assertEquals(1, response.dataCount)
    val result = response.dataList.first().file
    assertEquals("AwesomeExample.xml", result.pathName)
    assertEquals(2, result.range.start)
    assertEquals(6, result.range.stop)
    assertEquals(1, result.fullRange.startLineNumber)
    assertEquals(2, result.fullRange.startColumn)
    assertEquals(5, result.fullRange.endLineNumber)
    assertEquals(11, result.fullRange.endColumn)
  }

  @Test
  fun testFindSymbolNoSearchScope() {
    myFixture.configureByFile("AwesomeExample.xml")
    myFixture.configureByFile("AwesomeExample2.xml")
    IndexingTestUtil.waitUntilIndexesAreReady(myFixture.project)

    val request =
      FindSymbolRequest
        .newBuilder()
        .setData(
          FindSymbolRequestData
            .newBuilder()
            .setQuery("awesomeElement1")
            .build(),
        ).build()

    val response =
      runBlocking {
        ChatWebviewMessageClient(project)
          .findSymbol(request)
      }

    // No search scope provided, so we should get both results
    assertEquals(2, response.dataList.size)
    assertTrue(response.dataList.map { it.file.pathName }.toSet() == setOf("AwesomeExample.xml", "AwesomeExample2.xml"))
  }

  @Test
  fun testFindSymbolWithSearchScope() {
    myFixture.configureByFile("AwesomeExample.xml")
    myFixture.configureByFile("AwesomeExample2.xml")
    IndexingTestUtil.waitUntilIndexesAreReady(myFixture.project)

    val request =
      FindSymbolRequest
        .newBuilder()
        .setData(
          FindSymbolRequestData
            .newBuilder()
            .setQuery("awesomeElement1")
            .setSearchScope(
              ISearchScopeArgs
                .newBuilder()
                .addFiles(
                  FileDetails
                    .newBuilder()
                    .setPathName("AwesomeExample.xml")
                    .build(),
                ),
            ).build(),
        ).build()

    val response =
      runBlocking {
        ChatWebviewMessageClient(project)
          .findSymbol(request)
      }

    assertEquals(1, response.dataCount)
    val result = response.dataList.first().file
    assertEquals("AwesomeExample.xml", result.pathName)
  }
}
