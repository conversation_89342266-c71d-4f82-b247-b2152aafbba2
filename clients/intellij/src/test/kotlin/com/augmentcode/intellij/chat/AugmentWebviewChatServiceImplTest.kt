package com.augmentcode.intellij.chat

import com.augmentcode.intellij.guidelines.GuidelinesService
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class AugmentWebviewChatServiceImplTest : AugmentBasePlatformTestCase() {
  override fun setUp() {
    super.setUp()

    // Set up fake credentials for the test
    AugmentSettings.instance.apiToken = "test-token"
    AugmentSettings.instance.completionURL = "http://test-server"

    // Create a mock engine that returns feature flags with guidelines enabled
    val mockEngine = createMockEngineWithGuidelines()
    augmentHelpers().registerMockEngine(mockEngine)
  }

  private fun createMockEngineWithGuidelines(): MockEngine {
    return MockEngine { request ->
      when (request.url.encodedPath) {
        "/get-models" -> {
          // Return a response with feature flags that have guidelines enabled
          respond(
            content =
              """
              {
                "default_model": "test-model",
                "models": [
                  {
                    "name": "test-model",
                    "suggested_prefix_char_count": 100,
                    "suggested_suffix_char_count": 100
                  }
                ],
                "languages": [
                  {
                    "name": "Text",
                    "extensions": [".txt"]
                  }
                ],
                "feature_flags": {
                  "enable_guidelines": true,
                  "intellij_enable_user_guidelines": true,
                  "user_guidelines_length_limit": 2000
                }
              }
              """.trimIndent(),
            status = HttpStatusCode.OK,
            headers = headersOf(HttpHeaders.ContentType, "application/json"),
          )
        }
        else -> error("Unexpected request to ${request.url.encodedPath}")
      }
    }
  }

  private fun createMockEngineWithGuidelinesDisabled(): MockEngine {
    return MockEngine { request ->
      when (request.url.encodedPath) {
        "/get-models" -> {
          // Return a response with guidelines features disabled
          respond(
            content =
              """
              {
                "default_model": "test-model",
                "models": [
                  {
                    "name": "test-model",
                    "suggested_prefix_char_count": 100,
                    "suggested_suffix_char_count": 100
                  }
                ],
                "languages": [
                  {
                    "name": "Text",
                    "extensions": [".txt"]
                  }
                ],
                "feature_flags": {
                  "enable_guidelines": false,
                  "intellij_enable_user_guidelines": false,
                  "user_guidelines_length_limit": 2000
                }
              }
              """.trimIndent(),
            status = HttpStatusCode.OK,
            headers = headersOf(HttpHeaders.ContentType, "application/json"),
          )
        }
        else -> error("Unexpected request to ${request.url.encodedPath}")
      }
    }
  }

  private fun createMockEngineWithGuidelinesAndSmallLimit(): MockEngine {
    return MockEngine { request ->
      when (request.url.encodedPath) {
        "/get-models" -> {
          // Return a response with guidelines enabled but a small length limit
          respond(
            content =
              """
              {
                "default_model": "test-model",
                "models": [
                  {
                    "name": "test-model",
                    "suggested_prefix_char_count": 100,
                    "suggested_suffix_char_count": 100
                  }
                ],
                "languages": [
                  {
                    "name": "Text",
                    "extensions": [".txt"]
                  }
                ],
                "feature_flags": {
                  "enable_guidelines": true,
                  "intellij_enable_user_guidelines": true,
                  "user_guidelines_length_limit": 20
                }
              }
              """.trimIndent(),
            status = HttpStatusCode.OK,
            headers = headersOf(HttpHeaders.ContentType, "application/json"),
          )
        }
        else -> error("Unexpected request to ${request.url.encodedPath}")
      }
    }
  }

  @Test
  fun testSendGuidelinesStateToWebviewIsPublic() {
    // This test verifies that the sendGuidelinesStateToWebview method is public
    // and can be called from outside the class
    val chatService = ChatWebviewMessageClient(project)

    // This should compile without errors since the method is now public
    chatService.sendGuidelinesStateToWebview()

    // No assertions needed - we're just testing that the method is accessible
  }

  @Test
  fun testSendGuidelinesStateToWebviewWhenFeaturesEnabled() {
    // Set up mock engine with guidelines features enabled
    val mockEngine = createMockEngineWithGuidelines()
    augmentHelpers().registerMockEngine(mockEngine)

    // Set up test guidelines content
    val testGuidelines = "Test guidelines content"
    val guidelinesService = GuidelinesService.getInstance(project)
    guidelinesService.updateUserGuidelines(testGuidelines)

    // Create a chat service and call the method
    val chatService = ChatWebviewMessageClient(project)
    chatService.sendGuidelinesStateToWebview()

    // Verification is implicit - if the feature flags are correctly read and the method
    // doesn't throw exceptions, the test passes
  }

  @Test
  fun testSendGuidelinesStateToWebviewWhenFeaturesDisabled() {
    // Set up mock engine with guidelines features disabled
    val mockEngine = createMockEngineWithGuidelinesDisabled()
    augmentHelpers().registerMockEngine(mockEngine)

    // Create a chat service and call the method
    val chatService = ChatWebviewMessageClient(project)
    chatService.sendGuidelinesStateToWebview()

    // Verification is implicit - if the method correctly checks feature flags
    // and doesn't attempt to send anything when disabled, the test passes
  }

  @Test
  fun testSendGuidelinesStateToWebviewWithEmptyGuidelines() {
    // Set up mock engine with guidelines features enabled
    val mockEngine = createMockEngineWithGuidelines()
    augmentHelpers().registerMockEngine(mockEngine)

    // Set up empty guidelines content
    val guidelinesService = GuidelinesService.getInstance(project)

    guidelinesService.updateUserGuidelines("")

    // Create a chat service and call the method
    val chatService = ChatWebviewMessageClient(project)
    chatService.sendGuidelinesStateToWebview()

    // Verification is implicit - if the method correctly handles empty guidelines
    // and sets the enabled flag to false, the test passes
  }

  @Test
  fun testSendGuidelinesStateToWebviewWithOverLimitGuidelines() {
    // Set up mock engine with guidelines features enabled and a small length limit
    val mockEngine = createMockEngineWithGuidelinesAndSmallLimit()
    augmentHelpers().registerMockEngine(mockEngine)

    // Set up guidelines content that exceeds the limit
    val longGuidelines = "A".repeat(50) // This exceeds our test limit of 20 characters
    val guidelinesService = GuidelinesService.getInstance(project)
    guidelinesService.updateUserGuidelines(longGuidelines)

    // Create a chat service and call the method
    val chatService = ChatWebviewMessageClient(project)
    chatService.sendGuidelinesStateToWebview()

    // Verification is implicit - if the method correctly identifies over-limit guidelines
    // and sets the overLimit flag to true, the test passes
  }
}
