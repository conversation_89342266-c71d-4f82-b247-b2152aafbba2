package com.augmentcode.intellij.chat

import com.augmentcode.intellij.index.AugmentBlobState
import com.augmentcode.intellij.syncing.AugmentRemoteSyncingManager
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.rpc.FindFolderRequest
import com.augmentcode.rpc.FindFolderRequestData
import com.intellij.testFramework.TestDataPath
import com.intellij.testFramework.registerOrReplaceServiceInstance
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
@TestDataPath("\$CONTENT_ROOT/src/test/testData")
class FindFoldersTest : AugmentBasePlatformTestCase() {
  override fun getTestDataPath() = "src/test/testData/chat"

  private fun setupMocks(relPaths: List<String> = emptyList()) {
    val mockSyncManager =
      mockk<AugmentRemoteSyncingManager> {
        every { syncedBlobs() } answers {
          relPaths.map { path ->
            AugmentBlobState(
              remoteName = "hash($path).v1",
              relativePath = path,
              localName = "hash($path).v1",
              rootPath = "",
            )
          }
        }
      }
    project.registerOrReplaceServiceInstance(AugmentRemoteSyncingManager::class.java, mockSyncManager, testRootDisposable)
  }

  @Test
  fun testNoSelection() {
    setupMocks()
    val chatService = ChatWebviewMessageClient(project)

    myFixture.configureByText("simple.txt", "line 1<caret>")

    val chatRequest = runBlocking { chatService.baseChatRequestFromSelection(myFixture.editor) }
    assertEquals(null, chatRequest.selectedCode)
  }

  @Test
  fun testFindFolder() {
    val testFiles =
      listOf(
        "src/foo/bar/test1.txt",
        "src/foo/other/test2.txt",
      )
    setupMocks(testFiles)
    val chatService = ChatWebviewMessageClient(project)

    val request =
      FindFolderRequest
        .newBuilder()
        .setData(
          FindFolderRequestData
            .newBuilder()
            .setRelPath("bar")
            .setExactMatch(false)
            .setMaxResults(10)
            .build(),
        ).build()

    val response = runBlocking { chatService.findFolder(request) }
    val foundFolders = response.dataList.map { it.pathName }.toSet()
    assertTrue("Should find folder 'src/foo/bar'", foundFolders.contains("src/foo/bar"))
  }

  @Test
  fun testFolderHierarchy() {
    val testFiles =
      listOf(
        "src/foo/bar/test1.txt",
        "src/foo/other/test2.txt",
        "src/foo/bar/baz/test3.txt",
      )
    setupMocks(testFiles)
    val chatService = ChatWebviewMessageClient(project)

    val request =
      FindFolderRequest
        .newBuilder()
        .setData(
          FindFolderRequestData
            .newBuilder()
            .setRelPath("foo")
            .setExactMatch(false)
            .setMaxResults(10)
            .build(),
        ).build()

    val response = runBlocking { chatService.findFolder(request) }
    val foundFolders = response.dataList.map { it.pathName }.toSet()
    assertTrue("Should find folder 'src/foo'", foundFolders.contains("src/foo"))
    assertTrue("Should find subfolder 'src/foo/bar'", foundFolders.contains("src/foo/bar"))
    assertTrue("Should find subfolder 'src/foo/other'", foundFolders.contains("src/foo/other"))
  }

  @Test
  fun testExactMatch() {
    val testFiles =
      listOf(
        "src/foo/bar/test1.txt",
        "src/other/bar/test2.txt",
      )
    setupMocks(testFiles)
    val chatService = ChatWebviewMessageClient(project)

    val request =
      FindFolderRequest
        .newBuilder()
        .setData(
          FindFolderRequestData
            .newBuilder()
            .setRelPath("src/foo/bar")
            .setExactMatch(true)
            .setMaxResults(10)
            .build(),
        ).build()

    val response = runBlocking { chatService.findFolder(request) }
    assertEquals("Should find exactly one folder with exact match 'src/foo/bar'", 1, response.dataCount)
    assertEquals("Should find folder 'src/foo/bar'", "src/foo/bar", response.dataList[0].pathName)
  }

  @Test
  fun testPartialMatch() {
    val testFiles =
      listOf(
        "src/foo/bar/test1.txt",
        "src/foo/baz/test2.txt",
        "src/other/bar/test3.txt",
      )
    setupMocks(testFiles)
    val chatService = ChatWebviewMessageClient(project)

    val request =
      FindFolderRequest
        .newBuilder()
        .setData(
          FindFolderRequestData
            .newBuilder()
            .setRelPath("ba")
            .setExactMatch(false)
            .setMaxResults(10)
            .build(),
        ).build()

    val response = runBlocking { chatService.findFolder(request) }
    val foundFolders = response.dataList.map { it.pathName }.toSet()
    assertTrue("Should find folder 'src/foo/bar'", foundFolders.contains("src/foo/bar"))
    assertTrue("Should find folder 'src/foo/baz'", foundFolders.contains("src/foo/baz"))
    assertTrue("Should find folder 'src/other/bar'", foundFolders.contains("src/other/bar"))
  }

  @Test
  fun testPartialMatchWithTrailingSlash() {
    val testFiles =
      listOf(
        "src/foo/bar/test1.txt",
        "src/foo/baz/test2.txt",
        "src/other/test3.txt",
      )
    setupMocks(testFiles)
    val chatService = ChatWebviewMessageClient(project)

    val request =
      FindFolderRequest
        .newBuilder()
        .setData(
          FindFolderRequestData
            .newBuilder()
            .setRelPath("src/")
            .setExactMatch(false)
            .setMaxResults(10)
            .build(),
        ).build()

    val response = runBlocking { chatService.findFolder(request) }
    val foundFolders = response.dataList.map { it.pathName }.toSet()
    assertEquals(
      "Should find 'src' folder hierarchy",
      setOf("src/foo", "src/foo/bar", "src/foo/baz", "src/other"), // note that the root 'src' folder is not included
      foundFolders,
    )
  }

  @Test
  fun testPartialMatchWithSlashBetweenTerms() {
    val testFiles =
      listOf(
        "vscode/scripts/test1.txt",
        "vscode/scripts/subfolder/test2.txt",
        "other/scripts/test3.txt",
      )
    setupMocks(testFiles)
    val chatService = ChatWebviewMessageClient(project)

    val request =
      FindFolderRequest
        .newBuilder()
        .setData(
          FindFolderRequestData
            .newBuilder()
            .setRelPath("vscode/scripts")
            .setExactMatch(false)
            .setMaxResults(10)
            .build(),
        ).build()

    val response = runBlocking { chatService.findFolder(request) }
    val foundFolders = response.dataList.map { it.pathName }.toSet()
    assertEquals(
      "Should find 'vscode/scripts' and its subfolders",
      setOf("vscode/scripts", "vscode/scripts/subfolder"),
      foundFolders,
    )
  }

  @Test
  fun testPartialMatchShowsChildFolders() {
    val testFiles =
      listOf(
        "vscode/src/main/test1.txt",
        "vscode/docs/guide/test2.txt",
        "vscode/scripts/test3.txt",
      )
    setupMocks(testFiles)
    val chatService = ChatWebviewMessageClient(project)

    // Test with a limit of 4 results
    val request =
      FindFolderRequest
        .newBuilder()
        .setData(
          FindFolderRequestData
            .newBuilder()
            .setRelPath("vscode")
            .setExactMatch(false)
            .setMaxResults(4) // Set specific limit
            .build(),
        ).build()

    val response = runBlocking { chatService.findFolder(request) }
    val foundFolders = response.dataList.map { it.pathName }.toSet()
    assertEquals(
      "Should find 'vscode' and its immediate subfolders, limited to 4 results, prioritizing shallower paths",
      setOf("vscode", "vscode/src", "vscode/docs", "vscode/scripts"),
      foundFolders,
    )

    // Add a second test case with a smaller limit
    val requestWithSmallerLimit =
      FindFolderRequest
        .newBuilder()
        .setData(
          FindFolderRequestData
            .newBuilder()
            .setRelPath("vscode")
            .setExactMatch(false)
            .setMaxResults(2) // Smaller limit
            .build(),
        ).build()

    val responseWithSmallerLimit = runBlocking { chatService.findFolder(requestWithSmallerLimit) }
    val foundFoldersWithSmallerLimit = responseWithSmallerLimit.dataList.map { it.pathName }.toSet()
    assertEquals(
      "Should find only 'vscode' and one immediate subfolder when limited to 2 results",
      setOf("vscode", "vscode/docs"), // Should get root and first subfolder
      foundFoldersWithSmallerLimit,
    )
  }

  @Test
  fun testFindTopLevelFolder() {
    val testFiles =
      listOf(
        "src/foo/bar/test1.txt",
        "src/baz/test2.txt",
        "test/other/test3.txt",
      )
    setupMocks(testFiles)
    val chatService = ChatWebviewMessageClient(project)

    val request =
      FindFolderRequest
        .newBuilder()
        .setData(
          FindFolderRequestData
            .newBuilder()
            .setRelPath("src")
            .setExactMatch(false)
            .setMaxResults(10)
            .build(),
        ).build()

    val response = runBlocking { chatService.findFolder(request) }
    val foundFolders = response.dataList.map { it.pathName }.toSet()
    assertTrue("Should find folder 'src'", foundFolders.contains("src"))
    assertTrue("Should find subfolder 'src/foo'", foundFolders.contains("src/foo"))
    assertTrue("Should find subfolder 'src/baz'", foundFolders.contains("src/baz"))
  }

  @Test
  fun testFindFolderPerformance() {
    // turn on to see verbose output
    val verbose = false
    val maxTimeThreshold = 400L // if the time exceeds this, the UX is pretty bad
    val maxTimeWarningThreshold = 200L // if the time exceeds this, it's maybe acceptable for huge datasets only

    // Test with progressively larger datasets
    listOf(100, 500, 2_000, 5_000, 10_000, 100_000).forEach { size ->
      if (verbose) println("\n=== Testing with dataset size: $size ===")

      // Create deep nested paths to maximize traversal time
      val testPaths =
        (1..size).map { i ->
          // Create very deep paths with many segments to stress the folder traversal
          "level1/level2/level3/level4/level5/folder${i % 50}/subfolder${i % 30}/deep${i % 20}/final$i/file.txt"
        }

      setupMocks(testPaths)
      val chatService = ChatWebviewMessageClient(project)

      // Test cases designed for worst-case performance
      listOf(
        Triple("Non-existent deep path", "nonexistent/very/deep/path", true), // Forces full traversal
        Triple("Common substring match", "level", false), // Forces checking many paths
        Triple("Deep partial match", "deep", false), // Must traverse to deep levels
        Triple("Suffix pattern match", "final", false), // Must check all paths fully
      ).forEach { (testName, searchPath, exactMatch) ->
        val request =
          FindFolderRequest
            .newBuilder()
            .setData(
              FindFolderRequestData
                .newBuilder()
                .setRelPath(searchPath)
                .setExactMatch(exactMatch)
                .setMaxResults(10)
                .build(),
            ).build()

        // Multiple runs to get stable measurements
        val runs = 5
        val timings = mutableListOf<Double>()

        // Warmup run
        runBlocking { chatService.findFolder(request) }

        repeat(runs) {
          val startTime = System.nanoTime()
          val response = runBlocking { chatService.findFolder(request) }
          if (searchPath == "nonexistent/very/deep/path") {
            assertTrue("Expected no results for non-existent path", response.dataList.isEmpty())
          } else {
            assertTrue("Expected > 0 results for search path", response.dataList.isNotEmpty())
          }
          val durationMs = (System.nanoTime() - startTime) / 1_000_000.0
          timings.add(durationMs)
        }

        val avgTime = timings.average()
        val maxTime = timings.maxOrNull() ?: 0.0
        val minTime = timings.minOrNull() ?: 0.0

        if (verbose) {
          println(
            """
            |Test: $testName
            |  Dataset size: $size
            |  Search path: $searchPath
            |  Exact match: $exactMatch
            |  Avg time: $avgTime ms
            |  Min time: $minTime ms
            |  Max time: $maxTime ms
            |
            """.trimMargin(),
          )
        }

        // Only assert on worst-case performance
        assertTrue(
          "Worst-case performance test '$testName' with size $size took too long: $maxTime ms",
          maxTime < maxTimeThreshold,
        )
        if (maxTime > maxTimeWarningThreshold) {
          println("WARNING: Test '$testName' with size $size exceeded warning threshold: $maxTime ms.")
        }
      }
    }
  }

  @Test
  fun testFindFolderPrioritizesExactNames() {
    val testFiles =
      listOf(
        "clients/vscode/__tests__/unit/test1.txt",
        "clients/vscode/__tests__/integration/test2.txt",
        "server/__tests__/api/test3.txt",
        "libs/__tests__/common/test4.txt",
      )
    setupMocks(testFiles)
    val chatService = ChatWebviewMessageClient(project)

    val request =
      FindFolderRequest
        .newBuilder()
        .setData(
          FindFolderRequestData
            .newBuilder()
            .setRelPath("__tests__")
            .setExactMatch(false)
            .setMaxResults(3)
            .build(),
        ).build()

    val response = runBlocking { chatService.findFolder(request) }
    val foundFolders = response.dataList.map { it.pathName }.toSet()

    // Should find all __tests__ directories first
    assertEquals(
      "Should find all directories named exactly '__tests__' before any child directories",
      setOf(
        "clients/vscode/__tests__",
        "server/__tests__",
        "libs/__tests__",
      ),
      foundFolders,
    )
  }
}
