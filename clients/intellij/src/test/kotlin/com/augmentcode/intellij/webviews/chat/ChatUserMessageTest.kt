package com.augmentcode.intellij.webviews.chat

import com.augmentcode.api.ChatRequest
import com.augmentcode.api.Exchange
import com.augmentcode.intellij.chat.FIRST_CHAT_EXCHANGE_ID_FOR_SELECTION
import com.augmentcode.intellij.mock.*
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.pluginstate.PluginStateService
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.sidecar.clientinterfaces.PluginFileStore
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.GsonUtil
import com.augmentcode.intellij.testutils.HttpUtil
import com.augmentcode.intellij.testutils.HttpUtil.streamRespond
import com.augmentcode.intellij.testutils.waitForAssertion
import com.augmentcode.intellij.testutils.waitForAssertionAsync
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.engine.mock.toByteArray
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class ChatUserMessageTest : AugmentBasePlatformTestCase() {
  @Test
  fun testChatResponse() =
    runBlocking {
      var lastChatRequest: ChatRequest? = null
      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/chat-stream" -> {
              lastChatRequest = GsonUtil.createApiGson().fromJson(request.body.toByteArray().decodeToString(), ChatRequest::class.java)
              streamRespond(
                coroutineScope = augmentHelpers().createCoroutineScope(Dispatchers.IO),
                chunks =
                  listOf(
                    """{"text": "foo", "workspaceFileChunks": [""" +
                      """{"char_start": "10", "char_end": "20", "blob_name": "hash(chat.txt).v1", "file": """ +
                      """{"repoRoot": "", "pathName": "chat.txt"}}""" +
                      """]}""",
                    """{"text": "bar"}""",
                    """{"text": "baz"}""",
                  ),
              )
            }
            else ->
              respond(
                content = "Not Found: ${request.url.encodedPath}",
                status = HttpStatusCode.NotFound,
              )
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      myFixture.configureByText("chat.txt", "Hello, chat!")
      myFixture.configureByText("foo.txt", "Hello, <selection>world</selection>!")
      assertNull(myFixture.editor.getUserData(FIRST_CHAT_EXCHANGE_ID_FOR_SELECTION))

      AugmentSettings.instance.apiToken = "test-token"
      AugmentSettings.instance.completionURL = "http://test-server"

      waitForAssertion({
        assertEquals(PluginState.ENABLED, PluginStateService.instance.state)
      })

      val messagingService = ChatMessagingService.getInstance(project)
      val chatUserMessage =
        """
        {
          "type":"async-wrapper",
          "requestId":"abc-123",
          "error":null,
          "baseMsg": {
            "type": "chat-user-message",
            "data": {
              "text": "Test!",
              "chatHistory": [
                {
                  "requestId": "abcdef12345",
                  "requestMessage": "Previous message",
                  "responseText": "Previous response"
                }
              ]
            }
          }
        }
        """.trimIndent()

      val responses =
        runBlocking {
          messagingService.processMessageFromWebview(chatUserMessage).toList()
        }

      assertNotNull(lastChatRequest)
      assertEquals("Previous message", lastChatRequest?.chatHistory?.firstOrNull()?.requestMessage)
      assertEquals("Previous response", lastChatRequest?.chatHistory?.firstOrNull()?.responseText)

      assertEquals("new", lastChatRequest?.contextCodeExchangeRequestId)
      assertNotNull(myFixture.editor.getUserData(FIRST_CHAT_EXCHANGE_ID_FOR_SELECTION))

      // Extract the actual requestId from the first response
      val actualRequestIds =
        listOf(
          responses[0].toPrettyJson().let { json ->
            val regex = """"requestId": "([^"]+)"""".toRegex()
            regex.findAll(json).toList().last().groupValues.last()
          },
          responses[1].toPrettyJson().let { json ->
            val regex = """"requestId": "([^"]+)"""".toRegex()
            regex.findAll(json).toList().last().groupValues.last()
          },
          responses[2].toPrettyJson().let { json ->
            val regex = """"requestId": "([^"]+)"""".toRegex()
            regex.findAll(json).toList().last().groupValues.last()
          },
        )

      val expected =
        listOf(
          """
          {
            "requestId": "abc-123",
            "error": "",
            "baseMsg": {
              "data": {
                "text": "foo",
                "requestId": "${actualRequestIds[0]}",
                "streaming": true,
                "workspaceFileChunks": [
                  {
                    "char_start": "10",
                    "char_end": "20",
                    "blob_name": "hash(chat.txt).v1",
                    "file": {
                      "repoRoot": "",
                      "pathName": "chat.txt"
                    }
                  }
                ],
                "nodes": []
              },
              "type": "chat-model-reply"
            },
            "streamCtx": {
              "streamMsgIdx": 0,
              "streamNextRequestId": "abc-123-0",
              "isStreamComplete": false
            },
            "type": "async-wrapper"
          }
          """.trimIndent(),
          """
          {
            "requestId": "abc-123-0",
            "error": "",
            "baseMsg": {
              "data": {
                "text": "bar",
                "requestId": "${actualRequestIds[1]}",
                "streaming": true,
                "workspaceFileChunks": [],
                "nodes": []
              },
              "type": "chat-model-reply"
            },
            "streamCtx": {
              "streamMsgIdx": 1,
              "streamNextRequestId": "abc-123-1",
              "isStreamComplete": false
            },
            "type": "async-wrapper"
          }
          """.trimIndent(),
          """
          {
            "requestId": "abc-123-1",
            "error": "",
            "baseMsg": {
              "data": {
                "text": "baz",
                "requestId": "${actualRequestIds[2]}",
                "streaming": true,
                "workspaceFileChunks": [],
                "nodes": []
              },
              "type": "chat-model-reply"
            },
            "streamCtx": {
              "streamMsgIdx": 2,
              "streamNextRequestId": "abc-123-2",
              "isStreamComplete": false
            },
            "type": "async-wrapper"
          }
          """.trimIndent(),
          """
          {
            "requestId": "abc-123-2",
            "error": "",
            "baseMsg": {
              "type": "empty"
            },
            "streamCtx": {
              "streamMsgIdx": 2,
              "streamNextRequestId": "",
              "isStreamComplete": true
            },
            "type": "async-wrapper"
          }
          """.trimIndent(),
        )

      assertEquals(expected.size, responses.size)
      assertEquals(expected[0], responses[0].toPrettyJson())
      assertEquals(expected[1], responses[1].toPrettyJson())
      assertEquals(expected[2], responses[2].toPrettyJson())
      assertEquals(expected[3], responses[3].toPrettyJson())

      // test context persistence
      myFixture.editor.putUserData(FIRST_CHAT_EXCHANGE_ID_FOR_SELECTION, "mock-previous-exchange-id")
      runBlocking {
        // to update recorderLastChatRequest
        messagingService.processMessageFromWebview(chatUserMessage).toList()
      }
      assertEquals("mock-previous-exchange-id", lastChatRequest?.contextCodeExchangeRequestId)

      // check we clean up after selection is removed
      myFixture.editor.selectionModel.removeSelection()
      assertNull(myFixture.editor.getUserData(FIRST_CHAT_EXCHANGE_ID_FOR_SELECTION))
    }

  @Test
  fun testClassifyAndDistillMemories() =
    runBlocking {
      // Create a mock engine that returns our feature flags and handles the chat stream
      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> {
              // Return feature flags with classify and distill prompt
              respond(
                content =
                  """
                  {
                    "default_model": "test-model",
                    "models": [
                      {
                        "name": "test-model",
                        "suggested_prefix_char_count": 100,
                        "suggested_suffix_char_count": 100
                      }
                    ],
                    "languages": [
                      {
                        "name": "Text",
                        "extensions": [".txt"]
                      }
                    ],
                    "feature_flags": {
                      "memories_params": "{\"classify_and_distill_prompt\": \"the message goes here: {message}\"}"
                    }
                  }
                  """.trimIndent(),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            "/chat-stream" -> {
              // Verify the request contains the modified prompt
              val requestBody = request.body.toByteArray().decodeToString()
              val chatRequest = GsonUtil.createApiGson().fromJson(requestBody, ChatRequest::class.java)

              // Check that the prompt was applied correctly
              assertTrue(chatRequest.message.startsWith("the message goes here: Tell me about the project structure"))

              // Use channel to send streaming response
              val chunk =
                """{"text": "{\"explanation\":\"This is important information about """ +
                  """the project structure\",\"worthRemembering\":true,""" +
                  """\"content\":\"The project uses a client-server architecture with TypeScript frontend\"}"}""" + "\n"
              streamRespond(
                coroutineScope = augmentHelpers().createCoroutineScope(Dispatchers.IO),
                chunks = listOf(chunk),
              )
            }
            "/report-error" -> {
              respond(
                content = "{}",
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else ->
              respond(
                content = "Not Found: ${request.url.encodedPath}",
                status = HttpStatusCode.NotFound,
              )
          }
        }

      // Register the mock engine
      HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

      // Set up credentials so the API client can be created
      AugmentSettings.instance.apiToken = "test-token"
      AugmentSettings.instance.completionURL = "http://test-server"

      waitForAssertion({
        assertNotNull(PluginStateService.instance.context?.flags)
      })

      val messagingService = ChatMessagingService.getInstance(project)

      // Create a chat message with memoriesInfo.isClassifyAndDistill set to true
      val chatUserMessage =
        """
        {
          "type":"async-wrapper",
          "requestId":"abc-123",
          "error":null,
          "baseMsg": {
            "type": "chat-user-message",
            "data": {
              "text": "Tell me about the project structure",
              "chatHistory": [],
              "memoriesInfo": {
                "isClassifyAndDistill": true
              }
            }
          }
        }
        """.trimIndent()

      // Process the message
      val response = messagingService.processMessageFromWebview(chatUserMessage).toList()[0]

      // The response should contain the JSON from the mock API
      val responseJson = response.toPrettyJson()
      assertTrue(
        responseJson.contains("\"text\": \"{\\\"explanation\\\":\\\"This is important information about the project structure\\\""),
      )
      assertTrue(responseJson.contains("\\\"worthRemembering\\\":true"))
      assertTrue(responseJson.contains("\\\"content\\\":\\\"The project uses a client-server architecture with TypeScript frontend\\\"}\""))
    }

  @Test
  fun testChatHistoryTruncation() =
    runBlocking {
      // Set up API token and completion URL
      AugmentSettings.instance.apiToken = "test-token"
      AugmentSettings.instance.completionURL = "http://test-server"

      // Create a mock engine to handle API requests
      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> {
              respond(
                content =
                  """
                  {
                    "default_model": "test-model",
                    "models": [
                      {
                        "name": "test-model",
                        "suggested_prefix_char_count": 100,
                        "suggested_suffix_char_count": 100
                      }
                    ],
                    "languages": [
                      {
                        "name": "Text",
                        "extensions": [".txt"]
                      }
                    ],
                    "feature_flags": {}
                  }
                  """.trimIndent(),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            "/chat-stream" -> {
              // Parse the request body to check chat history truncation
              val requestBody = request.body.toByteArray().decodeToString()
              val chatRequest = GsonUtil.createApiGson().fromJson(requestBody, ChatRequest::class.java)

              // Verify that chat history has been truncated
              assertTrue(
                "Chat history should be truncated (was: ${chatRequest.chatHistory.size}, expected less than 50)",
                chatRequest.chatHistory.size < 50,
              )

              // Return a simple response
              streamRespond(
                coroutineScope = augmentHelpers().createCoroutineScope(Dispatchers.IO),
                chunks = listOf("""{"text": "Response with truncated history"}"""),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else ->
              respond(
                content = "Not Found: ${request.url.encodedPath}",
                status = HttpStatusCode.NotFound,
              )
          }
        }
      HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

      // Create a messaging service
      val messagingService = ChatMessagingService.getInstance(project)

      // Create a large string that will be used in each exchange to exceed the 800,000 char limit
      // Each exchange will have approximately 20,000 chars, so 50 exchanges should exceed 800,000
      val largeString = "x".repeat(10000)

      // Create a chat history with 50 exchanges, each with large content
      val largeHistory =
        (1..50).map { i ->
          Exchange().apply {
            requestId = "req-$i"
            requestMessage = "Request message $i with large content: $largeString"
            responseText = "Response text $i with large content: $largeString"
          }
        }

      // Create a chat user message with the large history
      val chatUserMessage =
        """
        {
          "type":"async-wrapper",
          "requestId":"abc-123",
          "error":null,
          "baseMsg": {
            "type": "chat-user-message",
            "data": {
              "text": "Test message with large history",
              "chatHistory": ${GsonUtil.createApiGson().toJson(largeHistory)}
            }
          }
        }
        """.trimIndent()

      // Process the message and collect responses
      val responses = messagingService.processMessageFromWebview(chatUserMessage).toList()

      // Verify we got a response
      assertTrue(responses.isNotEmpty())

      // Verify the response contains the expected text
      val responseJson = responses.first().toPrettyJson()
      assertTrue("Response should contain the expected text: $responseJson", responseJson.contains("Response with truncated history"))
    }

  @Test
  fun testImageHydration() =
    runBlocking {
      // Set up API token and completion URL
      AugmentSettings.instance.apiToken = "test-token"
      AugmentSettings.instance.completionURL = "http://test-server"

      // Create a mock image to store in the plugin file store
      val mockImageBytes = "mock-image-data".toByteArray()
      val mockImageId = "test-image-123"

      // Set up the plugin file store with our mock image
      val pluginFileStore = PluginFileStore(project)
      pluginFileStore.saveAsset(mockImageId, mockImageBytes)

      // Create a mock engine to handle API requests and verify image hydration
      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> {
              respond(
                content =
                  """
                  {
                    "default_model": "test-model",
                    "models": [
                      {
                        "name": "test-model",
                        "suggested_prefix_char_count": 100,
                        "suggested_suffix_char_count": 100
                      }
                    ],
                    "languages": [
                      {
                        "name": "Text",
                        "extensions": [".txt"]
                      }
                    ],
                    "feature_flags": {}
                  }
                  """.trimIndent(),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            "/chat-stream" -> {
              // Parse the request body to check image hydration
              val requestBody = request.body.toByteArray().decodeToString()
              val chatRequest = GsonUtil.createApiGson().fromJson(requestBody, ChatRequest::class.java)

              // Verify that the request contains a node with type IMAGE (2) instead of IMAGE_ID (3)
              val imageNode = chatRequest.nodes?.find { it.type == 2 } // 2 = IMAGE

              // Verify that the image data was properly hydrated
              assertNotNull("Image node should be present", imageNode)
              assertNotNull("Image node should have image_node field", imageNode?.image_node)

              // The image data should be base64 encoded
              val base64ImageData = imageNode?.image_node?.image_data
              assertNotNull("Image data should be present", base64ImageData)

              // Decode the base64 data and verify it matches our original image
              val decodedImageData = java.util.Base64.getDecoder().decode(base64ImageData)
              assertEquals(
                "Decoded image data should match original image",
                String(mockImageBytes),
                String(decodedImageData),
              )

              // Return a simple response
              streamRespond(
                coroutineScope = augmentHelpers().createCoroutineScope(Dispatchers.IO),
                chunks = listOf("""{"text": "Response with hydrated image"}"""),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else ->
              respond(
                content = "Not Found: ${request.url.encodedPath}",
                status = HttpStatusCode.NotFound,
              )
          }
        }
      HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

      // Create a messaging service
      val messagingService = ChatMessagingService.getInstance(project)

      // Create a chat message with an IMAGE_ID node
      val chatUserMessage =
        """
        {
          "type":"async-wrapper",
          "requestId":"abc-123",
          "error":null,
          "baseMsg": {
            "type": "chat-user-message",
            "data": {
              "text": "Test message with image",
              "chatHistory": [],
              "nodes": [
                {
                  "id": 0,
                  "type": 3,
                  "imageIdNode": {
                    "imageId": "test-image-123",
                    "format": 1
                  }
                }
              ]
            }
          }
        }
        """.trimIndent()

      // Process the message and collect responses
      val responses = messagingService.processMessageFromWebview(chatUserMessage).toList()

      // Verify we got a response
      assertTrue(responses.isNotEmpty())

      // Verify the response contains the expected text
      val responseJson = responses.first().toPrettyJson()
      assertTrue(
        "Response should contain the expected text",
        responseJson.contains("Response with hydrated image"),
      )
    }

  @Test
  fun testPreferenceCollectionWithInsufficientModels() =
    runBlocking {
      // Enable preference collection but provide insufficient models
      System.setProperty("augmentcode.preferencesCollectionEnabled", "true")

      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> {
              respond(
                content =
                  """
                  {
                    "default_model": "test-model",
                    "models": [
                      {
                        "name": "test-model",
                        "suggested_prefix_char_count": 100,
                        "suggested_suffix_char_count": 100
                      }
                    ],
                    "languages": [
                      {
                        "name": "Text",
                        "extensions": [".txt"]
                      }
                    ],
                    "feature_flags": {
                      "intellij_preference_collection_allowed_min_version": "0.0.0",
                      "elo_model_configuration": "{\"highPriorityModels\": [\"model1\"], \"regularBattleModels\": [\"model2\"], \"highPriorityThreshold\": 0.5}"
                    }
                  }
                  """.trimIndent(),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            "/chat-stream" -> {
              streamRespond(
                coroutineScope = augmentHelpers().createCoroutineScope(Dispatchers.IO),
                chunks = listOf("""{"text": "Regular chat response"}"""),
              )
            }
            else ->
              respond(
                content = "Not Found: ${request.url.encodedPath}",
                status = HttpStatusCode.NotFound,
              )
          }
        }
      HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

      AugmentSettings.instance.apiToken = "test-token"
      AugmentSettings.instance.completionURL = "http://test-server"

      // Wait for context to fetch flags
      waitForAssertionAsync({
        assertNotNull(PluginStateService.instance.context?.flags)
      }, timeoutMs = 5000) // Increase from default 1000ms

      val messagingService = ChatMessagingService.getInstance(project)
      val chatUserMessage =
        """
        {
          "type":"async-wrapper",
          "requestId":"abc-123",
          "error":null,
          "baseMsg": {
            "type": "chat-user-message",
            "data": {
              "text": "Test message",
              "chatHistory": []
            }
          }
        }
        """.trimIndent()

      val responses = messagingService.processMessageFromWebview(chatUserMessage).toList()

      // Should get an error response since models are insufficient
      assertTrue(responses.isNotEmpty())
      val responseJson = responses.first().toPrettyJson()
      assertTrue(responseJson.contains("Model comparison is not available at this time."))
    }

  @Test
  fun testSilentFlagIsPassedThroughToAPI() {
    runBlocking {
      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/chat-stream" -> {
              val requestBody = request.body.toByteArray().decodeToString()
              val chatRequest = GsonUtil.createApiGson().fromJson(requestBody, ChatRequest::class.java)

              // Verify that the silent flag is properly set
              assertTrue("Silent flag should be true for silent requests", chatRequest.silent == true)

              this.streamRespond(
                coroutineScope = this@runBlocking,
                chunks = listOf("""{"text": "Test response"}"""),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }

      HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

      AugmentSettings.instance.apiToken = "test-token"
      AugmentSettings.instance.completionURL = "http://test-server"

      val messagingService = ChatMessagingService.getInstance(project)

      val chatUserMessage =
        """
        {
          "type":"async-wrapper",
          "requestId":"abc-123",
          "error":null,
          "baseMsg": {
            "type": "chat-user-message",
            "data": {
              "text": "Test silent message",
              "chatHistory": [],
              "silent": true
            }
          }
        }
        """.trimIndent()

      val responses = messagingService.processMessageFromWebview(chatUserMessage).toList()
      assertTrue("Should receive responses for silent messages", responses.isNotEmpty())
    }
  }

  @Test
  fun testClassifyAndDistillRequestsAreMarkedSilent() {
    runBlocking {
      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/chat-stream" -> {
              val requestBody = request.body.toByteArray().decodeToString()
              val chatRequest = GsonUtil.createApiGson().fromJson(requestBody, ChatRequest::class.java)

              // Verify that classify and distill requests are automatically marked as silent
              assertTrue("Classify and distill requests should be marked as silent", chatRequest.silent == true)

              this.streamRespond(
                coroutineScope = this@runBlocking,
                chunks = listOf("""{"text": "Enhanced prompt"}"""),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }

      HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

      AugmentSettings.instance.apiToken = "test-token"
      AugmentSettings.instance.completionURL = "http://test-server"

      val messagingService = ChatMessagingService.getInstance(project)

      val chatUserMessage =
        """
        {
          "type":"async-wrapper",
          "requestId":"abc-123",
          "error":null,
          "baseMsg": {
            "type": "chat-user-message",
            "data": {
              "text": "Test prompt enhancement",
              "chatHistory": [],
              "memoriesInfo": {
                "isClassifyAndDistill": true
              }
            }
          }
        }
        """.trimIndent()

      val responses = messagingService.processMessageFromWebview(chatUserMessage).toList()
      assertTrue("Should receive responses for classify and distill requests", responses.isNotEmpty())
    }
  }
}
