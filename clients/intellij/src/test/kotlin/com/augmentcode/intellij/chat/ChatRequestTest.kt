@file:Suppress("DEPRECATION") // We use MockAugmentAPI

package com.augmentcode.intellij.chat

import com.augmentcode.api.ChatRequest
import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.memories.MemoriesService
import com.augmentcode.intellij.mock.MockAugmentAPI
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.GsonUtil
import com.augmentcode.intellij.testutils.HttpUtil
import com.augmentcode.intellij.testutils.waitForAssertion
import com.augmentcode.rpc.ChatUserMessageRequest
import com.intellij.testFramework.TestDataPath
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.engine.mock.toByteArray
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import io.ktor.utils.io.ByteReadChannel
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
@TestDataPath("\$CONTENT_ROOT/src/test/testData")
class ChatRequestTest : AugmentBasePlatformTestCase() {
  override fun getTestDataPath() = "src/test/testData/chat"

  @Test
  fun testNoSelection() {
    val mockAPI = MockAugmentAPI()
    application.registerOrReplaceServiceInstance(
      AugmentAPI::class.java,
      mockAPI,
      testRootDisposable,
    )

    val chatService = ChatWebviewMessageClient(project)

    myFixture.configureByText("simple.txt", "line 1<caret>")

    val chatRequest = runBlocking { chatService.baseChatRequestFromSelection(myFixture.editor) }
    assertEquals(null, chatRequest.selectedCode)
  }

  @Test
  fun testSelectionWholeFile() {
    val mockAPI = MockAugmentAPI()
    application.registerOrReplaceServiceInstance(
      AugmentAPI::class.java,
      mockAPI,
      testRootDisposable,
    )

    val chatService = ChatWebviewMessageClient(project)

    myFixture.configureByText("simple.txt", "<selection>line 1\nline 2\nline 3\n</selection>")

    val chatRequest = runBlocking { chatService.baseChatRequestFromSelection(myFixture.editor) }
    assertEquals("Text", chatRequest.lang)
    assertEquals("simple.txt", chatRequest.path)
    assertEquals("line 1\nline 2\nline 3\n", chatRequest.selectedCode)
    assertEquals("", chatRequest.prefix)
    assertEquals("", chatRequest.suffix)
  }

  @Test
  fun testSelectionPrefix() {
    val mockAPI = MockAugmentAPI()
    application.registerOrReplaceServiceInstance(
      AugmentAPI::class.java,
      mockAPI,
      testRootDisposable,
    )

    val chatService = ChatWebviewMessageClient(project)

    myFixture.configureByText("simple.txt", "<selection>line 1</selection>\nline 2\nline 3\n")

    val chatRequest = runBlocking { chatService.baseChatRequestFromSelection(myFixture.editor) }
    assertEquals("Text", chatRequest.lang)
    assertEquals("simple.txt", chatRequest.path)
    assertEquals("", chatRequest.prefix)
    assertEquals("line 1", chatRequest.selectedCode)
    assertEquals("\nline 2\nline 3\n", chatRequest.suffix)
  }

  @Test
  fun testSelectionMiddle() {
    val mockAPI = MockAugmentAPI()
    application.registerOrReplaceServiceInstance(
      AugmentAPI::class.java,
      mockAPI,
      testRootDisposable,
    )

    val chatService = ChatWebviewMessageClient(project)

    myFixture.configureByText("simple.txt", "line 1\n<selection>line 2</selection>\nline 3\n")

    val chatRequest = runBlocking { chatService.baseChatRequestFromSelection(myFixture.editor) }
    assertEquals("Text", chatRequest.lang)
    assertEquals("simple.txt", chatRequest.path)
    assertEquals("line 1\n", chatRequest.prefix)
    assertEquals("line 2", chatRequest.selectedCode)
    assertEquals("\nline 3\n", chatRequest.suffix)
  }

  @Test
  fun testSelectionSuffix() {
    val mockAPI = MockAugmentAPI()
    application.registerOrReplaceServiceInstance(
      AugmentAPI::class.java,
      mockAPI,
      testRootDisposable,
    )

    val chatService = ChatWebviewMessageClient(project)

    myFixture.configureByText("simple.txt", "line 1\nline 2\n<selection>line 3\n</selection>")

    val chatRequest = runBlocking { chatService.baseChatRequestFromSelection(myFixture.editor) }
    assertEquals("Text", chatRequest.lang)
    assertEquals("simple.txt", chatRequest.path)
    assertEquals("line 1\nline 2\n", chatRequest.prefix)
    assertEquals("line 3\n", chatRequest.selectedCode)
    assertEquals("", chatRequest.suffix)
  }

  @Test
  fun testSelectionLargeFile() {
    val mockAPI = MockAugmentAPI()
    application.registerOrReplaceServiceInstance(
      AugmentAPI::class.java,
      mockAPI,
      testRootDisposable,
    )

    val chatService = ChatWebviewMessageClient(project)

    val lineLength20 = "very important line\n"

    myFixture.configureByText(
      "simple.txt",
      lineLength20.repeat(1_000) + "<caret>" + lineLength20.repeat(1_000),
    )

    val chatRequest = runBlocking { chatService.baseChatRequestFromSelection(myFixture.editor) }
    assertEquals("Text", chatRequest.lang)
    assertEquals(4096, chatRequest.prefix.length)
    assertEquals(4096, chatRequest.suffix.length)
  }

  @Test
  fun testMemories() =
    runBlocking {
      // use mock engine to test that memories are included in chat request.
      // but first use the memories service to save "foo" to the memories file
      // then check that the chat request includes the memories

      // set api token and completion url
      AugmentSettings.instance.apiToken = "test-token"
      AugmentSettings.instance.completionURL = "http://test-server"

      // MockEngine
      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/chat-stream" -> {
              respond(
                content = ByteReadChannel(""),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/record-onboarding-session-event" -> HttpUtil.respondOK(this)
            else ->
              respond(
                content = "Other request",
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
          }
        }
      HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

      augmentHelpers().forcePluginState(PluginState.ENABLED)

      MemoriesService.getInstance(project).updateMemories("foo")

      val chatService = ChatWebviewMessageClient(project)
      runBlocking {
        chatService.chatUserMessage(
          ChatUserMessageRequest.newBuilder()
            .build(),
        ).toList()
      }

      waitForAssertion({
        assert(mockEngine.requestHistory.count { it.url.encodedPath == "/chat-stream" } == 1)
      })
      val request = mockEngine.requestHistory.first { it.url.encodedPath == "/chat-stream" }
      val gson = GsonUtil.createApiGson()
      val chatRequest = gson.fromJson(request.body.toByteArray().decodeToString(), ChatRequest::class.java)
      assert(chatRequest.agent_memories == "foo")
    }
}
