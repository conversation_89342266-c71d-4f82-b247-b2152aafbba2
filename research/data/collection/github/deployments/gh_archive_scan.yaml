apiVersion: apps/v1
kind: Deployment
metadata:
  name: gh-archive-scan
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gh-archive-scan
  template:
    metadata:
      labels:
        app: gh-archive-scan
    spec:
      containers:
      - name: gh-archive-scanner
        image: us-central1-docker.pkg.dev/augment-research-gsc/docker-us-central1/augment-research-cpu-with-spark:ubuntu22.04-py-3.11.7-spark-3.4.3-23
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
        resources:
          requests:
            cpu: 1
            memory: "2Gi"      # Set the requested memory
        command: ["python"]
        args: ["-m", "research.data.collection.github.controllers.process_gh_archive"]
        env:
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: metastore
              key: password
        - name: PGHOST
          value: "metastore.gcp-us1.r.augmentcode.com"
        - name: PGDATABASE
          value: "metastore"
        - name: PGUSER
          value: "augment"
