local config = (import 'deploy/tenants/namespace_configs/dev-defaults.jsonnet');

local githubOauthSecretOverride = {
  name: 'dev-markp-github-processor-oauth-app-secret',
  version: 'latest',
};

config + {
  flags: config.flags
         .override('remoteAgentImageTag', '{namespace}')
         .override('githubOauthSecretOverride', githubOauthSecretOverride)
         .override('extraFakeFeatureFlags', config.flags.extraFakeFeatureFlags + {
    workingset_use_bigcache: false,
  }),
}
