package main

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"os"
	"slices"
	"sort"
	"strings"
	"sync"
	"time"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging/audit"
	authpb "github.com/augmentcode/augment/services/auth/central/server/auth"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	front_end_token_service_pb "github.com/augmentcode/augment/services/auth/central/server/front_end_token_service"
	"github.com/augmentcode/augment/services/integrations/orb"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	riproto "github.com/augmentcode/augment/services/request_insight/proto"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tw_pb "github.com/augmentcode/augment/services/tenant_watcher/proto"
	tenantutil "github.com/augmentcode/augment/services/tenant_watcher/util"
	tokenexchangeproto "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/google/uuid"
	"github.com/prometheus/client_golang/prometheus"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"github.com/stripe/stripe-go/v80"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

var (
	// inActiveSubscriptionStatusTypes is a list of stripe subscription statuses that are considered inactive.
	inactiveSubscriptionStatusTypes = []auth_entities.Subscription_Status{
		auth_entities.Subscription_UNKNOWN,
		auth_entities.Subscription_INCOMPLETE,
		auth_entities.Subscription_INCOMPLETE_EXPIRED,
		auth_entities.Subscription_PAST_DUE,
		auth_entities.Subscription_CANCELED,
		auth_entities.Subscription_UNPAID,
		auth_entities.Subscription_PAUSED,
	}

	// SubscriptionTypeCounter tracks the types of subscriptions returned to users
	SubscriptionTypeCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "au_auth_subscription_type_total",
			Help: "Count of subscription types returned to users",
		},
		[]string{"tenant_tier", "subscription_type", "tenant_name"},
	)

	// Track GetUserBillingInfo calls
	GetUserBillingInfoCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "au_auth_get_user_billing_info_total",
			Help: "Count of GetUserBillingInfo calls",
		},
		[]string{"status"}, // OK or error response
	)
)

func init() {
	prometheus.MustRegister(SubscriptionTypeCounter)
	prometheus.MustRegister(GetUserBillingInfoCounter)
}

type AuthGrpcServer struct {
	daoFactory              *DAOFactory
	tenantMap               *TenantMap
	auditLogger             *audit.AuditLogger
	requestInsightPublisher ripublisher.RequestInsightPublisher
	asyncOpsPublisher       AsyncOpsPublisher
	featureFlagHandle       featureflags.FeatureFlagHandle
	stripeConfig            *StripeConfig
	orbConfig               *OrbConfig
	orbClient               orb.OrbClient
	stripeClient            StripeClient
}

func NewAuthGrpcServer(
	featureFlagHandle featureflags.FeatureFlagHandle,
	daoFactory *DAOFactory,
	tenantMap *TenantMap,
	auditLogger *audit.AuditLogger,
	requestInsightPublisher ripublisher.RequestInsightPublisher,
	asyncOpsPublisher AsyncOpsPublisher,
	stripeConfig *StripeConfig,
	orbConfig *OrbConfig,
	stripeClient StripeClient,
) *AuthGrpcServer {
	if stripeConfig != nil && stripeConfig.Enabled {
		key, err := os.ReadFile(stripeConfig.SecretKeyPath)
		if err != nil {
			log.Error().Err(err).Msg("Failed to read Stripe secret key")
		} else {
			cleanKey := strings.TrimSpace(string(key))
			stripe.Key = cleanKey
			log.Info().Msg("Stripe integration enabled for AuthGrpcServer")
		}
	}
	// Initialize Orb client if enabled
	var orbClient orb.OrbClient
	if orbConfig != nil && orbConfig.Enabled {
		// Read the Orb API key from the specified file path
		key, err := os.ReadFile(orbConfig.ApiKeyPath)
		if err != nil {
			log.Error().Err(err).Msg("Failed to read Orb secret key")
		} else {
			cleanKey := strings.TrimSpace(string(key))
			orbClient = orb.NewOrbClient(cleanKey, featureFlagHandle)
			log.Info().Msg("Orb client initialized successfully")
		}
	}

	server := &AuthGrpcServer{
		daoFactory:              daoFactory,
		tenantMap:               tenantMap,
		auditLogger:             auditLogger,
		requestInsightPublisher: requestInsightPublisher,
		asyncOpsPublisher:       asyncOpsPublisher,
		featureFlagHandle:       featureFlagHandle,
		stripeConfig:            stripeConfig,
		orbConfig:               orbConfig,
		orbClient:               orbClient,
		stripeClient:            stripeClient,
	}

	return server
}

// This method should be used when authenticating service requests (with service tokens) to
// access/modify information about a specific tenant.
//
// Returns an error that can be returned directly to the caller if provided gRPC context does not
// contain auth claims that give access to the provided tenant id and optional scope. Some things to
// note:
//  1. The auth server interceptor is responsible for checking that the token is cryptographically
//     valid, so here we only need to concern ourselves with whether the token's content is correct.
//  2. The service token comes from token-exchnage, which checks peer certificates before granting
//     scopes. As long as a caller has a scope in its claims, we can be sure they are allowed to
//     have that scope.
func authCheck(
	ctx context.Context, tenantID string, requiredScope tokenexchangeproto.Scope,
) error {
	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return status.Error(codes.Unknown, "Invalid context")
	}

	if tenantID == "" {
		if !authClaims.AllowsAllTenants() {
			log.Error().Msgf("Auth claims give permission for tenant %s, but request requires access to all tenants", authClaims.TenantID)
			return status.Error(codes.PermissionDenied, "Access denied")
		}
	} else if !authClaims.IsTenantAllowed(tenantID) {
		log.Error().Msgf("Auth claims give permission for tenant %s, but request has tenant %s", authClaims.TenantID, tenantID)
		return status.Error(codes.PermissionDenied, "Access denied")
	}

	if !authClaims.HasScope(requiredScope) {
		log.Error().Msgf("Auth claims do not give have scope %s", requiredScope)
		return status.Error(codes.PermissionDenied, "Access denied")
	}

	return nil
}

// This method should be used when authenticating user requests (with user tokens)
// to access/modify their own information.
//
// Returns an error that can be returned directly to the caller if provided gRPC context does not
// contain auth claims that give access to the user's own information. Some things to note:
//  1. The auth server interceptor is responsible for checking that the token is cryptographically
//     valid, so here we only need to concern ourselves with whether the token's content is correct.
//  2. This function verifies that the user ID in the auth claims matches the requested user ID,
//     allowing users to access their own information regardless of tenant.
//  3. The required scope is still checked to ensure the caller has appropriate permissions for
//     the requested operation, even when accessing their own information.
func selfAuthCheck(ctx context.Context, userID string, requiredScope tokenexchangeproto.Scope) error {
	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return status.Error(codes.Unknown, "Invalid context")
	}

	if !authClaims.HasScope(requiredScope) {
		log.Error().Msgf("Auth claims do not have scope %s", requiredScope)
		return status.Error(codes.PermissionDenied, "Access denied")
	}

	if authClaims.UserID != userID {
		log.Error().Msgf("Auth claims user ID %s does not match requested user ID %s", authClaims.UserID, userID)
		return status.Error(codes.PermissionDenied, "Access denied: user ID in token does not match requested user")
	}

	return nil
}

// Returns the tenant object for the provided tenant ID, or an error that can be returned directly
// to the caller if something goes wrong.
func (s *AuthGrpcServer) getTenant(tenantID string) (*tw_pb.Tenant, error) {
	tenant, err := s.tenantMap.GetTenantByIdDeletedOk(tenantID)
	if err != nil {
		log.Error().Err(err).Msg("Failed to fetch tenant information")
		return nil, status.Error(codes.Internal, "Internal error")
	}
	if tenant == nil {
		log.Error().Msgf("Tenant ID %s not found", tenantID)
		return nil, status.Error(codes.PermissionDenied, "Access denied")
	}
	return tenant, nil
}

func (s *AuthGrpcServer) AddUserToTenant(ctx context.Context, req *authpb.AddUserToTenantRequest) (*authpb.AddUserToTenantResponse, error) {
	log.Info().Msgf("AddUserToTenant: %s", req.Email)
	err := authCheck(ctx, req.TenantId, tokenexchangeproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	// Check the tenant exists.
	// TODO(jacqueline): With a valid service token I'm not sure this is necessary, but there are
	// tests that require it so I'm leaving it for now.
	tenant, err := s.getTenant(req.TenantId)
	if err != nil {
		return nil, err
	}
	if tenant.DeletedAt != "" {
		return nil, status.Error(codes.PermissionDenied, "Tenant deleted")
	}

	authInfo, _ := auth.GetAugmentClaims(ctx)
	iapEmail, ok := authInfo.GetIapEmail()
	if ok {
		s.auditLogger.WriteAuditLog(
			iapEmail,
			authInfo.OpaqueUserIDType,
			authInfo.TenantName,
			fmt.Sprintf("Add user '%s' to tenant %s", req.Email, authInfo.TenantName),
		)
	}

	if req.Email == "" {
		return nil, status.Error(codes.InvalidArgument, "Invalid request")
	}

	user, err := s.tenantMap.GetUserByEmailAddress(ctx, req.Email)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get user")
		return nil, status.Error(codes.Internal, "Failed to fetch user")
	}
	// Ensure the user is in the tenant
	// Only allow adding the user if they are not currently in any other tenant
	user, err = s.tenantMap.EnsureUserInTenant(ctx, user, req.Email, req.TenantId, "", front_end_token_service_pb.TenantEnsureMode_TENANT_ENSURE_MODE_ADD_IF_EMPTY)
	if err != nil {
		log.Error().Err(err).Msg("Failed to ensure user in tenant")
		return nil, status.Error(codes.Internal, "Failed to ensure user in tenant")
	}

	// Publish Request Insight AddUserToTenant event.
	tenantEvent := ripublisher.NewTenantEvent()
	tenantEvent.Event = &riproto.TenantEvent_AddUserToTenant{
		AddUserToTenant: &riproto.AddUserToTenant{
			User: user,
		},
	}
	riErr := s.requestInsightPublisher.PublishTenantEvent(ctx, &riproto.TenantInfo{
		TenantId:   req.TenantId,
		TenantName: authInfo.TenantName,
	}, tenantEvent)
	if riErr != nil {
		log.Warn().Err(riErr).Msg("Failed to publish AddUserToTenant event")
	}

	return &authpb.AddUserToTenantResponse{
		User: user,
	}, nil
}

func (s *AuthGrpcServer) UpdateUserOnTenant(ctx context.Context, req *authpb.UpdateUserOnTenantRequest) (*authpb.UpdateUserOnTenantResponse, error) {
	err := authCheck(ctx, req.TenantId, tokenexchangeproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	tenant, err := s.getTenant(req.TenantId)
	if err != nil {
		return nil, err
	}
	if tenant.DeletedAt != "" {
		return nil, status.Error(codes.PermissionDenied, "Tenant Deleted")
	}

	authInfo, _ := auth.GetAugmentClaims(ctx)
	iapEmail, ok := authInfo.GetIapEmail()
	if ok {
		s.auditLogger.WriteAuditLog(
			iapEmail,
			authInfo.OpaqueUserIDType,
			authInfo.TenantName,
			fmt.Sprintf("Update user '%s' in tenant %s", req.UserId, authInfo.TenantName),
		)
	}

	tenantMappingDAO := s.daoFactory.GetUserTenantMappingDAO(tenant.Name)
	mapping, err := tenantMappingDAO.GetByUser(ctx, req.UserId)
	if err != nil {
		return nil, status.Error(codes.Internal, "Failed to check existing mapping")
	}

	if mapping == nil {
		return nil, status.Error(codes.NotFound, "Resource not found")
	}
	mapping.CustomerUiRoles = req.CustomerUiRoles
	_, err = tenantMappingDAO.Update(ctx, mapping)
	if err != nil {
		return nil, status.Error(codes.Internal, "Failed to update mapping")
	}

	return &authpb.UpdateUserOnTenantResponse{
		CustomerUiRoles: mapping.CustomerUiRoles,
	}, nil
}

func (s *AuthGrpcServer) GetUserOnTenant(ctx context.Context, req *authpb.GetUserOnTenantRequest) (*authpb.GetUserOnTenantResponse, error) {
	err := authCheck(ctx, req.TenantId, tokenexchangeproto.Scope_AUTH_R)
	if err != nil {
		return nil, err
	}

	tenant, err := s.getTenant(req.TenantId)
	if err != nil {
		return nil, err
	}

	authInfo, _ := auth.GetAugmentClaims(ctx)
	iapEmail, ok := authInfo.GetIapEmail()
	if ok {
		s.auditLogger.WriteAuditLog(
			iapEmail,
			authInfo.OpaqueUserIDType,
			authInfo.TenantName,
			fmt.Sprintf("Get user/tenant info for user '%s' from tenant %s", req.UserId, authInfo.TenantName),
		)
	}

	tenantMappingDAO := s.daoFactory.GetUserTenantMappingDAO(tenant.Name)
	mapping, err := tenantMappingDAO.GetByUser(ctx, req.UserId)
	if err != nil {
		return nil, status.Error(codes.Internal, "Failed to check existing mapping")
	}

	if mapping == nil {
		return nil, status.Error(codes.NotFound, "Resource not found")
	}

	return &authpb.GetUserOnTenantResponse{
		CustomerUiRoles: mapping.CustomerUiRoles,
	}, nil
}

func (s *AuthGrpcServer) deleteUserTokensForTenant(ctx context.Context, userId string, tenantID string) (int, error) {
	tokenHashDAO := s.daoFactory.GetTokenHashDAO()

	hashes := make([]string, 0, 16)
	err := tokenHashDAO.FindAll(ctx, func(token *auth_entities.TokenHash) bool {
		if token.AugmentUserId == userId && token.TenantId == tenantID {
			hashes = append(hashes, token.Hash)
		}
		return true
	})
	if err != nil {
		return 0, status.Error(codes.Internal, "Failed to fetch tokens")
	}

	deleted := 0
	for _, hash := range hashes {
		if err := tokenHashDAO.Delete(ctx, hash); err != nil {
			return deleted, status.Error(codes.Internal, "Failed to delete token")
		}
		deleted++
	}
	return deleted, nil
}

func (s *AuthGrpcServer) RemoveUserFromTenant(ctx context.Context, req *authpb.RemoveUserFromTenantRequest) (*authpb.RemoveUserFromTenantResponse, error) {
	err := authCheck(ctx, req.TenantId, tokenexchangeproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}
	return s.removeUserFromTenant(ctx, req, nil)
}

// helper function to remove user from tenant - this should be used AFTER any auth checks
// tenantIdToNameMappings is an optional map of tenant IDs to names that can be used to find tenants that have been deleted
func (s *AuthGrpcServer) removeUserFromTenant(ctx context.Context, req *authpb.RemoveUserFromTenantRequest, tenantIdToNameMappings map[string]string) (*authpb.RemoveUserFromTenantResponse, error) {
	tenantId := req.TenantId
	tenant, err := s.tenantMap.GetTenantByIdDeletedOk(tenantId)
	if err != nil {
		return nil, err
	}

	var tenantName string
	if tenant != nil {
		tenantName = tenant.Name
	} else if tenantIdToNameMappings != nil && tenantIdToNameMappings[tenantId] != "" {
		tenantName = tenantIdToNameMappings[tenantId]
	} else {
		log.Error().Msgf("Tenant ID %s not found in tenant table nor in tenantIdToNameMappings", tenantId)
		return nil, status.Error(codes.NotFound, "Tenant not found")
	}

	authInfo, _ := auth.GetAugmentClaims(ctx)
	iapEmail, ok := authInfo.GetIapEmail()
	if ok {
		s.auditLogger.WriteAuditLog(
			iapEmail,
			authInfo.OpaqueUserIDType,
			tenantName,
			fmt.Sprintf("[Tenant Change] Remove user '%s' from tenant %s", req.UserId, tenantName),
		)
	}

	userDAO := s.daoFactory.GetUserDAO()

	removeTenantIDFromUser := func(user *auth_entities.User) bool {
		idx := slices.Index(user.Tenants, tenantId)
		if idx == -1 {
			return false
		}
		user.Tenants = slices.Delete(user.Tenants, idx, idx+1)
		return true
	}

	user, err := userDAO.TryUpdate(ctx, req.UserId, removeTenantIDFromUser, DefaultRetry)
	if err != nil {
		return nil, status.Error(codes.Internal, "Failed to fetch user")
	}

	if user == nil {
		return nil, status.Error(codes.NotFound, "Resource not found")
	}

	// don't delete the user as they may belong to multiple tenants.
	tenantMappingDAO := s.daoFactory.GetUserTenantMappingDAO(tenantName)
	mapping_to_delete, err := tenantMappingDAO.GetByUser(ctx, req.UserId)
	if err != nil {
		return nil, status.Error(codes.Internal, "Failed to check existing mapping")
	}

	if mapping_to_delete == nil {
		return nil, status.Error(codes.NotFound, "Resource not found")
	}

	err = tenantMappingDAO.Delete(ctx, req.UserId)
	if err != nil {
		return nil, status.Error(codes.Internal, "Failed to delete mapping")
	}

	// Also remove any tokens associated with this tenant and user.
	_, err = s.deleteUserTokensForTenant(ctx, user.Id, tenantId)
	if err != nil {
		return nil, err
	}

	// Revoke any user cookies used for authentication, since we are not removing the user
	userDAO.UpdateNonce(ctx, user.Id)

	// Publish Request Insight RemoveUserFromTenant event.
	tenantEvent := ripublisher.NewTenantEvent()
	tenantEvent.Event = &riproto.TenantEvent_RemoveUserFromTenant{
		RemoveUserFromTenant: &riproto.RemoveUserFromTenant{
			User: user,
		},
	}
	riErr := s.requestInsightPublisher.PublishTenantEvent(ctx, &riproto.TenantInfo{
		TenantId:   tenantId,
		TenantName: tenantName,
	}, tenantEvent)
	if riErr != nil {
		log.Warn().Err(riErr).Msg("Failed to publish RemoveUserFromTenant event")
	}

	return &authpb.RemoveUserFromTenantResponse{}, nil
}

func (s *AuthGrpcServer) UpdateUserEmail(ctx context.Context, req *authpb.UpdateUserEmailRequest) (*authpb.UpdateUserEmailResponse, error) {
	err := authCheck(ctx, "", tokenexchangeproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	// Log the operation
	authClaims, _ := auth.GetAugmentClaims(ctx)
	iapEmail, ok := authClaims.GetIapEmail()
	if ok {
		s.auditLogger.WriteAuditLog(
			iapEmail,
			authClaims.OpaqueUserIDType,
			authClaims.TenantName,
			fmt.Sprintf("Update user '%s' email from %s to %s", req.UserId, req.CurrentEmail, req.NewEmail),
		)
	}

	userDAO := s.daoFactory.GetUserDAO()

	// Update the user email
	updateErr := error(nil)
	user, err := userDAO.TryUpdate(ctx, req.UserId, func(u *auth_entities.User) bool {
		if u.Email != req.CurrentEmail {
			updateErr = status.Error(codes.InvalidArgument, "Current email doesn't match")
			return false
		}

		u.Email = req.NewEmail
		return true
	}, DefaultRetry)
	if user == nil {
		return nil, status.Error(codes.NotFound, "User not found")
	}
	if err != nil {
		return nil, status.Error(codes.Internal, "Failed to update user email")
	}
	if updateErr != nil {
		return nil, updateErr
	}

	// Delete all token hashes for this user
	totalTokensDeleted := 0
	for _, tenantID := range user.Tenants {
		tokensDeleted, err := s.deleteUserTokensForTenant(ctx, req.UserId, tenantID)
		if err != nil {
			log.Error().Err(err).Str("tenant_id", tenantID).Msg("Failed to delete tokens for tenant")
		} else {
			totalTokensDeleted += tokensDeleted
		}
	}

	log.Info().Int("count", totalTokensDeleted).Str("user_id", req.UserId).Msg("Deleted tokens after email update")

	// Revoke any user cookies used for authentication
	userDAO.UpdateNonce(ctx, user.Id)

	return &authpb.UpdateUserEmailResponse{}, nil
}

func (s *AuthGrpcServer) ListTenantUsers(ctx context.Context, req *authpb.ListTenantUsersRequest) (*authpb.ListTenantUsersResponse, error) {
	err := authCheck(ctx, req.TenantId, tokenexchangeproto.Scope_AUTH_R)
	if err != nil {
		return nil, err
	}

	tenant, err := s.getTenant(req.TenantId)
	if err != nil {
		return nil, err
	}

	authInfo, _ := auth.GetAugmentClaims(ctx)
	iapEmail, ok := authInfo.GetIapEmail()
	if ok {
		s.auditLogger.WriteAuditLog(
			iapEmail,
			authInfo.OpaqueUserIDType,
			authInfo.TenantName,
			fmt.Sprintf("List users for tenant %s", authInfo.TenantName),
		)
	}

	tenantMappingDAO := s.daoFactory.GetUserTenantMappingDAO(tenant.Name)
	userDAO := s.daoFactory.GetUserDAO()

	userIDs := make([]string, 0, 1024)
	err = tenantMappingDAO.FindAll(ctx, func(mapping *auth_entities.UserTenantMapping) bool {
		if mapping.Tenant == tenant.Name {
			userIDs = append(userIDs, mapping.UserId)
		}
		return true
	})
	if err != nil {
		return nil, status.Error(codes.Internal, "Failed to fetch mappings")
	}

	slices.Sort(userIDs)

	users := make([]*auth_entities.User, 0, len(userIDs))
	idx := 0

	err = userDAO.BatchGet(ctx, userIDs, 1000, func(user *auth_entities.User) bool {
		idxMatch := idx
		for userIDs[idxMatch] < user.Id {
			if idxMatch != idx {
				log.Error().Msgf("User not found: %s", userIDs[idxMatch])
			}
			idxMatch++
		}
		if userIDs[idxMatch] != user.Id {
			log.Error().Msgf("Unexpected user ID returned: %s != %s", userIDs[idxMatch], user.Id)
			return false
		}
		idx = idxMatch

		users = append(users, user)
		return true
	})
	if err != nil {
		log.Error().Err(err).Msgf("Failed to fetch users")
	}

	return &authpb.ListTenantUsersResponse{
		Users: users,
	}, nil
}

func (s *AuthGrpcServer) GetUser(ctx context.Context, req *authpb.GetUserRequest) (*authpb.GetUserResponse, error) {
	// Get auth claims first
	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unknown, "Invalid context")
	}

	// Auth checks
	if req.TenantId != nil {
		// Tenant-based authorization
		if err := authCheck(ctx, *req.TenantId, tokenexchangeproto.Scope_AUTH_R); err != nil {
			return nil, err
		}
	} else {
		// Self-access authorization
		if err := selfAuthCheck(ctx, req.UserId, tokenexchangeproto.Scope_AUTH_R); err != nil {
			return nil, err
		}
	}

	// Get user information after auth checks
	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, req.UserId)
	if err != nil {
		return nil, status.Error(codes.Internal, "Failed to fetch user")
	}
	if user == nil {
		return nil, status.Error(codes.NotFound, "Resource not found")
	}

	// Verify user belongs to the tenant if tenant-based access
	if req.TenantId != nil && !slices.Contains(user.Tenants, *req.TenantId) {
		return nil, status.Error(codes.NotFound, "Resource not found")
	}

	// Log the access
	if iapEmail, emailOk := authInfo.GetIapEmail(); emailOk {
		logMsg := fmt.Sprintf("User '%s' accessed", req.UserId)
		if req.TenantId != nil {
			logMsg += fmt.Sprintf(" from tenant %s", authInfo.TenantName)
		} else {
			logMsg += " their own information"
		}
		s.auditLogger.WriteAuditLog(iapEmail, authInfo.OpaqueUserIDType, authInfo.TenantName, logMsg)
	}

	return &authpb.GetUserResponse{User: user}, nil
}

// Get the information of a user related to billing.
func (s *AuthGrpcServer) GetUserBillingInfo(ctx context.Context, req *authpb.GetUserBillingInfoRequest) (*authpb.GetUserBillingInfoResponse, error) {
	// Auth check. Only allow tenant based auth since this is only used by the billing service now.
	if err := authCheck(ctx, req.TenantId, tokenexchangeproto.Scope_AUTH_R); err != nil {
		return nil, err
	}

	// Use the helper function to get the user billing info
	billingInfo, err := GetUserBillingInfo(ctx, req.UserId, req.TenantId, s.daoFactory, s.tenantMap)
	if err != nil {
		return nil, err
	}

	// Convert to proto response
	return &authpb.GetUserBillingInfoResponse{
		UserId:          billingInfo.UserID,
		Email:           billingInfo.Email,
		IsSelfServeTeam: billingInfo.IsSelfServeTeam,
		OrbCustomerId:   billingInfo.OrbCustomerID,
	}, nil
}

func matchUser(user *auth_entities.User, searchString string) bool {
	return strings.Contains(strings.ToLower(user.Email), searchString) ||
		strings.Contains(strings.ToLower(user.Id), searchString) ||
		strings.Contains(strings.ToLower(user.OrbCustomerId), searchString) ||
		strings.Contains(strings.ToLower(user.OrbSubscriptionId), searchString) ||
		strings.Contains(strings.ToLower(user.StripeCustomerId), searchString) ||
		slices.ContainsFunc(user.Tenants, func(tenant string) bool {
			return strings.Contains(strings.ToLower(tenant), searchString)
		}) ||
		slices.ContainsFunc(user.IdpUserIds, func(idpUserId string) bool {
			return strings.Contains(strings.ToLower(idpUserId), searchString)
		})
}

func (s *AuthGrpcServer) GetUsers(ctx context.Context, req *authpb.GetUsersRequest) (*authpb.GetUsersResponse, error) {
	if err := authCheck(ctx, "", tokenexchangeproto.Scope_AUTH_R); err != nil {
		return nil, err
	}

	// Get the user DAO
	userDAO := s.daoFactory.GetUserDAO()

	// Validation
	if len(req.PageToken) > 64 {
		return nil, status.Error(codes.InvalidArgument, "Page token too long")
	}

	pageSize := max(req.PageSize, 1000)
	if pageSize <= 0 {
		pageSize = 1000
	}

	if len(req.SearchString) > 128 {
		return nil, status.Error(codes.InvalidArgument, "Search string too long")
	}

	searchString := strings.ToLower(req.SearchString)

	// Get the users
	users := make([]*auth_entities.User, 0, pageSize)
	nextPageToken := ""

	err := userDAO.FindAllWithStartKey(ctx, req.PageToken, func(user *auth_entities.User) bool {
		// only return users with an email that matches the search string if provided
		// if no search string is specified, return all users
		if searchString != "" && !matchUser(user, searchString) {
			return true
		}

		users = append(users, user)
		if len(users) >= int(pageSize) {
			nextPageToken = user.Id + string(0xFF)
			return false
		}

		return true
	})
	if err != nil {
		return nil, err
	}

	// Return the users
	return &authpb.GetUsersResponse{
		Users:         users,
		NextPageToken: nextPageToken,
	}, nil
}

// Update the information of a user related to billing.
func (s *AuthGrpcServer) UpdateUserBillingInfo(ctx context.Context, req *authpb.UpdateUserBillingInfoRequest) (*authpb.UpdateUserBillingInfoResponse, error) {
	// Auth check. Only allow tenant based auth since this is only used by the billing service now.
	if err := authCheck(ctx, req.TenantId, tokenexchangeproto.Scope_AUTH_RW); err != nil {
		return nil, err
	}

	// Verify the user exists
	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, req.UserId)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, status.Error(codes.NotFound, "Resource not found")
	}

	// Update the user
	updateUser := func(u *auth_entities.User) bool {
		update := false
		if req.BillingMethod != nil && u.BillingMethod != *req.BillingMethod {
			u.BillingMethod = *req.BillingMethod
			update = true
		}
		if req.OrbCustomerId != nil && u.OrbCustomerId != *req.OrbCustomerId {
			u.OrbCustomerId = *req.OrbCustomerId
			update = true
		}
		if req.OrbSubscriptionId != nil && u.OrbSubscriptionId != *req.OrbSubscriptionId {
			u.OrbSubscriptionId = *req.OrbSubscriptionId
			update = true
		}
		if req.StripeCustomerId != nil && u.StripeCustomerId != *req.StripeCustomerId {
			u.StripeCustomerId = *req.StripeCustomerId
			update = true
		}
		return update
	}
	_, err = userDAO.TryUpdate(ctx, user.Id, updateUser, DefaultRetry)
	if err != nil {
		return nil, err
	}

	return &authpb.UpdateUserBillingInfoResponse{}, nil
}

func (s *AuthGrpcServer) GetTokenInfo(ctx context.Context, req *authpb.GetTokenInfoRequest) (*authpb.GetTokenInfoResponse, error) {
	tokenHashDAO := s.daoFactory.GetTokenHashDAO()
	tokenInfo, err := tokenHashDAO.Get(ctx, tokenHash(req.Token))
	if err != nil {
		log.Error().Err(err).Msg("Failed to get token info")
		return nil, status.Error(codes.Internal, "Failed to fetch token info")
	}
	if tokenInfo == nil {
		log.Warn().Msgf("Token not found for hash %s", tokenHash(req.Token))
		return nil, status.Error(codes.NotFound, "Resource not found")
	}

	tenant, err := s.tenantMap.GetTenantByIdDeletedOk(tokenInfo.TenantId)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get tenant")
		return nil, status.Error(codes.Internal, "Internal error")
	}
	if tenant == nil {
		log.Error().Msgf("Tenant ID %s not found", tokenInfo.TenantId)
		return nil, status.Error(codes.Internal, "Internal")
	}
	if tenant.DeletedAt != "" {
		log.Error().Msgf("Tenant %s is deleted", tenant.Name)
		return nil, status.Error(codes.NotFound, "Tenant deleted")
	}

	if tokenInfo.ExpirationTimeSeconds != 0 && time.Now().Sub(tokenInfo.CreationTime.AsTime()) >= time.Duration(tokenInfo.ExpirationTimeSeconds)*time.Second {
		log.Info().Msgf("Token expired for hash %s", tokenHash(req.Token))
		return nil, status.Error(codes.NotFound, "Token expired")
	}

	// Get user
	userID := tokenInfo.AugmentUserId
	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, userID)
	if err != nil {
		log.Error().Err(err).Str("user_id", userID).Msg("GetTokenInfo failed: error fetching user")
		return nil, status.Error(codes.Internal, "Failed to fetch user")
	}
	if user == nil {
		log.Error().Str("user_id", userID).Msg("GetTokenInfo failed: user not found")
		return nil, status.Error(codes.NotFound, "User not found")
	}

	response := &authpb.GetTokenInfoResponse{
		UserId:        tokenInfo.EmailAddress,
		AugmentUserId: tokenInfo.AugmentUserId,
		UserEmail:     tokenInfo.EmailAddress,
		TenantId:      tokenInfo.TenantId,
		TenantName:    tenant.Name,
		Suspensions:   user.Suspensions,
	}

	// Get subscription type
	subscriptionType, err := s.getSubscriptionType(ctx, tenant, user, tokenInfo)
	if err != nil {
		return nil, err
	}

	// Set the subscription field based on type
	switch s := subscriptionType.(type) {
	case EnterpriseSubscription:
		response.Subscription = s.GetTokenInfoResponse_Enterprise
	case ActiveSubscription:
		response.Subscription = s.GetTokenInfoResponse_ActiveSubscription
	case TrialSubscription:
		response.Subscription = s.GetTokenInfoResponse_Trial
	case InactiveSubscription:
		response.Subscription = s.GetTokenInfoResponse_InactiveSubscription
	}

	return response, nil
}

// Custom interface for token info response subscription type
type SubscriptionType interface {
	isSubscriptionType()
}

type EnterpriseSubscription struct {
	*authpb.GetTokenInfoResponse_Enterprise
}

type ActiveSubscription struct {
	*authpb.GetTokenInfoResponse_ActiveSubscription
}

type TrialSubscription struct {
	*authpb.GetTokenInfoResponse_Trial
}

type InactiveSubscription struct {
	*authpb.GetTokenInfoResponse_InactiveSubscription
}

// Implement the interface
func (EnterpriseSubscription) isSubscriptionType() {}
func (ActiveSubscription) isSubscriptionType()     {}
func (TrialSubscription) isSubscriptionType()      {}
func (InactiveSubscription) isSubscriptionType()   {}

// getSubscriptionType determines the subscription type based on tenant tier and user subscription
// Works with both Stripe and Orb billing methods
func (s *AuthGrpcServer) getSubscriptionType(
	ctx context.Context,
	tenant *tw_pb.Tenant,
	user *auth_entities.User,
	tokenInfo *auth_entities.TokenHash,
) (SubscriptionType, error) {
	var subscription SubscriptionType

	// if the user is in multiple tenants, let's return an active subscription since they may be currently switching tenants
	if len(user.Tenants) > 1 {
		log.Info().Str("user_id", tokenInfo.AugmentUserId).Int("tenant_count", len(user.Tenants)).Msg("User is in multiple tenants, returning active subscription")
		subscription = ActiveSubscription{
			&authpb.GetTokenInfoResponse_ActiveSubscription{
				ActiveSubscription: &authpb.ActiveSubscription{
					BillingMethod: user.BillingMethod,
				},
			},
		}
		SubscriptionTypeCounter.WithLabelValues(tenant.Tier.String(), "ACTIVE_MULTI_TENANT", tenantwatcherclient.MetricsTenantName(tenant)).Inc()
		return subscription, nil
	}

	// Set subscription based on tenant tier and user subscription
	if tenant.Tier == tw_pb.TenantTier_TENANT_TIER_UNKNOWN {
		log.Error().Msgf("Tenant %s has unknown tier", tenant.Name)
		return nil, status.Error(codes.Internal, "Internal error")
	} else if tenant.Tier == tw_pb.TenantTier_ENTERPRISE {
		// Enterprise tier
		subscription = EnterpriseSubscription{
			&authpb.GetTokenInfoResponse_Enterprise{
				Enterprise: &authpb.EnterpriseSubscription{},
			},
		}
		SubscriptionTypeCounter.WithLabelValues(tenant.Tier.String(), "ENTERPRISE", tenantwatcherclient.MetricsTenantName(tenant)).Inc()
	} else {
		if tenantutil.IsLegacySelfServeTeamTenant(tenant) {
			// currently, we assume that all legacy self-serve teams have an active subscription
			subscription = ActiveSubscription{
				&authpb.GetTokenInfoResponse_ActiveSubscription{
					ActiveSubscription: &authpb.ActiveSubscription{
						BillingMethod: user.BillingMethod,
					},
				},
			}
			SubscriptionTypeCounter.WithLabelValues(tenant.Tier.String(), "ACTIVE_TEAM", tenantwatcherclient.MetricsTenantName(tenant)).Inc()
			return subscription, nil
		}

		// Check to see if subscriptionCreationInfo is pending
		if user.SubscriptionCreationInfo != nil && user.SubscriptionCreationInfo.Status == auth_entities.User_SubscriptionCreationInfo_PENDING {
			subscription = ActiveSubscription{
				&authpb.GetTokenInfoResponse_ActiveSubscription{
					ActiveSubscription: &authpb.ActiveSubscription{
						BillingMethod: user.BillingMethod,
					},
				},
			}
			SubscriptionTypeCounter.WithLabelValues(tenant.Tier.String(), "ACTIVE_SUBSCRIPTION_CREATION_PENDING", tenantwatcherclient.MetricsTenantName(tenant)).Inc()
			return subscription, nil
		}

		// Get user billing info - this will handle getting the appropriate subscription id for the team OR user
		userBillingInfo, err := GetUserBillingInfo(ctx, user.Id, tenant.Id, s.daoFactory, s.tenantMap)
		if err != nil {
			log.Error().Err(err).Msg("Failed to get user billing info")
			return nil, status.Error(codes.Internal, "Failed to get user billing info")
		}

		if userBillingInfo.BillingMethod == auth_entities.BillingMethod_BILLING_METHOD_ORB || userBillingInfo.BillingMethod == auth_entities.BillingMethod_BILLING_METHOD_UNKNOWN {
			return s.getOrbSubscriptionType(ctx, tenant, &userBillingInfo.OrbSubscriptionID, tokenInfo.AugmentUserId)
		} else {
			log.Warn().Str("user_id", user.Id).Str("billing_method", user.BillingMethod.String()).Msg("User's billing method may be in migration - treating as active user.")
			subscription = ActiveSubscription{
				&authpb.GetTokenInfoResponse_ActiveSubscription{
					ActiveSubscription: &authpb.ActiveSubscription{
						BillingMethod: user.BillingMethod,
					},
				},
			}
			SubscriptionTypeCounter.WithLabelValues(tenant.Tier.String(), "ACTIVE_MIGRATION", tenantwatcherclient.MetricsTenantName(tenant)).Inc()
		}

	}
	return subscription, nil
}

func (s *AuthGrpcServer) getOrbSubscriptionType(
	ctx context.Context,
	tenant *tw_pb.Tenant,
	orbSubscriptionId *string,
	augmentUserId string,
) (SubscriptionType, error) {
	var subscriptionType SubscriptionType

	if orbSubscriptionId == nil || *orbSubscriptionId == "" {
		log.Warn().Msgf("No Orb subscription ID found for user %s", augmentUserId)
		// return an inactive subscription
		subscriptionType = InactiveSubscription{
			&authpb.GetTokenInfoResponse_InactiveSubscription{
				InactiveSubscription: &authpb.InactiveSubscription{
					BillingMethod: auth_entities.BillingMethod_BILLING_METHOD_ORB,
				},
			},
		}
		SubscriptionTypeCounter.WithLabelValues(tenant.Tier.String(), "INACTIVE_NO_ORB_SUBSCRIPTION", tenantwatcherclient.MetricsTenantName(tenant)).Inc()
		return subscriptionType, nil
	}

	// Get Orb subscription
	subscription, err := s.getUserSubscription(ctx, orbSubscriptionId)
	if err != nil {
		log.Error().Err(err).Str("orb_subscription_id", *orbSubscriptionId).Msg("Failed to get user subscription")
		return nil, status.Error(codes.Internal, "Failed to fetch user subscription")
	}

	// Check if subscription is nil
	// If subscription ID is present, but subscription entry does not yet exist, this means
	// we are in the interval of time between the creation of the subscription and the webhook
	// updating the subscription table, or the webhook processor isn't turned on.
	// In either case, this should only happen for a newly created user; treating as active.
	if subscription == nil {
		log.Warn().Str("orb_subscription_id", *orbSubscriptionId).Str("user_id", augmentUserId).Msg("No subscription found for Orb user, treating as active")
		// Create a timestamp for 14 days in the future
		futureTime := time.Now().Add(14 * 24 * time.Hour)
		subscriptionType = ActiveSubscription{
			&authpb.GetTokenInfoResponse_ActiveSubscription{
				ActiveSubscription: &authpb.ActiveSubscription{
					EndDate:              timestamppb.New(futureTime), // it's a new user so most likely 14 days
					UsageBalanceDepleted: false,
					BillingMethod:        auth_entities.BillingMethod_BILLING_METHOD_ORB,
				},
			},
		}
		return subscriptionType, nil
	}

	// Check subscription status
	if subscription.OrbStatus == auth_entities.Subscription_ORB_STATUS_ACTIVE {

		// Active subscription
		subscriptionType = ActiveSubscription{
			&authpb.GetTokenInfoResponse_ActiveSubscription{
				ActiveSubscription: &authpb.ActiveSubscription{
					EndDate:              subscription.EndDate,
					UsageBalanceDepleted: subscription.UsageBalanceDepleted,
					BillingMethod:        subscription.BillingMethod,
				},
			},
		}
		SubscriptionTypeCounter.WithLabelValues(tenant.Tier.String(), "ORB_ACTIVE", tenantwatcherclient.MetricsTenantName(tenant)).Inc()

	} else if subscription.OrbStatus == auth_entities.Subscription_ORB_STATUS_ENDED {
		subscriptionType = InactiveSubscription{
			&authpb.GetTokenInfoResponse_InactiveSubscription{
				InactiveSubscription: &authpb.InactiveSubscription{
					BillingMethod: subscription.BillingMethod,
				},
			},
		}
		SubscriptionTypeCounter.WithLabelValues(tenant.Tier.String(), "ORB_INACTIVE", tenantwatcherclient.MetricsTenantName(tenant)).Inc()
	} else {
		// We do not expect orb subscription status to be anything other than ACTIVE or ENDED.
		log.Error().Msgf("Unknown Orb subscription status: %v", subscription.OrbStatus)
		SubscriptionTypeCounter.WithLabelValues(tenant.Tier.String(), "ORB_UNKNOWN", tenantwatcherclient.MetricsTenantName(tenant)).Inc()
		return nil, status.Error(codes.Internal, "Internal error")
	}
	return subscriptionType, nil
}

// getUserSubscription returns the user subscription
func (s *AuthGrpcServer) getUserSubscription(ctx context.Context, subscriptionID *string) (*auth_entities.Subscription, error) {
	// Get subscription

	if subscriptionID == nil || *subscriptionID == "" {
		return nil, nil
	}
	subscriptionDAO := s.daoFactory.GetSubscriptionDAO()
	subscription, err := subscriptionDAO.Get(ctx, *subscriptionID)
	if err != nil {
		log.Error().Err(err).Str("subscription_id", *subscriptionID).Msg("GetUserSubscription failed: error fetching subscription")
		return nil, status.Error(codes.Internal, "Failed to fetch subscription")
	}

	return subscription, nil
}

// tokenHash generates a SHA256 hash of the token
func tokenHash(token string) string {
	hash := sha256.Sum256([]byte(token))
	return hex.EncodeToString(hash[:])
}

func (s *AuthGrpcServer) RevokeUserCookies(ctx context.Context, request *authpb.RevokeUserCookiesRequest) (*authpb.RevokeUserCookiesResponse, error) {
	err := authCheck(ctx, request.TenantId, tokenexchangeproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	tenant, err := s.getTenant(request.TenantId)
	if err != nil {
		return nil, status.Error(codes.Internal, "Failed to fetch tenant")
	}

	authInfo, _ := auth.GetAugmentClaims(ctx)
	iapEmail, ok := authInfo.GetIapEmail()
	if ok {
		s.auditLogger.WriteAuditLog(
			iapEmail,
			authInfo.OpaqueUserIDType,
			authInfo.TenantName,
			fmt.Sprintf("Revoke user cookie from tenant %s", authInfo.TenantName),
		)
	}

	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, request.UserId)
	if err != nil {
		return nil, status.Error(codes.Internal, "Failed to fetch user")
	}

	if user == nil {
		return nil, status.Error(codes.NotFound, "Resource not found")
	}

	tenantMappingDAO := s.daoFactory.GetUserTenantMappingDAO(tenant.Name)
	mapping, err := tenantMappingDAO.GetByUser(ctx, request.UserId)
	if err != nil {
		return nil, status.Error(codes.Internal, "Failed to check existing mapping")
	}

	if mapping == nil {
		return nil, status.Error(codes.NotFound, "Resource not found")
	}

	// Force authentication on next request.
	userDAO.UpdateNonce(ctx, user.Id)

	return &authpb.RevokeUserCookiesResponse{}, nil
}

func (s *AuthGrpcServer) RevokeUser(ctx context.Context, request *authpb.RevokeUserRequest) (*authpb.RevokeUserResponse, error) {
	err := authCheck(ctx, request.TenantId, tokenexchangeproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	tenant, err := s.getTenant(request.TenantId)
	if err != nil {
		return nil, status.Error(codes.Internal, "Failed to fetch tenant")
	}

	authInfo, _ := auth.GetAugmentClaims(ctx)
	iapEmail, ok := authInfo.GetIapEmail()
	if ok {
		s.auditLogger.WriteAuditLog(
			iapEmail,
			authInfo.OpaqueUserIDType,
			authInfo.TenantName,
			fmt.Sprintf("Revoke user '%s' from tenant %s", request.Email, authInfo.TenantName),
		)
	}

	user, err := s.tenantMap.GetUserByEmailAddress(ctx, request.Email)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, status.Error(codes.NotFound, "Resource not found")
	}

	// Remove any tokens associated with this tenant and user.
	tokensDeleted, err := s.deleteUserTokensForTenant(ctx, user.Id, tenant.Id)
	if err != nil {
		return nil, err
	}

	// Revoke any user cookies used for authentication, since we are not removing the user
	userDAO := s.daoFactory.GetUserDAO()
	userDAO.UpdateNonce(ctx, user.Id)

	return &authpb.RevokeUserResponse{TokensDeleted: int32(tokensDeleted)}, nil
}

func (s *AuthGrpcServer) CreateUserSuspension(ctx context.Context, req *authpb.CreateUserSuspensionRequest) (*authpb.CreateUserSuspensionResponse, error) {
	// Add suspension to the user
	log.Info().Str("user_id", req.UserId).Msg("CreateUserSuspension request received")

	err := authCheck(ctx, req.TenantId, tokenexchangeproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	authInfo, _ := auth.GetAugmentClaims(ctx)
	iapEmail, ok := authInfo.GetIapEmail()
	suspensionId := uuid.New().String()

	if ok {
		s.auditLogger.WriteAuditLog(
			iapEmail,
			authInfo.OpaqueUserIDType,
			authInfo.TenantName,
			fmt.Sprintf("Add suspension '%s' to user '%s' from tenant %s with evidence '%s'",
				suspensionId, req.UserId, authInfo.TenantName, req.Evidence),
		)
	}

	userDAO := s.daoFactory.GetUserDAO()

	user, err := userDAO.Get(ctx, req.UserId)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, status.Error(codes.NotFound, "Resource not found")
	}

	// Do not suspend enterprise or teams users.
	for _, tenantID := range user.Tenants {
		tenant, err := s.getTenant(tenantID)
		if err != nil {
			return nil, err
		}
		if tenantutil.IsEnterpriseTenant(tenant) || tenantutil.IsSelfServeTeamTenant(tenant) {
			return nil, status.Error(codes.InvalidArgument, "Can only suspend professional or community user")
		}
	}

	if user.SuspensionExempt {
		return nil, status.Error(codes.InvalidArgument, "User is exempt from suspension")
	}

	// Validate suspension type against user and account
	logoutUser := false
	switch req.SuspensionType {
	case auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_API_ABUSE:
		// Any self-serve user can be blocked for API abuse.
		logoutUser = true
	case auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_COMMUNITY_ABUSE:
		// Only community users can be suspended for community abuse.
		tenant, err := s.getTenant(req.TenantId)
		if err != nil {
			return nil, err
		}
		if !tenantutil.IsCommunityTenant(tenant) {
			return nil, status.Error(codes.InvalidArgument, "Can only suspend community user")
		}

	case auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE:

		// Only free trial users on Orb can be suspended for free trial abuse.
		subscriptionDAO := s.daoFactory.GetSubscriptionDAO()

		// Only Orb billing method is allowed for free trial abuse suspension
		if user.BillingMethod != auth_entities.BillingMethod_BILLING_METHOD_ORB {
			log.Error().Str("billing_method", user.BillingMethod.String()).Str("user_id", user.Id).Msg("User not on Orb billing method")
			return nil, status.Error(codes.InvalidArgument, "User not on Orb billing method")
		}

		if user.OrbSubscriptionId == "" {
			log.Error().Str("billing_method", user.BillingMethod.String()).Str("user_id", user.Id).Msg("User does not have an Orb subscription")
			return nil, status.Error(codes.InvalidArgument, "Orb user does not have a subscription")
		}

		// Get the Orb subscription and check if it has a payment method
		subscription, err := subscriptionDAO.Get(ctx, user.OrbSubscriptionId)
		if err != nil {
			return nil, err
		}
		if subscription == nil {
			log.Error().Str("subscription_id", user.OrbSubscriptionId).Str("user_id", user.Id).Msg("Subscription not found")
			return nil, status.Error(codes.NotFound, fmt.Sprintf("Subscription not found: subscription_id=%s, user_id=%s", user.OrbSubscriptionId, user.Id))
		}

		// Subscription must be on the trial plan
		if subscription.ExternalPlanId != s.orbConfig.getTrialPlan().ID {
			return nil, status.Error(codes.InvalidArgument, "Subscription is not on the trial plan")
		}

		// Check if the user has a payment method configured
		if subscription.HasPaymentMethod {
			return nil, status.Error(codes.InvalidArgument, "User has a payment method configured")
		}

	default:
		return nil, status.Error(codes.InvalidArgument, "Invalid suspension type")
	}

	// Add suspension to the user
	suspension := &auth_entities.UserSuspension{
		SuspensionId:   suspensionId,
		CreatedTime:    timestamppb.Now(),
		SuspensionType: req.SuspensionType,
		Evidence:       req.Evidence,
	}
	updateUser := func(u *auth_entities.User) bool {
		u.Suspensions = append(u.Suspensions, suspension)
		return true
	}
	_, err = userDAO.TryUpdate(ctx, user.Id, updateUser, DefaultRetry)
	if err != nil {
		return nil, err
	}

	tokensDeleted := 0
	if logoutUser {
		// Remove any tokens associated with user.
		tokensDeleted, err = s.deleteUserTokensForTenant(ctx, user.Id, req.TenantId)
		if err != nil {
			return nil, err
		}

		// Revoke any user cookies used for authentication, since we are not removing the user
		userDAO.UpdateNonce(ctx, user.Id)
	}

	return &authpb.CreateUserSuspensionResponse{
		SuspensionId:  suspensionId,
		TokensDeleted: int32(tokensDeleted),
	}, nil
}

func (s *AuthGrpcServer) DeleteUserSuspensions(ctx context.Context, req *authpb.DeleteUserSuspensionsRequest) (*authpb.DeleteUserSuspensionsResponse, error) {
	// Lift an existing user suspension
	log.Info().Str("user_id", req.UserId).Msg("DeleteUserSuspensions request received")

	err := authCheck(ctx, req.TenantId, tokenexchangeproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	authInfo, _ := auth.GetAugmentClaims(ctx)
	iapEmail, ok := authInfo.GetIapEmail()
	if ok {
		s.auditLogger.WriteAuditLog(
			iapEmail,
			authInfo.OpaqueUserIDType,
			authInfo.TenantName,
			fmt.Sprintf("Lift suspensions %v for user '%s' from tenant %s", req.SuspensionIds, req.UserId, authInfo.TenantName),
		)
	}

	userDAO := s.daoFactory.GetUserDAO()

	user, err := userDAO.Get(ctx, req.UserId)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, status.Error(codes.NotFound, "Resource not found")
	}

	// Create a map for quick lookup of suspension IDs to remove
	toRemove := make(map[string]bool)
	for _, id := range req.SuspensionIds {
		toRemove[id] = true
	}

	// Lift suspension by suspension ID
	suspensionsDeleted := 0
	updateUser := func(u *auth_entities.User) bool {
		// Filter suspensions, keeping only those not in the removal list
		newSuspensions := make([]*auth_entities.UserSuspension, 0, len(u.Suspensions))
		for _, s := range u.Suspensions {
			if !toRemove[s.SuspensionId] {
				newSuspensions = append(newSuspensions, s)
			}
		}
		suspensionsDeleted = len(u.Suspensions) - len(newSuspensions)
		u.Suspensions = newSuspensions
		return true
	}
	_, err = userDAO.TryUpdate(ctx, user.Id, updateUser, DefaultRetry)
	if err != nil {
		return nil, err
	}

	return &authpb.DeleteUserSuspensionsResponse{
		SuspensionsDeleted: int32(suspensionsDeleted),
	}, nil
}

func (s *AuthGrpcServer) UpdateSuspensionExemption(ctx context.Context, req *authpb.UpdateSuspensionExemptionRequest) (*authpb.UpdateSuspensionExemptionResponse, error) {
	// Update suspension exemption for a user
	log.Info().Str("user_id", req.UserId).Msg("UpdateSuspensionExemption request received")

	err := authCheck(ctx, req.TenantId, tokenexchangeproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	authInfo, _ := auth.GetAugmentClaims(ctx)
	iapEmail, ok := authInfo.GetIapEmail()
	if ok {
		s.auditLogger.WriteAuditLog(
			iapEmail,
			authInfo.OpaqueUserIDType,
			authInfo.TenantName,
			fmt.Sprintf("Update suspension exemption for user '%s' from tenant %s to %v", req.UserId, authInfo.TenantName, req.Exempt),
		)
	}

	userDAO := s.daoFactory.GetUserDAO()

	user, err := userDAO.Get(ctx, req.UserId)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, status.Error(codes.NotFound, "Resource not found")
	}

	// Update suspension exemption
	updateUser := func(u *auth_entities.User) bool {
		u.SuspensionExempt = req.Exempt
		return true
	}
	_, err = userDAO.TryUpdate(ctx, user.Id, updateUser, DefaultRetry)
	if err != nil {
		return nil, err
	}

	return &authpb.UpdateSuspensionExemptionResponse{
		UserId:   req.UserId,
		TenantId: req.TenantId,
		Exempt:   req.Exempt,
	}, nil
}

// Remove self-serve accounts for users that are on team / enterprise plans already
func (s *AuthGrpcServer) RemoveSelfServeAccountsForTeam(ctx context.Context, req *authpb.RemoveSelfServeAccountsForTeamRequest) (*authpb.RemoveSelfServeAccountsForTeamResponse, error) {
	requestID := uuid.New().String()
	logger := log.With().Str("requestID", requestID).Logger()
	logger.Info().Msg("RemoveSelfServeAccountsForTeam request received")

	err := authCheck(ctx, "", tokenexchangeproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unknown, "Invalid context")
	}
	iapEmail, ok := authClaims.GetIapEmail()
	if ok {
		s.auditLogger.WriteAuditLog(
			iapEmail,
			authClaims.OpaqueUserIDType,
			authClaims.TenantName,
			fmt.Sprintf("RemoveExtraSelfServeTenantsFromUsers request received"),
		)
	}

	resp := &authpb.RemoveSelfServeAccountsForTeamResponse{
		RemovedUsers: make([]*authpb.RemoveSelfServeAccountsForTeamResponse_UserRemovals, 0),
	}

	// Iterate through all users
	userDAO := s.daoFactory.GetUserDAO()
	failedUsers := make([]string, 0)

	var wg sync.WaitGroup
	var error error
	wg.Add(1)
	go func() {
		defer wg.Done()
		backgroundCtx := context.Background()
		if authClaims, ok := auth.GetAugmentClaims(ctx); ok {
			backgroundCtx = authClaims.NewContext(backgroundCtx)
		}

		// Process all users
		err := userDAO.FindAll(backgroundCtx, func(user *auth_entities.User) bool {
			// only process users in the specified tenant if provided
			// if no tenant is specified, process all users
			if req.TenantId != nil && *req.TenantId != "" && !slices.Contains(user.Tenants, *req.TenantId) {
				return true
			}

			// only return users with multiple tenants
			if len(user.Tenants) == 1 {
				return true
			}

			// check if any tenant is an enterprise or team tenant
			primaryTenants := make([]*authpb.RemoveSelfServeAccountsForTeamResponse_TenantInfo, 0)
			teamOrEnterpriseTenantIDs := make([]string, 0)
			for _, tenantID := range user.Tenants {

				// skip processing this user if any of their tenants are in the ignore list
				if slices.Contains(req.TenantIdsToIgnore, tenantID) {
					return true
				}
				tenant, err := s.getTenant(tenantID)
				if err != nil {
					logger.Error().Err(err).Msgf("Error getting tenant %s", tenantID)
					failedUsers = append(failedUsers, user.Id)
					return true
				}
				if tenant.DeletedAt != "" {
					continue
				}
				if tenantutil.IsEnterpriseTenant(tenant) || tenantutil.IsSelfServeTeamTenant(tenant) {
					teamOrEnterpriseTenantIDs = append(teamOrEnterpriseTenantIDs, tenantID)
					primaryTenants = append(primaryTenants, &authpb.RemoveSelfServeAccountsForTeamResponse_TenantInfo{
						TenantId:   tenantID,
						TenantName: tenant.Name,
					})
				}
			}

			if len(teamOrEnterpriseTenantIDs) == 0 {
				return true
			}

			logger.Info().Msgf("User %s is on team/enterprise tenant %v and has other tenants", user.Id, teamOrEnterpriseTenantIDs)

			// remove any tenant that is not an enterprise or team tenant
			tenantsToRemove := make([]*authpb.RemoveSelfServeAccountsForTeamResponse_TenantInfo, 0)
			for _, tenantID := range user.Tenants {
				if !slices.Contains(teamOrEnterpriseTenantIDs, tenantID) {
					tenant, err := s.getTenant(tenantID)
					if err != nil {
						logger.Error().Err(err).Msgf("Error getting tenant %s", tenantID)
						continue
					}
					logger.Info().Msgf("Would remove user %s from tenant %s (%s)", user.Id, tenantID, tenant.Name)
					tenantsToRemove = append(tenantsToRemove, &authpb.RemoveSelfServeAccountsForTeamResponse_TenantInfo{
						TenantId:   tenantID,
						TenantName: tenant.Name,
					})

					if !req.DryRun {
						// Remove the user from the tenant
						removeTenantReq := &authpb.RemoveUserFromTenantRequest{
							UserId:   user.Id,
							TenantId: tenantID,
						}
						_, err := s.removeUserFromTenant(backgroundCtx, removeTenantReq, nil)
						if err != nil {
							logger.Error().Err(err).Msgf("Error removing user %s from tenant %s", user.Id, tenantID)
							if !slices.Contains(failedUsers, user.Id) {
								failedUsers = append(failedUsers, user.Id)
							}
						} else {
							logger.Info().Msgf("Removed user %s from tenant %s", user.Id, tenantID)
						}
					}
				}
			}

			if len(tenantsToRemove) > 0 {
				resp.RemovedUsers = append(resp.RemovedUsers, &authpb.RemoveSelfServeAccountsForTeamResponse_UserRemovals{
					UserId:         user.Id,
					PrimaryTenants: primaryTenants,
					RemovedTenants: tenantsToRemove,
				})
			}
			return true
		})
		if err != nil {
			logger.Error().Err(err).Msg("Error iterating through users")
			error = err
		}

		logger.Info().Int("users processed", len(resp.RemovedUsers)).Int("failed_users", len(failedUsers)).Msg("RemoveSelfServeAccountsForTeam request completed")
	}()
	wg.Wait()

	if error != nil {
		logger.Error().Err(error).Msg("Error iterating through users")
		return nil, error
	}

	resp.FailedUsers = failedUsers

	return resp, nil
}

func (s *AuthGrpcServer) RemoveExtraSelfServeTenantsFromUsers(ctx context.Context, req *authpb.RemoveExtraSelfServeTenantsFromUsersRequest) (*authpb.RemoveExtraSelfServeTenantsFromUsersResponse, error) {
	requestID := uuid.New().String()
	logger := log.With().Str("requestID", requestID).Logger()

	logger.Info().Msg("RemoveExtraSelfServeTenantsFromUsers request received")

	err := authCheck(ctx, "", tokenexchangeproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unknown, "Invalid context")
	}
	iapEmail, ok := authClaims.GetIapEmail()
	if ok {
		s.auditLogger.WriteAuditLog(
			iapEmail,
			authClaims.OpaqueUserIDType,
			authClaims.TenantName,
			fmt.Sprintf("RemoveExtraSelfServeTenantsFromUsers request received"),
		)
	}

	resp := &authpb.RemoveExtraSelfServeTenantsFromUsersResponse{
		RemovedUsers: make([]*authpb.RemoveExtraSelfServeTenantsFromUsersResponse_UserRemovals, 0),
	}

	checkNamespace := len(req.NamespaceIds) > 0 && !(len(req.NamespaceIds) == 1 && req.NamespaceIds[0] == "")

	// Iterate through all users
	userDAO := s.daoFactory.GetUserDAO()
	failedUsers := make([]string, 0)

	var wg sync.WaitGroup
	var error error
	wg.Add(1)
	go func() {
		defer wg.Done()
		backgroundCtx := context.Background()
		if authClaims, ok := auth.GetAugmentClaims(ctx); ok {
			backgroundCtx = authClaims.NewContext(backgroundCtx)
		}
		err := userDAO.FindAll(backgroundCtx, func(user *auth_entities.User) bool {
			userLog := logger.With().Str("userID", user.Id).Logger()

			// only process users with multiple tenants
			if len(user.Tenants) <= 1 {
				return true
			}

			primaryTenant := &authpb.RemoveExtraSelfServeTenantsFromUsersResponse_TenantInfo{}
			var primaryTenantTier tw_pb.TenantTier
			tenantsToRemove := make([]*authpb.RemoveExtraSelfServeTenantsFromUsersResponse_TenantInfo, 0)

			// Phase 1: Collect all tenant information
			userTenants := make([]*tw_pb.Tenant, 0, len(user.Tenants))
			for _, tenantID := range user.Tenants {
				tenant, err := s.getTenant(tenantID)
				if err != nil {
					userLog.Error().Err(err).Msgf("Error getting tenant %s", tenantID)
					failedUsers = append(failedUsers, user.Id)
					return true
				}
				if tenant.DeletedAt != "" {
					continue
				}
				userTenants = append(userTenants, tenant)
			}

			userLog.Info().Msgf("Got %d active tenants for user", len(userTenants))

			// Phase 2: Check if user is in specified namespace
			if checkNamespace {
				inRequestedNamespace := slices.ContainsFunc(userTenants, func(tenant *tw_pb.Tenant) bool {
					return slices.Contains(req.NamespaceIds, tenant.ShardNamespace)
				})
				if !inRequestedNamespace {
					return true
				}
			}

			// Phase 3: Check for enterprise or team tenants
			for _, tenant := range userTenants {
				if tenantutil.IsSelfServeTeamTenant(tenant) || tenantutil.IsEnterpriseTenant(tenant) {
					userLog.Info().Msgf("User is on enterprise or team tenant, skipping")
					return true
				}
			}

			userLog.Info().Msgf("User is in specified namespace and has no enterprise or team tenants, processing...")

			// Phase 4: Determine primary tenant and tenants to remove
			for i, tenant := range userTenants {
				if i == 0 {
					primaryTenant.TenantId = tenant.Id
					primaryTenant.TenantName = tenant.Name
					primaryTenantTier = tenant.Tier
				} else {
					if tenant.Tier != primaryTenantTier {
						userLog.Warn().Msgf("User %s is on tenants with different tiers (%s vs %s). Skipping.",
							user.Id, primaryTenantTier, tenant.Tier)
						return true
					}
					tenantsToRemove = append(tenantsToRemove, &authpb.RemoveExtraSelfServeTenantsFromUsersResponse_TenantInfo{
						TenantId:   tenant.Id,
						TenantName: tenant.Name,
					})
				}
			}

			if primaryTenant.TenantId == "" {
				userLog.Info().Msgf("User %s is not on any tenants. Skipping.", user.Id)
				return true
			}

			if len(tenantsToRemove) == 0 {
				return true
			}

			// Phase 5: Remove the user from the unnecessary tenants

			userLog.Info().Msgf("User %s is on primary tenant %s and has other tenants %v", user.Id, primaryTenant.TenantName, tenantsToRemove)

			if req.MakeChanges {
				// Remove the user from the unnecessary tenants
				for _, tenantToRemove := range tenantsToRemove {
					userLog.Info().Msgf("Removing user %s from tenant %s (%s)", user.Id, tenantToRemove.TenantId, tenantToRemove.TenantName)
					removeTenantReq := &authpb.RemoveUserFromTenantRequest{
						UserId:   user.Id,
						TenantId: tenantToRemove.TenantId,
					}
					_, err := s.removeUserFromTenant(backgroundCtx, removeTenantReq, nil)
					if err != nil {
						if !slices.Contains(failedUsers, user.Id) {
							failedUsers = append(failedUsers, user.Id)
						}
						userLog.Error().Err(err).Msgf("Error removing user %s from tenant %s", user.Id, tenantToRemove.TenantId)
					} else {
						userLog.Info().Msgf("Removed user %s from tenant %s", user.Id, tenantToRemove.TenantId)
					}
				}
			} else {
				userLog.Info().Msgf("[dry-run] Would remove user %s from tenants %v", user.Id, tenantsToRemove)
			}

			resp.RemovedUsers = append(resp.RemovedUsers, &authpb.RemoveExtraSelfServeTenantsFromUsersResponse_UserRemovals{
				UserId:         user.Id,
				PrimaryTenant:  primaryTenant,
				RemovedTenants: tenantsToRemove,
			})
			return true
		})
		if err != nil {
			logger.Error().Err(err).Msg("Error iterating through users")
			error = err
		}

		logger.Info().Int("users processed", len(resp.RemovedUsers)).Int("failed_users", len(failedUsers)).Msg("RemoveExtraSelfServeTenantsFromUsers request completed")
	}()
	wg.Wait()

	if error != nil {
		logger.Error().Err(error).Msg("Error iterating through users")
		return nil, error
	}

	resp.FailedUsers = failedUsers

	return resp, nil
}

func (s *AuthGrpcServer) RemoveDeletedTenantsFromUsers(ctx context.Context, req *authpb.RemoveDeletedTenantsFromUsersRequest) (*authpb.RemoveDeletedTenantsFromUsersResponse, error) {
	requestID := uuid.New().String()
	logger := log.With().Str("requestID", requestID).Logger()

	logger.Info().Msg("RemoveDeletedTenantsFromUsers request received")

	err := authCheck(ctx, "", tokenexchangeproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unknown, "Invalid context")
	}
	iapEmail, ok := authClaims.GetIapEmail()
	if ok {
		s.auditLogger.WriteAuditLog(
			iapEmail,
			authClaims.OpaqueUserIDType,
			authClaims.TenantName,
			fmt.Sprintf("RemoveDeletedTenantsFromUsers request received"),
		)
	}

	resp := &authpb.RemoveDeletedTenantsFromUsersResponse{
		RemovedUsers: make([]*authpb.RemoveDeletedTenantsFromUsersResponse_UserRemovals, 0),
	}

	// Iterate through all users
	userDAO := s.daoFactory.GetUserDAO()
	failedUsers := make([]string, 0)

	var wg sync.WaitGroup
	var error error
	wg.Add(1)
	go func() {
		defer wg.Done()
		backgroundCtx := context.Background()
		if authClaims, ok := auth.GetAugmentClaims(ctx); ok {
			backgroundCtx = authClaims.NewContext(backgroundCtx)
		}

		err = userDAO.FindAll(backgroundCtx, func(user *auth_entities.User) bool {
			userLog := logger.With().Str("userID", user.Id).Logger()
			tenantsToRemove := make([]*authpb.RemoveDeletedTenantsFromUsersResponse_TenantInfo, 0)

			for _, tenantID := range user.Tenants {
				tenant, err := s.tenantMap.GetTenantByIdDeletedOk(tenantID)
				if err != nil {
					userLog.Error().Err(err).Msgf("Error getting tenant %s", tenantID)
					failedUsers = append(failedUsers, user.Id)
					return true
				}

				// Skip active tenants
				if tenant != nil && tenant.DeletedAt == "" {
					continue
				}

				// Add deleted tenant to removal list
				tenantInfo := &authpb.RemoveDeletedTenantsFromUsersResponse_TenantInfo{
					TenantId: tenantID,
				}
				if tenant != nil {
					tenantInfo.TenantName = tenant.Name
				}
				tenantsToRemove = append(tenantsToRemove, tenantInfo)
			}

			if len(tenantsToRemove) == 0 {
				return true
			}

			userLog.Info().Msgf("User %s is on deleted tenants %v", user.Id, tenantsToRemove)

			if req.MakeChanges {
				// Remove the user from the tenant
				for _, tenantToRemove := range tenantsToRemove {
					userLog.Info().Msgf("Removing user %s from tenant %s (%s)", user.Id, tenantToRemove.TenantId, tenantToRemove.TenantName)
					removeTenantReq := &authpb.RemoveUserFromTenantRequest{
						UserId:   user.Id,
						TenantId: tenantToRemove.TenantId,
					}
					_, err := s.removeUserFromTenant(backgroundCtx, removeTenantReq, req.TenantMappingsForDeletedTenants)
					if err != nil {
						if !slices.Contains(failedUsers, user.Id) {
							failedUsers = append(failedUsers, user.Id)
						}
						userLog.Error().Err(err).Msgf("Error removing user %s from tenant %s", user.Id, tenantToRemove.TenantId)
					} else {
						userLog.Info().Msgf("Removed user %s from tenant %s", user.Id, tenantToRemove.TenantId)
					}
				}
			} else {
				userLog.Info().Msgf("[dry-run] Would remove user %s from tenants %v", user.Id, tenantsToRemove)
			}

			resp.RemovedUsers = append(resp.RemovedUsers, &authpb.RemoveDeletedTenantsFromUsersResponse_UserRemovals{
				UserId:         user.Id,
				RemovedTenants: tenantsToRemove,
			})
			return true
		})
		if err != nil {
			log.Error().Err(err).Msg("Error iterating through users")
			error = err
		}

		logger.Info().Int("users processed", len(resp.RemovedUsers)).Int("failed_users", len(failedUsers)).Msg("RemoveDeletedTenantsFromUsers request completed")
	}()
	wg.Wait()

	if error != nil {
		log.Error().Err(error).Msg("Error iterating through users")
		return nil, error
	}
	resp.FailedUsers = failedUsers

	return resp, nil
}

func (s *AuthGrpcServer) SetUsersBillingMethodToOrb(ctx context.Context, req *authpb.SetUsersBillingMethodToOrbRequest) (*authpb.SetUsersBillingMethodToOrbResponse, error) {
	requestID := uuid.New().String()
	logger := log.With().Str("requestID", requestID).Logger()

	logger.Info().Msg("SetUsersBillingMethodToOrb request received")

	err := authCheck(ctx, "", tokenexchangeproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	// Get auth claims for audit logging
	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unknown, "Invalid context")
	}
	iapEmail, ok := authClaims.GetIapEmail()
	if ok {
		s.auditLogger.WriteAuditLog(
			iapEmail,
			authClaims.OpaqueUserIDType,
			authClaims.TenantName,
			fmt.Sprintf("SetUsersBillingMethodToOrb request received"),
		)
	}

	resp := &authpb.SetUsersBillingMethodToOrbResponse{
		UpdatedUserIds: make([]string, 0),
		FailedUserIds:  make([]string, 0),
	}

	// Iterate through all users
	userDAO := s.daoFactory.GetUserDAO()

	var wg sync.WaitGroup
	var error error
	wg.Add(1)
	go func() {
		defer wg.Done()
		backgroundCtx := authClaims.NewContext(context.Background())

		err = userDAO.FindAll(backgroundCtx, func(user *auth_entities.User) bool {
			userLog := logger.With().Str("userID", user.Id).Logger()

			// Skip users that already have BillingMethod set to ORB
			if user.BillingMethod == auth_entities.BillingMethod_BILLING_METHOD_ORB {
				return true
			}

			userLog.Info().Msgf("User %s has BillingMethod %v, will update to ORB", user.Id, user.BillingMethod)

			if req.MakeChanges {
				// Update the user's BillingMethod to ORB using TryUpdate
				_, err := userDAO.TryUpdate(backgroundCtx, user.Id, func(u *auth_entities.User) bool {
					u.BillingMethod = auth_entities.BillingMethod_BILLING_METHOD_ORB
					return true
				}, DefaultRetry)
				if err != nil {
					userLog.Error().Err(err).Msg("Failed to update user's BillingMethod")
					resp.FailedUserIds = append(resp.FailedUserIds, user.Id)
					return true
				}
				userLog.Info().Msg("Updated user's BillingMethod to ORB")
				// Add user to the response after successful update
				resp.UpdatedUserIds = append(resp.UpdatedUserIds, user.Id)
			} else {
				userLog.Info().Msg("[dry-run] Would update user's BillingMethod to ORB")
				// In dry-run mode, add user to the response as it would be updated
				resp.UpdatedUserIds = append(resp.UpdatedUserIds, user.Id)
			}

			return true
		})
		if err != nil {
			log.Error().Err(err).Msg("Error iterating through users")
			error = err
		}

		logger.Info().Int("users processed", len(resp.UpdatedUserIds)).Int("failed_users", len(resp.FailedUserIds)).Msg("SetUsersBillingMethodToOrb request completed")
	}()
	wg.Wait()

	if error != nil {
		log.Error().Err(error).Msg("Error iterating through users")
		return nil, error
	}

	return resp, nil
}

func (s *AuthGrpcServer) ScanLegacySelfServeTeams(ctx context.Context, req *authpb.ScanLegacySelfServeTeamsRequest) (*authpb.ScanLegacySelfServeTeamsResponse, error) {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, err
	}
	ctx = requestContext.AnnotateLogContext(ctx)

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	ctx = authInfo.AnnotateLogContext(ctx)

	log.Ctx(ctx).Info().Msg("ScanLegacySelfServeTeams request received")

	// Auth check - require AUTH_R scope
	err = authCheck(ctx, "", tokenexchangeproto.Scope_AUTH_R)
	if err != nil {
		return nil, err
	}

	// Get auth claims for audit logging
	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unknown, "Invalid context")
	}
	iapEmail, ok := authClaims.GetIapEmail()
	if ok {
		s.auditLogger.WriteAuditLog(
			iapEmail,
			authClaims.OpaqueUserIDType,
			authClaims.TenantName,
			fmt.Sprintf("ScanLegacySelfServeTeams request received"),
		)
	}

	// Initialize response
	resp := &authpb.ScanLegacySelfServeTeamsResponse{
		TeamSubscriptions: make([]*authpb.ScanLegacySelfServeTeamsResponse_TeamSubscription, 0),
		FailedTenants:     make([]string, 0),
	}

	// Get all tenants first to identify self-serve team tenants
	tenants, err := s.tenantMap.tenantCache.GetAllTenants()
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get all tenants")
		return nil, status.Error(codes.Internal, "Failed to get tenants")
	}

	// Process each tenant to find legacy self-serve team tenants
	for _, tenant := range tenants {
		// Skip deleted tenants
		if tenant.DeletedAt != "" {
			continue
		}
		// Only process legacy self-serve team tenants
		if !tenantutil.IsLegacySelfServeTeamTenant(tenant) {
			continue
		}

		log.Ctx(ctx).Info().Msg("Found legacy self-serve team tenant")

		// Get all users in this tenant using UserTenantMappingDAO
		tenantMappingDAO := s.daoFactory.GetUserTenantMappingDAO(tenant.Name)
		userIDs := make([]string, 0)

		// First, count users and collect user IDs
		if err := tenantMappingDAO.FindAll(ctx, func(mapping *auth_entities.UserTenantMapping) bool {
			userIDs = append(userIDs, mapping.UserId)
			return true
		}); err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Error finding users in tenant")
			resp.FailedTenants = append(resp.FailedTenants, tenant.Id)
			continue
		}

		teamSub := &authpb.ScanLegacySelfServeTeamsResponse_TeamSubscription{
			TenantId:   tenant.Id,
			TenantName: tenant.Name,
			UserCount:  int32(len(userIDs)),
		}

		// Add team subscription to response, even if we are unable to find a Stripe subscription for the team later
		resp.TeamSubscriptions = append(resp.TeamSubscriptions, teamSub)

		// Try to find a single active Stripe subscription for the team
		activeStripeSubscription, adminUser, err := s.findActiveStripeSubscriptionForTenant(ctx, tenant, userIDs)
		if err != nil || activeStripeSubscription == nil || adminUser == nil {
			log.Ctx(ctx).Error().Err(err).Msg("Unable to find active Stripe subscription for tenant")
			teamSub.FindStripeSubscriptionError = fmt.Sprintf("%v", err)
			continue
		}

		// Populate team subscription with Stripe subscription details if we successfully find a single active subscription for the team
		teamSub.AdminUserId = adminUser.Id
		teamSub.StripeCustomerId = adminUser.StripeCustomerId
		teamSub.StripeSubscriptionId = activeStripeSubscription.ID
		teamSub.StripeSubscriptionStatus = string(activeStripeSubscription.Status)
		if activeStripeSubscription.Items != nil &&
			len(activeStripeSubscription.Items.Data) == 1 &&
			activeStripeSubscription.Items.Data[0].Price != nil {
			teamSub.StripeSubscriptionPriceId = activeStripeSubscription.Items.Data[0].Price.ID
			teamSub.StripeSubscriptionPriceKey = activeStripeSubscription.Items.Data[0].Price.LookupKey
			teamSub.StripeSubscriptionQuantity = activeStripeSubscription.Items.Data[0].Quantity
		}
		teamSub.StripeSubscriptionCurrentPeriodStart = timestamppb.New(time.Unix(activeStripeSubscription.CurrentPeriodStart, 0))
		teamSub.StripeSubscriptionCurrentPeriodEnd = timestamppb.New(time.Unix(activeStripeSubscription.CurrentPeriodEnd, 0))
	}

	log.Ctx(ctx).Info().
		Int("teams_processed", len(resp.TeamSubscriptions)).
		Int("failed_tenants", len(resp.FailedTenants)).
		Msg("ScanLegacySelfServeTeams request completed")

	return resp, nil
}

func (s *AuthGrpcServer) DeleteTenantSubscriptionMapping(ctx context.Context, req *authpb.DeleteTenantSubscriptionMappingRequest) (*authpb.DeleteTenantSubscriptionMappingResponse, error) {
	// Validate tenant_id is provided
	if req.TenantId == "" {
		return nil, status.Error(codes.Internal, "tenant_id is required")
	}

	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, err
	}
	ctx = requestContext.AnnotateLogContext(ctx)

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	ctx = authInfo.AnnotateLogContext(ctx)

	log.Ctx(ctx).Info().Str("tenant_id", req.TenantId).Msg("DeleteTenantSubscriptionMapping request received")

	// Auth check - require AUTH_RW scope
	err = authCheck(ctx, req.TenantId, tokenexchangeproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	// Get auth claims for audit logging
	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unknown, "Invalid context")
	}
	iapEmail, ok := authClaims.GetIapEmail()
	if ok {
		s.auditLogger.WriteAuditLog(
			iapEmail,
			authClaims.OpaqueUserIDType,
			authClaims.TenantName,
			fmt.Sprintf("DeleteTenantSubscriptionMapping request received for tenant_id: %s", req.TenantId),
		)
	}

	// Delete the tenant subscription mapping
	tenantSubscriptionMappingDAO := s.daoFactory.GetTenantSubscriptionMappingDAO()
	err = tenantSubscriptionMappingDAO.Delete(ctx, req.TenantId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("tenant_id", req.TenantId).Msg("Failed to delete tenant subscription mapping")
		return nil, status.Error(codes.Internal, "Failed to delete tenant subscription mapping")
	}

	log.Ctx(ctx).Info().Str("tenant_id", req.TenantId).Msg("DeleteTenantSubscriptionMapping request completed successfully")

	return &authpb.DeleteTenantSubscriptionMappingResponse{}, nil
}

func (s *AuthGrpcServer) UpdateSubscriptionOwnerToTeam(ctx context.Context, req *authpb.UpdateSubscriptionOwnerToTeamRequest) (*authpb.UpdateSubscriptionOwnerToTeamResponse, error) {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, err
	}
	ctx = requestContext.AnnotateLogContext(ctx)

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	ctx = authInfo.AnnotateLogContext(ctx)

	// Auth check - require AUTH_RW scope for all tenants
	err = authCheck(ctx, "", tokenexchangeproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	log.Ctx(ctx).Info().Strs("subscription_ids", req.SubscriptionIds).Msg("UpdateSubscriptionOwnerToTeam request received")

	// Process each subscription ID
	results := make([]*authpb.UpdateSubscriptionOwnerToTeamResponse_SubscriptionUpdateResult, 0, len(req.SubscriptionIds))

	for _, subscriptionId := range req.SubscriptionIds {
		result := s.updateSingleSubscriptionOwnerToTeam(ctx, subscriptionId)
		results = append(results, result)
	}

	log.Ctx(ctx).Info().Int("total_subscriptions", len(req.SubscriptionIds)).Msg("UpdateSubscriptionOwnerToTeam request completed")

	return &authpb.UpdateSubscriptionOwnerToTeamResponse{
		Results: results,
	}, nil
}

// Helper function to update a single subscription
func (s *AuthGrpcServer) updateSingleSubscriptionOwnerToTeam(ctx context.Context, subscriptionId string) *authpb.UpdateSubscriptionOwnerToTeamResponse_SubscriptionUpdateResult {
	result := &authpb.UpdateSubscriptionOwnerToTeamResponse_SubscriptionUpdateResult{
		SubscriptionId: subscriptionId,
		Success:        false,
	}

	// Get the subscription first to check its current owner
	subscriptionDAO := s.daoFactory.GetSubscriptionDAO()
	subscription, err := subscriptionDAO.Get(ctx, subscriptionId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("subscription_id", subscriptionId).Msg("Failed to get subscription")
		result.ErrorMessage = "Failed to get subscription"
		return result
	}
	if subscription == nil {
		log.Ctx(ctx).Error().Str("subscription_id", subscriptionId).Msg("Subscription not found")
		result.ErrorMessage = "Subscription not found"
		return result
	}

	// Verify the owner field of the subscription is a user not a tenant
	userOwner, ok := subscription.Owner.(*auth_entities.Subscription_UserId)
	if !ok {
		log.Ctx(ctx).Error().Str("subscription_id", subscriptionId).Msg("Subscription owner is not a user")
		result.ErrorMessage = "Subscription owner is not a user"
		return result
	}

	userID := userOwner.UserId
	log.Ctx(ctx).Info().Str("user_id", userID).Str("subscription_id", subscriptionId).Msg("Found user-owned subscription")

	// Get the user to retrieve their tenant information
	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, userID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", userID).Msg("Failed to get user")
		result.ErrorMessage = "Failed to get user"
		return result
	}
	if user == nil {
		log.Ctx(ctx).Error().Str("user_id", userID).Msg("User not found")
		result.ErrorMessage = "User not found"
		return result
	}

	// Verify user has only 1 tenant
	if len(user.Tenants) != 1 {
		log.Ctx(ctx).Error().Str("user_id", userID).Int("tenant_count", len(user.Tenants)).Msg("User does not belong to exactly one tenant")
		result.ErrorMessage = "User must belong to exactly one tenant"
		return result
	}

	tenantID := user.Tenants[0]
	log.Ctx(ctx).Info().Str("tenant_id", tenantID).Str("user_id", userID).Msg("Found user's tenant")

	// Get the tenant to verify it's a self-serve team
	tenant, err := s.getTenant(tenantID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("tenant_id", tenantID).Msg("Failed to get tenant")
		result.ErrorMessage = "Failed to get tenant"
		return result
	}
	if tenant == nil {
		log.Ctx(ctx).Error().Str("tenant_id", tenantID).Msg("Tenant not found")
		result.ErrorMessage = "Tenant not found"
		return result
	}

	// Verify tenantutil.isSelfServeTeam is true for the tenant
	if !tenantutil.IsSelfServeTeamTenant(tenant) {
		log.Ctx(ctx).Error().Str("tenant_id", tenantID).Msg("Tenant is not a self-serve team")
		result.ErrorMessage = "Tenant must be a self-serve team"
		return result
	}

	log.Ctx(ctx).Info().Str("tenant_id", tenantID).Msg("Verified tenant is a self-serve team")

	// Get auth claims for audit logging
	authClaims, ok := auth.GetAugmentClaims(ctx)
	if ok {
		iapEmail, ok := authClaims.GetIapEmail()
		if ok {
			s.auditLogger.WriteAuditLog(
				iapEmail,
				authClaims.OpaqueUserIDType,
				authClaims.TenantName,
				fmt.Sprintf("UpdateSubscriptionOwnerToTeam request received for subscription_id: %s, changing owner from user %s to tenant %s", subscriptionId, userID, tenantID),
			)
		}
	}

	// Update the subscription owner to be the tenant
	_, err = subscriptionDAO.TryUpdate(ctx, subscriptionId, func(sub *auth_entities.Subscription) bool {
		sub.Owner = &auth_entities.Subscription_TenantId{TenantId: tenantID}
		return true
	}, DefaultRetry)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("subscription_id", subscriptionId).Msg("Failed to update subscription owner")
		result.ErrorMessage = "Failed to update subscription owner"
		return result
	}

	log.Ctx(ctx).Info().Str("subscription_id", subscriptionId).Str("tenant_id", tenantID).Msg("UpdateSubscriptionOwnerToTeam completed successfully for subscription")

	result.Success = true
	return result
}

func (s *AuthGrpcServer) findActiveStripeSubscriptionForTenant(ctx context.Context, tenant *tw_pb.Tenant, userIDs []string) (*stripe.Subscription, *auth_entities.User, error) {
	userDAO := s.daoFactory.GetUserDAO()

	// Initialize variables to store the active subscription and admin user
	var activeSubscription *stripe.Subscription
	var trialingSubscriptions []*stripe.Subscription
	var adminUser *auth_entities.User
	var trailingUsers []*auth_entities.User

	// Process each user to find Stripe subscriptions
	for _, userID := range userIDs {
		user, err := userDAO.Get(ctx, userID)
		if err != nil {
			return nil, nil, err
		}

		if user.OrbCustomerId != "" && user.OrbSubscriptionId != "" {
			orbSubscription, err := s.orbClient.GetUserSubscription(ctx, user.OrbSubscriptionId, nil)
			if err != nil {
				return nil, nil, err
			}
			// If any user has an active non-trial Orb subscription, return an error
			if parseOrbStatus(orbSubscription.OrbStatus) == auth_entities.Subscription_ORB_STATUS_ACTIVE &&
				orbSubscription.ExternalPlanID != s.orbConfig.getTrialPlan().ID {
				return nil, nil, fmt.Errorf("active Orb subscription found: user=%s, cus=%s, sub=%s", user.Id, user.OrbCustomerId, user.OrbSubscriptionId)
			}
		}

		// Check if the user has a StripeCustomerId
		if user.StripeCustomerId == "" {
			continue
		}

		// List all active Stripe subscriptions
		subscriptions, err := s.stripeClient.ListSubscriptions(user.StripeCustomerId)
		if err != nil {
			return nil, nil, err
		}

		for _, sub := range subscriptions {
			if sub.Status == stripe.SubscriptionStatusActive {
				if activeSubscription != nil {
					return nil, nil, fmt.Errorf(
						"multiple active Stripe subscriptions found, user1=%s, user2=%s, sub1=%s, sub2=%s",
						adminUser.Id, user.Id, activeSubscription.ID, sub.ID,
					)
				}
				activeSubscription = sub
				adminUser = user
			} else if sub.Status == stripe.SubscriptionStatusTrialing {
				trialingSubscriptions = append(trialingSubscriptions, sub)
				trailingUsers = append(trailingUsers, user)
			}
		}
	}

	// If we have no active subscriptions but exactly one trialing subscription, use that as the team's subscription
	if activeSubscription == nil && len(trialingSubscriptions) == 1 && len(trailingUsers) == 1 {
		activeSubscription = trialingSubscriptions[0]
		adminUser = trailingUsers[0]
	}

	return activeSubscription, adminUser, nil
}

func (s *AuthGrpcServer) MigrateLegacySelfServeTeams(ctx context.Context, req *authpb.MigrateLegacySelfServeTeamsRequest) (*authpb.MigrateLegacySelfServeTeamsResponse, error) {
	requestID := uuid.New().String()
	logger := log.With().Str("requestID", requestID).Logger()

	logger.Info().Int("teams_count", len(req.Teams)).Msg("MigrateLegacySelfServeTeams request received")

	// Check permissions - require AUTH_RW scope and wildcard tenant access
	err := authCheck(ctx, "", tokenexchangeproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unknown, "Invalid context")
	}

	// Audit log
	iapEmail, ok := authClaims.GetIapEmail()
	if ok {
		s.auditLogger.WriteAuditLog(
			iapEmail,
			authClaims.OpaqueUserIDType,
			authClaims.TenantName,
			fmt.Sprintf("MigrateLegacySelfServeTeams request received for %d teams", len(req.Teams)),
		)
	}

	resp := &authpb.MigrateLegacySelfServeTeamsResponse{
		Results: make([]*authpb.MigrateLegacySelfServeTeamsResponse_MigrationResult, 0, len(req.Teams)),
	}

	successfulMigrations := 0
	failedMigrations := 0

	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		backgroundCtx := authClaims.NewContext(context.Background())

		// Process each team migration
		for _, team := range req.Teams {
			teamLogger := logger.With().
				Str("tenant_name", team.TenantName).
				Str("admin_user_id", team.AdminUserId).
				Logger()

			teamLogger.Info().Msg("Starting migration for team")

			result := &authpb.MigrateLegacySelfServeTeamsResponse_MigrationResult{
				TenantName: team.TenantName,
				Success:    false,
			}

			// Migrate the team
			err := s.migrateTeam(backgroundCtx, team, teamLogger)
			if err != nil {
				teamLogger.Error().Err(err).Msg("Failed to migrate team")
				result.ErrorMessage = fmt.Sprintf("%v", err)
				failedMigrations++
			} else {
				result.Success = true
				successfulMigrations++
			}

			resp.Results = append(resp.Results, result)
		}
	}()
	wg.Wait()

	resp.SuccessfulMigrations = int32(successfulMigrations)
	resp.FailedMigrations = int32(failedMigrations)

	logger.Info().
		Int32("successful_migrations", resp.SuccessfulMigrations).
		Int32("failed_migrations", resp.FailedMigrations).
		Msg("MigrateLegacySelfServeTeams request completed")

	return resp, nil
}

// migrateTeam performs the migration of a single team from Stripe to Orb
func (s *AuthGrpcServer) migrateTeam(
	ctx context.Context,
	team *authpb.MigrateLegacySelfServeTeamsRequest_LegacyTeamInfo,
	logger zerolog.Logger,
) error {
	userDAO := s.daoFactory.GetUserDAO()

	// Get the admin user
	adminUser, err := userDAO.Get(ctx, team.AdminUserId)
	if err != nil || adminUser == nil {
		return fmt.Errorf("failed to get admin user: %w", err)
	}
	if len(adminUser.Tenants) != 1 {
		return fmt.Errorf("admin user %s is in multiple tenants", team.AdminUserId)
	}
	tenantId := adminUser.Tenants[0]

	tenant, err := s.tenantMap.GetTenantByID(tenantId)
	if err != nil || tenant == nil {
		return fmt.Errorf("failed to get tenant: %w", err)
	}
	if tenant.Name != team.TenantName {
		return fmt.Errorf("tenant name mismatch: expected %s, got %s", team.TenantName, tenant.Name)
	}
	if !tenantutil.IsLegacySelfServeTeamTenant(tenant) {
		return fmt.Errorf("tenant %s is not a legacy self-serve team tenant", tenantId)
	}

	tenantMappingDAO := s.daoFactory.GetUserTenantMappingDAO(team.TenantName)
	mapping, err := tenantMappingDAO.GetByUser(ctx, team.AdminUserId)
	if err != nil || mapping == nil {
		return fmt.Errorf("failed to get user tenant mapping: %w", err)
	}

	logger.Info().Msg("Starting team migration")

	// Create idempotency keys
	customerIdempotencyKey := fmt.Sprintf("%s-migrate-cus", tenantId)
	subscriptionIdempotencyKey := fmt.Sprintf("%s-migrate-sub", tenantId)

	// Step 1: Create or update TenantSubscriptionMapping
	logger.Info().Msg("Creating or updating TenantSubscriptionMapping")

	tsmDAO := s.daoFactory.GetTenantSubscriptionMappingDAO()
	existingTSM, err := tsmDAO.Get(ctx, tenantId)
	if err != nil {
		return fmt.Errorf("failed to check existing TenantSubscriptionMapping: %w", err)
	}

	if existingTSM != nil {
		// Update existing TenantSubscriptionMapping
		_, err = tsmDAO.TryUpdate(ctx, tenantId, func(tsm *auth_entities.TenantSubscriptionMapping) bool {
			tsm.StripeCustomerId = adminUser.StripeCustomerId
			tsm.BillingMethod = auth_entities.BillingMethod_BILLING_METHOD_ORB
			return true
		}, DefaultRetry)
		if err != nil {
			return fmt.Errorf("failed to update TenantSubscriptionMapping: %w", err)
		}
		logger.Info().Msg("Updated existing TenantSubscriptionMapping")
	} else {
		// Create new TenantSubscriptionMapping
		newTSM := &auth_entities.TenantSubscriptionMapping{
			TenantId:         tenantId,
			StripeCustomerId: adminUser.StripeCustomerId,
			BillingMethod:    auth_entities.BillingMethod_BILLING_METHOD_ORB,
			CreatedAt:        timestamppb.Now(),
		}
		_, err = tsmDAO.Create(ctx, newTSM)
		if err != nil {
			return fmt.Errorf("failed to create TenantSubscriptionMapping: %w", err)
		}
		logger.Info().Msg("Created new TenantSubscriptionMapping")
	}

	// Step 2: Update admin user's CustomerUIRoles for the tenant
	logger.Info().Msg("Updating admin user's CustomerUIRoles for the tenant")

	if !slices.Contains(mapping.CustomerUiRoles, auth_entities.CustomerUiRole_ADMIN) {
		mapping.CustomerUiRoles = append(mapping.CustomerUiRoles, auth_entities.CustomerUiRole_ADMIN)
		if _, err := tenantMappingDAO.Update(ctx, mapping); err != nil {
			return fmt.Errorf("failed to update admin user's CustomerUIRoles for the tenant: %w", err)
		}
	}

	// Step 3.1: Create Orb customer if not already created
	orbCustomerId := adminUser.OrbCustomerId
	if orbCustomerId == "" {
		logger.Info().Msg("Creating Orb customer for admin user")

		// create Orb Customer
		orbCustomer := orb.OrbCustomer{
			Email:    adminUser.Email,
			Name:     adminUser.Email,
			StripeID: adminUser.StripeCustomerId,
			Metadata: map[string]string{"augment_user_id": adminUser.Id},
			Timezone: "UTC",
		}
		orbCustomerId, err = s.orbClient.CreateCustomer(ctx, orbCustomer, true, &customerIdempotencyKey)
		if err != nil {
			return fmt.Errorf("failed to create Orb customer: %w", err)
		}

		// Add alerts for the Orb customer
		logger.Info().Str("orb_customer_id", orbCustomerId).Msg("Adding alerts for Orb customer")
		if err := s.orbClient.AddAlertsForCustomer(ctx, orbCustomerId, "usermessages"); err != nil {
			return fmt.Errorf("Failed to add alerts for Orb customer: %w", err)
		}

		// Update admin user with Orb customer ID
		if _, err := userDAO.TryUpdate(ctx, team.AdminUserId, func(u *auth_entities.User) bool {
			u.OrbCustomerId = orbCustomerId
			return true
		}, DefaultRetry); err != nil {
			return fmt.Errorf("failed to update admin user with Orb customer ID: %w", err)
		}
		logger.Info().Str("orb_customer_id", orbCustomerId).Msg("Updated admin user with Orb customer ID")
	} else {
		logger.Info().Str("orb_customer_id", orbCustomerId).Msg("Admin user already has Orb customer ID")
	}

	// Step 3.2: Update TenantSubscriptionMapping with Orb customer ID
	if _, err := tsmDAO.TryUpdate(ctx, tenantId, func(tsm *auth_entities.TenantSubscriptionMapping) bool {
		tsm.OrbCustomerId = orbCustomerId
		return true
	}, DefaultRetry); err != nil {
		return fmt.Errorf("failed to update TenantSubscriptionMapping with Orb customer ID: %w", err)
	}
	logger.Info().Str("orb_customer_id", orbCustomerId).Msg("Updated TenantSubscriptionMapping with Orb customer ID")

	// Step 4.1: Create Orb subscription if not already created
	orbSubscriptionId := adminUser.OrbSubscriptionId

	if orbSubscriptionId != "" {
		orbSubscription, err := s.orbClient.GetUserSubscription(ctx, orbSubscriptionId, &orb.ItemIds{
			SeatsID:            s.orbConfig.SeatsItemID,
			IncludedMessagesID: s.orbConfig.IncludedMessagesItemID,
		})
		if err != nil {
			return fmt.Errorf("failed to get Orb subscription: %w", err)
		}

		// If the user already has an active subscription, check it is the correct plan and has correct number of seats
		if parseOrbStatus(orbSubscription.OrbStatus) == auth_entities.Subscription_ORB_STATUS_ACTIVE &&
			orbSubscription.ExternalPlanID != s.orbConfig.getTrialPlan().ID {

			// plan or number of seats mismatch, return an error
			if orbSubscription.ExternalPlanID != team.OrbExternalPlanId ||
				orbSubscription.CurrentFixedQuantities.Seats != int(team.Seats) {
				return fmt.Errorf("Orb subscription already exists, but subscription is not configured correctly")
			}

			// plan and number of seats are correct, no need to create a new subscription
			logger.Info().
				Str("orb_subscription_id", orbSubscriptionId).
				Str("external_plan_id", orbSubscription.ExternalPlanID).
				Msg("Orb subscription already exists and configured correctly")
		} else {
			// if the subscription is ended or on-trial, clear the orbSubscriptionId so we create a new one below
			logger.Info().
				Str("orb_subscription_id", orbSubscriptionId).
				Str("orb_status", orbSubscription.OrbStatus).
				Str("external_plan_id", orbSubscription.ExternalPlanID).
				Msg("Orb subscription already exists, but is not active or is a trial plan")
			orbSubscriptionId = ""
		}
	}

	if orbSubscriptionId == "" {
		logger.Info().
			Str("orb_external_plan_id", team.OrbExternalPlanId).
			Int32("seats", team.Seats).
			Msg("Creating Orb subscription")

		if team.OrbExternalPlanId == "" ||
			team.OrbPlanVersionNumber == 0 ||
			team.StripeCurrentPeriodStart.AsTime().IsZero() ||
			team.StripeCurrentPeriodEnd.AsTime().IsZero() {
			return fmt.Errorf("missing required fields for creating Orb subscription")
		}

		// Align Orb subscription start date to current Stripe start date
		startDate := team.StripeCurrentPeriodStart.AsTime()
		var seatsPriceId string
		var pricePerSeat float64
		var messagesPriceID string
		var messagesPerSeat float64

		// This is the most common case, the $30 per seat plan. We set the price ids from config.
		if team.OrbExternalPlanId == s.orbConfig.ProfessionalPlanID &&
			team.OrbPlanVersionNumber == s.orbConfig.DeveloperPlanForStripeUsers.VersionNumber {
			seatsPriceId = s.orbConfig.DeveloperPlanForStripeUsers.SeatsPriceID
			messagesPriceID = s.orbConfig.DeveloperPlanForStripeUsers.IncludedMessagesPriceID
			pricePerSeat = s.orbConfig.StripeProfessionalPlanPricePerSeat
			messagesPerSeat = s.orbConfig.DeveloperPlanForStripeUsers.MessagesPerSeat
		} else {
			// for custom plans, we need to check that all the price ids and per seat parameters are filled in
			if team.SeatsPriceId == "" ||
				team.MessagesPriceId == "" ||
				team.PricePerSeat == 0 ||
				team.MessagesPerSeat == 0 {
				return fmt.Errorf("missing required parameters for non-standard plan")
			}
			seatsPriceId = team.SeatsPriceId
			messagesPriceID = team.MessagesPriceId
			pricePerSeat = float64(team.PricePerSeat)
			messagesPerSeat = float64(team.MessagesPerSeat)
		}

		orbSubscription := orb.OrbSubscription{
			CustomerOrbID:     orbCustomerId,
			ExternalPlanID:    team.OrbExternalPlanId,
			PlanVersionNumber: &team.OrbPlanVersionNumber,
			StartDate:         &startDate,
			PriceOverrides: []orb.OrbPriceOverrides{
				{
					PriceID:  seatsPriceId,
					Quantity: float64(team.Seats),
				},
				{
					PriceID:  messagesPriceID,
					Quantity: float64(team.Seats) * float64(messagesPerSeat),
				},
			},
		}

		// For the adjustment to work, must floor the date to day start of CurrentPeriodEnd, see:
		// https://augment-wic8570.slack.com/archives/C08JUPVU6MD/p1744758434443149?thread_ts=1744743858.238459&cid=C08JUPVU6MD
		adjustmentEndDate := team.StripeCurrentPeriodEnd.AsTime().Truncate(24 * time.Hour)

		orbSubscription.Adjustments = []orb.OrbAdjustment{
			{
				PriceIDs: []string{seatsPriceId},
				Amount:   pricePerSeat * float64(team.Seats), // user always have 1 seat
				EndDate:  adjustmentEndDate,
			},
		}

		// Apply free seat adjustment for 6 months for teams that need the "billing admin".
		// Hopefully by then we will properly support "billing admin" that doesn't occupy a paid seat.
		if team.ApplyFreeSeatAdjustment {
			orbSubscription.Adjustments = append(orbSubscription.Adjustments, orb.OrbAdjustment{
				PriceIDs: []string{seatsPriceId},
				Amount:   pricePerSeat,
				EndDate:  adjustmentEndDate.AddDate(0, 6, 0),
			})
		}

		orbSubscriptionId, err = s.orbClient.CreateSubscription(ctx, orbSubscription, &subscriptionIdempotencyKey)
		if err != nil {
			return fmt.Errorf("failed to create Orb subscription: %w", err)
		}
		logger.Info().Str("orb_subscription_id", orbSubscriptionId).Msg("Created Orb subscription")

		// Update admin user with Orb subscription ID
		if _, err := userDAO.TryUpdate(ctx, team.AdminUserId, func(u *auth_entities.User) bool {
			u.OrbSubscriptionId = orbSubscriptionId
			return true
		}, DefaultRetry); err != nil {
			return fmt.Errorf("failed to update admin user with Orb subscription ID: %w", err)
		}
		logger.Info().Str("orb_subscription_id", orbSubscriptionId).Msg("Updated admin user with Orb subscription ID")
	} else {
		logger.Info().Str("orb_subscription_id", orbSubscriptionId).Msg("Admin user already has Orb subscription ID")
	}

	// Step 4.2: Update TenantSubscriptionMapping with Orb subscription ID
	if _, err := tsmDAO.TryUpdate(ctx, tenantId, func(tsm *auth_entities.TenantSubscriptionMapping) bool {
		tsm.OrbSubscriptionId = orbSubscriptionId
		return true
	}, DefaultRetry); err != nil {
		return fmt.Errorf("failed to update TenantSubscriptionMapping with Orb subscription ID: %w", err)
	}
	logger.Info().Str("orb_subscription_id", orbSubscriptionId).Msg("Updated TenantSubscriptionMapping with Orb subscription ID")

	// Step 5: Cancel Stripe subscription
	logger.Info().Str("stripe_subscription_id", team.StripeSubscriptionId).Msg("Canceling Stripe subscription")

	if err := s.stripeClient.CancelSubscription(team.StripeSubscriptionId); err != nil {
		// Handle 404 resource_missing error, normally it means the subscription is already cancelled
		if stripeErr, ok := err.(*stripe.Error); ok {
			if stripeErr.Code != stripe.ErrorCodeResourceMissing {
				return fmt.Errorf("failed to cancel Stripe subscription: %w", err)
			}

			logger.Info().
				Str("subscription_id", team.StripeSubscriptionId).
				Msg("Got resource_missing error from CancelSubscription. The subscription is already cancelled.")
		}
	}

	logger.Info().Str("stripe_subscription_id", team.StripeSubscriptionId).Msg("Successfully canceled Stripe subscription")

	logger.Info().
		Str("stripe_customer_id", adminUser.StripeCustomerId).
		Str("orb_customer_id", orbCustomerId).
		Str("orb_subscription_id", orbSubscriptionId).
		Str("canceled_stripe_subscription_id", team.StripeSubscriptionId).
		Msg("Migration completed successfully")

	return nil
}

func (s *AuthGrpcServer) SuspensionCleanup(ctx context.Context, req *authpb.SuspensionCleanupRequest) (*authpb.SuspensionCleanupResponse, error) {
	log.Info().Msg("SuspensionCleanup request received: " + req.String())

	// Check permissions
	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unknown, "Invalid context")
	}
	if !authClaims.HasScope(tokenexchangeproto.Scope_AUTH_RW) {
		return nil, status.Error(codes.PermissionDenied, "Insufficient permissions")
	}

	iapEmail, ok := authClaims.GetIapEmail()
	if ok {
		s.auditLogger.WriteAuditLog(
			iapEmail,
			authClaims.OpaqueUserIDType,
			"",
			"SuspensionCleanup: "+req.String(),
		)
	}

	// Create a wait group to wait for background processing
	var wg sync.WaitGroup
	wg.Add(1)
	suspensionsRemoved := 0
	suspensionsDeduped := 0
	updatesFailed := 0

	// perform processing in the background.
	go func() {
		defer wg.Done()
		ctx := context.Background()
		userDAO := s.daoFactory.GetUserDAO()

		// Process all users
		err := userDAO.FindAll(ctx, func(user *auth_entities.User) bool {
			if len(user.Suspensions) == 0 {
				return true // no suspensions
			}

			removeSuspension := false
			dedupSuspension := false
			_, err := userDAO.TryUpdate(ctx, user.Id, func(u *auth_entities.User) bool {
				// Remove suspensions of indicated types.
				// Dedup suspensions of indicated types.
				removeSuspension = false
				dedupSuspension = false
				suspensionsModified := false
				newSuspensions := make([]*auth_entities.UserSuspension, 0, len(u.Suspensions))
				seenSuspensionTypes := make(map[auth_entities.UserSuspensionType]bool)
				for _, s := range u.Suspensions {
					if slices.Contains(req.RemoveSuspensionTypes, s.SuspensionType) {
						suspensionsModified = true
						removeSuspension = true
						continue // remove
					}
					if slices.Contains(req.DedupSuspensionTypes, s.SuspensionType) {
						if seenSuspensionTypes[s.SuspensionType] {
							suspensionsModified = true
							dedupSuspension = true
							continue // already seen this type, dedup
						}
						seenSuspensionTypes[s.SuspensionType] = true
					}
					newSuspensions = append(newSuspensions, s)
				}

				u.Suspensions = newSuspensions
				if suspensionsModified {
					log.Info().Str("user_id", u.Id).Msg("SuspensionCleanup: Modified suspensions")
				}
				return suspensionsModified
			}, DefaultRetry)
			if err != nil {
				updatesFailed++
				log.Warn().Err(err).Str("user_id", user.Id).Msg("SuspensionCleanup: Failed to update suspensions")
			}
			if removeSuspension {
				suspensionsRemoved++
			}
			if dedupSuspension {
				suspensionsDeduped++
			}
			return true
		})
		if err != nil {
			log.Error().Err(err).Msg("SuspensionCleanup: Failed to fetch users")
		}
		log.Info().Int("suspensions_removed", suspensionsRemoved).Int("suspensions_deduped", suspensionsDeduped).Msg("SuspensionCleanup: Completed")
	}()

	// Wait for background processing to complete
	wg.Wait()

	return &authpb.SuspensionCleanupResponse{
		SuspensionsRemoved: int32(suspensionsRemoved),
		SuspensionsDeduped: int32(suspensionsDeduped),
		UpdatesFailed:      int32(updatesFailed),
	}, nil
}

func (s *AuthGrpcServer) DeduplicateUsersByEmail(ctx context.Context, req *authpb.DeduplicateUsersByEmailRequest) (*authpb.DeduplicateUsersByEmailResponse, error) {
	requestID := uuid.New().String()
	logger := log.With().Str("requestID", requestID).Logger()

	logger.Info().Bool("make_changes", req.MakeChanges).Msg("DeduplicateUsersByEmail request received")

	err := authCheck(ctx, "", tokenexchangeproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unknown, "Invalid context")
	}
	iapEmail, ok := authClaims.GetIapEmail()
	if ok {
		s.auditLogger.WriteAuditLog(
			iapEmail,
			authClaims.OpaqueUserIDType,
			authClaims.TenantName,
			fmt.Sprintf("DeduplicateUsersByEmail request received"),
		)
	}

	// Create a wait group to wait for background processing
	var wg sync.WaitGroup
	var error error
	wg.Add(1)
	mergedUsers := 0
	failedUsers := 0

	resp := &authpb.DeduplicateUsersByEmailResponse{
		UserMerges: make([]*authpb.DeduplicateUsersByEmailResponse_UserMerge, 0),
	}

	// perform processing in the background.
	go func() {
		defer wg.Done()
		backgroundCtx := context.Background()
		if authClaims, ok := auth.GetAugmentClaims(ctx); ok {
			backgroundCtx = authClaims.NewContext(backgroundCtx)
		}

		userDAO := s.daoFactory.GetUserDAO()

		// Group users by normalized email
		usersByEmail := make(map[string][]*auth_entities.User)

		// First pass: collect all users and group by email
		err := userDAO.FindAll(backgroundCtx, func(user *auth_entities.User) bool {
			if user.Tenants == nil {
				return true // skip users without tenants
			}

			normalizedEmail, err := normalizeEmail(user.Email)
			if err != nil {
				logger.Error().Err(err).Str("email", user.Email).Msg("Failed to normalize email")
				return true
			}
			usersByEmail[normalizedEmail] = append(usersByEmail[normalizedEmail], user)
			return true
		})
		if err != nil {
			logger.Error().Err(err).Msg("Failed to fetch users")
			error = err
			return
		}

		logger.Info().Int("users_grouped", len(usersByEmail)).Msg("Users grouped by email")

		// Second pass: process duplicates
		for email, users := range usersByEmail {
			if len(users) <= 1 {
				continue // no duplicates
			}
			logger.Info().Str("email", email).Int("count", len(users)).Msg("Processing user with duplicate email")

			// Create merge info for processing
			merge, userToTenantMap, err := s.createMergeInfo(backgroundCtx, email, users, logger, false)
			if err != nil {
				failedUsers++
				logger.Error().Err(err).Str("email", email).Msg("Failed to create merge info")
				continue
			}

			// log the merge info
			mergeJson, err := json.Marshal(merge)
			if err != nil {
				logger.Error().Err(err).Str("email", email).Msg("Failed to marshal merge info")
			} else {
				logger.Info().RawJSON("merge", mergeJson).Msg("Merge info")
			}

			if req.MakeChanges {
				// Actually perform the merge
				if err := s.executeMerge(backgroundCtx, merge, userToTenantMap, logger); err != nil {
					failedUsers++
					logger.Error().Err(err).Str("email", email).Msg("Failed to execute merge")
					continue
				}
				logger.Info().Str("email", email).Int("count", len(users)).Msg("Merged duplicate users")
			} else {
				logger.Info().Str("email", email).Int("count", len(users)).Str("primary_user", merge.PrimaryUserId).Msg("[dry-run] Would merge duplicate users")
			}

			resp.UserMerges = append(resp.UserMerges, merge)
			mergedUsers++

			// sleep to avoid rate limit
			time.Sleep(100 * time.Millisecond)
		}
	}()
	wg.Wait()

	if error != nil {
		logger.Error().Err(error).Msg("Error iterating through users")
		return nil, error
	}

	// Add logging for analysis
	logger.Info().
		Int("merged_users", mergedUsers).
		Int("failed_users", failedUsers).
		Int("total_users_affected", func() int {
			total := 0
			for _, merge := range resp.UserMerges {
				total += len(merge.MergedUserIds) + 1 // +1 for primary user
			}
			return total
		}()).
		Int("subscriptions_canceled", func() int {
			count := 0
			for _, merge := range resp.UserMerges {
				count += len(merge.CanceledSubscriptionIds)
			}
			return count
		}()).
		Bool("dry_run", !req.MakeChanges).
		Msg("DeduplicateUsersByEmail analytics summary")

	// Return the response
	return &authpb.DeduplicateUsersByEmailResponse{
		UserMerges: resp.UserMerges,
	}, nil
}

func (s *AuthGrpcServer) DeduplicateUsersByIdpUserId(ctx context.Context, req *authpb.DeduplicateUsersByIdpUserIdRequest) (*authpb.DeduplicateUsersByIdpUserIdResponse, error) {
	requestID := uuid.New().String()
	logger := log.With().Str("requestID", requestID).Logger()

	logger.Info().Bool("make_changes", req.MakeChanges).Msg("DeduplicateUsersByIdpUserId request received")

	err := authCheck(ctx, "", tokenexchangeproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unknown, "Invalid context")
	}
	iapEmail, ok := authClaims.GetIapEmail()
	if ok {
		s.auditLogger.WriteAuditLog(
			iapEmail,
			authClaims.OpaqueUserIDType,
			authClaims.TenantName,
			fmt.Sprintf("DeduplicateUsersByIdpUserId request received"),
		)
	}

	// Create a wait group to wait for background processing
	var wg sync.WaitGroup
	var error error
	wg.Add(1)
	mergedUsers := 0
	failedUsers := make([]string, 0)

	// Using the same response type as the email deduplication since the processing is the same
	userMerges := make([]*authpb.DeduplicateUsersByEmailResponse_UserMerge, 0)

	// perform processing in the background.
	go func() {
		defer wg.Done()
		backgroundCtx := context.Background()
		if authClaims, ok := auth.GetAugmentClaims(ctx); ok {
			backgroundCtx = authClaims.NewContext(backgroundCtx)
		}

		userDAO := s.daoFactory.GetUserDAO()

		// Group users by idp user id
		usersByIdpUserId := make(map[string][]*auth_entities.User)

		// First pass: collect all users and group by idp user id
		err := userDAO.FindAll(backgroundCtx, func(user *auth_entities.User) bool {
			if user.Tenants == nil {
				return true // skip users without tenants
			}
			if len(user.IdpUserIds) == 0 {
				return true // skip users without any idp user ids
			}

			// Add this user to each of their IDP user ID groups
			for _, idpUserId := range user.IdpUserIds {
				if idpUserId != "" {
					usersByIdpUserId[idpUserId] = append(usersByIdpUserId[idpUserId], user)
				}
			}
			return true
		})
		if err != nil {
			logger.Error().Err(err).Msg("Failed to fetch users")
			error = err
			return
		}

		logger.Info().Int("users_grouped", len(usersByIdpUserId)).Msg("Users grouped by idp user id")

		// Find connected components of users
		connectedGroups := s.findConnectedUserGroups(usersByIdpUserId)
		logger.Info().Int("connected_groups", len(connectedGroups)).Msg("Found connected user groups")

		// Process each connected group
		for i, users := range connectedGroups {
			if len(users) <= 1 {
				continue // no duplicates
			}

			// Use a representative IDP user ID for logging (first one from first user)
			var representativeIdpUserId string
			if len(users[0].IdpUserIds) > 0 {
				representativeIdpUserId = users[0].IdpUserIds[0]
			}

			logger.Info().
				Str("representative_idp_user_id", representativeIdpUserId).
				Int("group_index", i).
				Int("count", len(users)).
				Msg("Processing connected user group")

			// Create merge info for processing
			merge, userToTenantMap, err := s.createMergeInfo(backgroundCtx, representativeIdpUserId, users, logger, true)
			if err != nil {
				failedUsers = append(failedUsers, representativeIdpUserId)
				logger.Error().Err(err).Int("group_index", i).Msg("Failed to create merge info")
				continue
			}

			// log the merge info
			mergeJson, err := json.Marshal(merge)
			if err != nil {
				logger.Error().Err(err).Str("idp_user_id", representativeIdpUserId).Msg("Failed to marshal merge info")
			} else {
				logger.Info().RawJSON("merge", mergeJson).Msg("Merge info")
			}

			if req.MakeChanges {
				// Actually perform the merge
				if err := s.executeMerge(backgroundCtx, merge, userToTenantMap, logger); err != nil {
					failedUsers = append(failedUsers, representativeIdpUserId)
					logger.Error().Err(err).Str("idp_user_id", representativeIdpUserId).Msg("Failed to execute merge")
					continue
				}
				logger.Info().Str("idp_user_id", representativeIdpUserId).Int("count", len(users)).Msg("Merged duplicate users")
			} else {
				logger.Info().Str("idp_user_id", representativeIdpUserId).Int("count", len(users)).Str("primary_user", merge.PrimaryUserId).Msg("[dry-run] Would merge duplicate users")
			}

			mergedUsers++
			userMerges = append(userMerges, merge)

			// sleep to avoid rate limit
			time.Sleep(100 * time.Millisecond)
		}
	}()
	wg.Wait()

	if error != nil {
		logger.Error().Err(error).Msg("Error iterating through users")
		return nil, error
	}

	// Add logging for analysis
	logger.Info().
		Int("merged_users", mergedUsers).
		Int("failed_users", len(failedUsers)).
		Int("total_users_affected", func() int {
			total := 0
			for _, merge := range userMerges {
				total += len(merge.MergedUserIds) + 1 // +1 for primary user
			}
			return total
		}()).
		Int("subscriptions_canceled", func() int {
			count := 0
			for _, merge := range userMerges {
				count += len(merge.CanceledSubscriptionIds)
			}
			return count
		}()).
		Bool("dry_run", !req.MakeChanges).
		Msg("DeduplicateUsersByIdpUserId analytics summary")

	return &authpb.DeduplicateUsersByIdpUserIdResponse{
		UserMerges:  userMerges,
		FailedUsers: failedUsers,
	}, nil
}

type userInfo struct {
	user                     *auth_entities.User
	hasActiveOrbSubscription bool
	planPriority             int
	tenant                   *tw_pb.Tenant
}

func (s *AuthGrpcServer) createMergeInfo(ctx context.Context, emailOrIdpUserId string, users []*auth_entities.User, logger zerolog.Logger, mergeWithIdp bool) (*authpb.DeduplicateUsersByEmailResponse_UserMerge, map[string]string, error) {
	var userLogger zerolog.Logger
	if mergeWithIdp {
		userLogger = logger.With().Str("idp_user_id", emailOrIdpUserId).Logger()
	} else {
		userLogger = logger.With().Str("email", emailOrIdpUserId).Logger()
	}

	// Sort the users by ID
	sort.Slice(users, func(i, j int) bool {
		return users[i].Id < users[j].Id
	})

	// The target user we want to use is the one with the lowest id
	targetUser := users[0]

	// Fetch subscription info for all users
	userInfos := make([]*userInfo, 0, len(users))
	hasSubscriptions := false
	for _, user := range users {
		userInfo := &userInfo{
			user:                     user,
			hasActiveOrbSubscription: false,
			planPriority:             0,
		}
		if len(user.Tenants) == 0 {
			userLogger.Warn().Str("user_id", user.Id).Msg("User has no tenant")
		} else {
			tenant, err := s.tenantMap.GetTenantByIdDeletedOk(user.Tenants[0])
			if err != nil {
				userLogger.Error().Err(err).Str("user_id", user.Id).Msg("Failed to fetch tenant")
				return nil, nil, err
			}
			if tenant != nil {
				userInfo.tenant = tenant
			}
		}
		if user.OrbSubscriptionId == "" {
			userLogger.Info().Str("user_id", user.Id).Msg("User has no subscription")
			userInfos = append(userInfos, userInfo)
			continue
		}
		subscription, err := s.orbClient.GetUserSubscription(ctx, user.OrbSubscriptionId, nil)
		if err != nil {
			userLogger.Error().Err(err).Str("user_id", user.Id).Msg("Failed to fetch subscription from Orb")
			return nil, nil, err
		}
		if subscription == nil {
			userLogger.Error().Str("user_id", user.Id).Msg("Failed to fetch subscription")
			return nil, nil, status.Error(codes.Internal, "Failed to fetch subscription")
		}
		userInfo.hasActiveOrbSubscription = subscription != nil && parseOrbStatus(subscription.OrbStatus) == auth_entities.Subscription_ORB_STATUS_ACTIVE
		userInfo.planPriority = s.getPlanPriority(subscription.ExternalPlanID)
		hasSubscriptions = true
		userInfos = append(userInfos, userInfo)

		userLogger.Info().Str("user_id", user.Id).Bool("has_active_subscription", userInfo.hasActiveOrbSubscription).Int("plan_priority", userInfo.planPriority).Msgf("Fetched subscription info with subscription id %s customer id %s", user.OrbSubscriptionId, user.OrbCustomerId)
	}

	var userWithBestSubscription *auth_entities.User
	var err error
	if !hasSubscriptions {
		// No subscriptions - return the first user
		userWithBestSubscription = targetUser
	} else {
		userWithBestSubscription, err = s.selectUserWithBestSubscription(ctx, userInfos, userLogger)
		if err != nil {
			return nil, nil, err
		}
	}

	if targetUser.Id != userWithBestSubscription.Id {
		userLogger.Info().Str("primary_user_id", targetUser.Id).Str("subscription_source_user_id", userWithBestSubscription.Id).Msg("Subscription info will be moved to primary user")
	}

	merge := &authpb.DeduplicateUsersByEmailResponse_UserMerge{
		PrimaryUserId:              targetUser.Id,
		MergedUserIds:              make([]string, 0),
		PrimaryUserTenant:          make(map[string]string),
		MergedUserTenants:          make(map[string]string),
		SubscriptionSourceUserId:   userWithBestSubscription.Id,
		PreservedOrbCustomerId:     userWithBestSubscription.OrbCustomerId,
		PreservedOrbSubscriptionId: userWithBestSubscription.OrbSubscriptionId,
		PreservedStripeCustomerId:  userWithBestSubscription.StripeCustomerId,
		MergedUserSubscriptionIds:  make([]string, 0),
		CanceledSubscriptionIds:    make([]string, 0),
		IdpUserIds:                 make([]string, 0),
	}

	if !mergeWithIdp {
		merge.Email = emailOrIdpUserId
	}

	// Store user ID to tenant ID mapping for efficient lookup
	userToTenantMap := make(map[string]string)

	// Store IDP user IDs to avoid duplicates
	idpUserIdsSet := make(map[string]bool)

	// Collect info from users to be merged
	for _, userInfo := range userInfos {
		userLogger.Info().Str("user_id", userInfo.user.Id).Msg("Collecting info from user to be merged")

		if userInfo.user.Id != targetUser.Id {
			merge.MergedUserIds = append(merge.MergedUserIds, userInfo.user.Id)
			userLogger.Info().Str("user_id", userInfo.user.Id).Msg("Added user to merged users")
		}

		// Collect IDP user IDs - avoid duplicates
		for _, idpUserId := range userInfo.user.IdpUserIds {
			if _, ok := idpUserIdsSet[idpUserId]; !ok {
				idpUserIdsSet[idpUserId] = true
				merge.IdpUserIds = append(merge.IdpUserIds, idpUserId)
			}
		}

		// Collect tenant info
		if len(userInfo.user.Tenants) > 1 {
			userLogger.Error().Str("user_id", userInfo.user.Id).Int("tenant_count", len(userInfo.user.Tenants)).Msg("User is in multiple tenants")
			return nil, nil, status.Error(codes.Internal, "User is in multiple tenants")
		}
		// Store the mapping
		userToTenantMap[userInfo.user.Id] = userInfo.user.Tenants[0]

		tenant, err := s.getTenant(userInfo.user.Tenants[0])
		if err != nil {
			return nil, nil, err
		}

		if userInfo.user.Id == userWithBestSubscription.Id {
			merge.PrimaryUserTenant[userInfo.user.Tenants[0]] = tenant.Name
		} else {
			merge.MergedUserTenants[userInfo.user.Tenants[0]] = tenant.Name

			if userInfo.user.OrbSubscriptionId != userWithBestSubscription.OrbSubscriptionId {
				// Collect subscription info
				if userInfo.user.OrbSubscriptionId != "" {
					merge.MergedUserSubscriptionIds = append(merge.MergedUserSubscriptionIds, userInfo.user.OrbSubscriptionId)
				}

				if userInfo.hasActiveOrbSubscription {
					merge.CanceledSubscriptionIds = append(merge.CanceledSubscriptionIds, userInfo.user.OrbSubscriptionId)
					userLogger.Info().Str("user_id", userInfo.user.Id).Str("subscription_id", userInfo.user.OrbSubscriptionId).Msg("Added subscription to be canceled")
				}
			}
		}
	}

	return merge, userToTenantMap, nil
}

func (s *AuthGrpcServer) selectUserWithBestSubscription(ctx context.Context, userInfos []*userInfo, logger zerolog.Logger) (*auth_entities.User, error) {
	logger.Info().Int("user_count", len(userInfos)).Msg("Selecting user with best subscription")

	// Sort by priority: non-nil tenant > tenant tier > active status > plan type > payment method > user id
	sort.Slice(userInfos, func(i, j int) bool {
		a, b := userInfos[i], userInfos[j]

		// First priority: non-nil tenant beats nil tenant
		if (a.tenant == nil) != (b.tenant == nil) {
			return a.tenant != nil
		}

		// Second priority: tenant tier (enterprise > self-serve > other)
		aIsEnterprise := a.tenant != nil && tenantutil.IsEnterpriseTenant(a.tenant)
		bIsEnterprise := b.tenant != nil && tenantutil.IsEnterpriseTenant(b.tenant)
		if aIsEnterprise != bIsEnterprise {
			return aIsEnterprise
		}

		aIsSelfServeTeam := a.tenant != nil && tenantutil.IsSelfServeTeamTenant(a.tenant)
		bIsSelfServeTeam := b.tenant != nil && tenantutil.IsSelfServeTeamTenant(b.tenant)
		if aIsSelfServeTeam != bIsSelfServeTeam {
			return aIsSelfServeTeam
		}

		// Third priority: active status (active beats inactive)
		if a.hasActiveOrbSubscription != b.hasActiveOrbSubscription {
			return a.hasActiveOrbSubscription
		}

		// Fourth priority: plan type (developer > community > trial)
		if a.planPriority != b.planPriority {
			return a.planPriority > b.planPriority
		}

		// Tiebreaker: prefer user with lower id
		return a.user.Id < b.user.Id
	})

	return userInfos[0].user, nil
}

func (s *AuthGrpcServer) getPlanPriority(externalPlanID string) int {
	// Priority: developer > community > trial
	switch externalPlanID {
	case "orb_developer_plan": // developer plan
		return 4
	case s.orbConfig.getCommunityPlan().ID:
		return 3
	case s.orbConfig.getTrialPlan().ID:
		return 2
	default:
		return 1
	}
}

func (s *AuthGrpcServer) executeMerge(ctx context.Context, merge *authpb.DeduplicateUsersByEmailResponse_UserMerge, userToTenantMap map[string]string, logger zerolog.Logger) error {
	logger.Info().Str("primary_user_id", merge.PrimaryUserId).Msg("Executing user merge")

	userDAO := s.daoFactory.GetUserDAO()

	// Set new primary user info
	_, err := userDAO.TryUpdate(ctx, merge.PrimaryUserId, func(u *auth_entities.User) bool {
		if merge.Email != "" {
			u.Email = merge.Email
		}
		u.StripeCustomerId = merge.PreservedStripeCustomerId
		u.OrbCustomerId = merge.PreservedOrbCustomerId
		u.OrbSubscriptionId = merge.PreservedOrbSubscriptionId
		u.IdpUserIds = merge.IdpUserIds
		return true
	}, DefaultRetry)
	if err != nil {
		logger.Error().Err(err).Str("user_id", merge.PrimaryUserId).Msg("Failed to update primary user info")
		return err
	}

	logger.Info().Str("user_id", merge.PrimaryUserId).Msgf("Updated primary user info to preserve subscription id %s customer id %s", merge.PreservedOrbSubscriptionId, merge.PreservedOrbCustomerId)

	if len(merge.PrimaryUserTenant) != 1 {
		return fmt.Errorf("Failed to find primary tenant for user %s", merge.PrimaryUserId)
	}

	// Get the primary tenant ID
	var primaryTenantId string
	for tenantId := range merge.PrimaryUserTenant {
		primaryTenantId = tenantId
		break
	}

	// Move user to new primary tenant
	if userToTenantMap[merge.PrimaryUserId] != primaryTenantId {
		logger.Info().Str("user_id", merge.PrimaryUserId).Str("old_tenant", userToTenantMap[merge.PrimaryUserId]).Str("new_tenant", primaryTenantId).Msg("Moving user to new primary tenant")
		if err := s.tenantMap.MoveUserToTenant(ctx, merge.PrimaryUserId, primaryTenantId); err != nil {
			logger.Error().Err(err).Str("user_id", merge.PrimaryUserId).Msg("Failed to move user to new primary tenant")
			return err
		}
	}

	// Map all existing IDP mappings to the primary user
	mappingsUpdated := 0
	idpUserMappingDAO := s.daoFactory.GetIDPUserMappingDAO()
	err = idpUserMappingDAO.FindAll(ctx, func(mapping *auth_entities.IdpUserMapping) bool {
		if slices.Contains(merge.MergedUserIds, mapping.AugmentUserId) {
			_, err := idpUserMappingDAO.TryUpdate(ctx, mapping.IdpUserId, func(m *auth_entities.IdpUserMapping) bool {
				m.AugmentUserId = merge.PrimaryUserId
				return true
			}, DefaultRetry)
			if err != nil {
				logger.Error().Err(err).Str("user_id", mapping.AugmentUserId).Msg("Failed to update IDP mapping")
				return false
			}
			mappingsUpdated++
		}
		return true
	})
	if err != nil {
		logger.Error().Err(err).Msg("Failed to iterate through IDP mappings")
		return err
	}
	logger.Info().Int("mappings_updated", mappingsUpdated).Msg("Updated IDP mappings")

	// Update the subscription owner
	subscriptionDAO := s.daoFactory.GetSubscriptionDAO()
	_, err = subscriptionDAO.TryUpdate(ctx, merge.PreservedOrbSubscriptionId, func(s *auth_entities.Subscription) bool {
		s.Owner = &auth_entities.Subscription_UserId{UserId: merge.PrimaryUserId}
		return true
	}, DefaultRetry)
	if err != nil {
		logger.Error().Err(err).Str("subscription_id", merge.PreservedOrbSubscriptionId).Msg("Failed to update subscription owner")
		return err
	}

	// Set the orb metadata to the primary user
	if merge.PreservedOrbCustomerId != "" {
		logger.Info().Str("customer_id", merge.PreservedOrbCustomerId).Str("user_id", merge.PrimaryUserId).Msg("Updating Orb customer metadata")
		if err := s.orbClient.UpdateCustomerMetadata(ctx, merge.PreservedOrbCustomerId, map[string]string{
			"augment_user_id": merge.PrimaryUserId,
		}); err != nil {
			logger.Error().Err(err).Str("customer_id", merge.PreservedOrbCustomerId).Str("user_id", merge.PrimaryUserId).Msg("Failed to update Orb customer metadata")
			return err
		}
	}

	// Delete the other users
	for _, userId := range merge.MergedUserIds {
		tenantId, ok := userToTenantMap[userId]
		if !ok {
			return fmt.Errorf("Failed to find tenant for user %s", userId)
		}
		logger.Info().Str("user_id", userId).Str("tenant_id", tenantId).Msg("Deleting merged user")
		_, err := s.removeUserFromTenant(ctx, &authpb.RemoveUserFromTenantRequest{
			UserId:   userId,
			TenantId: tenantId,
		}, nil)
		if err != nil {
			logger.Error().Err(err).Str("user_id", userId).Str("tenant_id", tenantId).Msg("Failed to delete merged user")
			return err
		}
	}

	// Cancel any remaining subscriptions
	for _, orbSubscriptionId := range merge.CanceledSubscriptionIds {
		logger.Info().Str("subscription_id", orbSubscriptionId).Msg("Cancelling subscription")
		err := SafeCancelOrbSubscription(ctx, s.orbClient, userDAO, merge.PrimaryUserId, orbSubscriptionId, orb.PlanChangeImmediate, nil, nil, true)
		if err != nil {
			logger.Error().Err(err).Str("subscription_id", orbSubscriptionId).Msg("Failed to cancel subscription")
			return err
		}
	}

	return nil
}

// find connected user groups by IDP user IDs
// ex. If user A has IDP user IDs [1, 2, 3] and user B has IDP user IDs [2, 3, 4], then users A and B are in the same group
func (s *AuthGrpcServer) findConnectedUserGroups(usersByIdpUserId map[string][]*auth_entities.User) [][]*auth_entities.User {
	userToGroup := make(map[string]int) // user ID -> group ID
	groups := make([][]*auth_entities.User, 0)

	for _, users := range usersByIdpUserId {
		if len(users) <= 1 {
			continue
		}

		// Find all existing groups that any of these users belong to
		existingGroups := make(map[int]bool) // group ID -> bool
		var usersNotInGroups []*auth_entities.User

		for _, user := range users {
			if groupId, exists := userToGroup[user.Id]; exists {
				existingGroups[groupId] = true
			} else {
				usersNotInGroups = append(usersNotInGroups, user)
			}
		}

		if len(existingGroups) == 0 {
			// Create new group with all users
			groupId := len(groups)
			groups = append(groups, make([]*auth_entities.User, 0))
			for _, user := range users {
				groups[groupId] = append(groups[groupId], user)
				userToGroup[user.Id] = groupId
			}
		} else if len(existingGroups) == 1 {
			// Add new users to existing group
			var targetGroup int
			for groupId := range existingGroups {
				targetGroup = groupId
				break
			}
			for _, user := range usersNotInGroups {
				groups[targetGroup] = append(groups[targetGroup], user)
				userToGroup[user.Id] = targetGroup
			}
		} else {
			// Merge multiple existing groups + new users
			var targetGroup int
			var groupsToMerge []int

			for groupId := range existingGroups {
				if targetGroup == 0 {
					targetGroup = groupId
				} else {
					groupsToMerge = append(groupsToMerge, groupId)
				}
			}

			// Merge other groups into target group
			for _, groupId := range groupsToMerge {
				for _, user := range groups[groupId] {
					groups[targetGroup] = append(groups[targetGroup], user)
					userToGroup[user.Id] = targetGroup
				}
				groups[groupId] = nil // Mark as merged
			}

			// Add new users to target group
			for _, user := range usersNotInGroups {
				groups[targetGroup] = append(groups[targetGroup], user)
				userToGroup[user.Id] = targetGroup
			}
		}
	}

	// Filter out nil groups and remove duplicates
	var result [][]*auth_entities.User
	for _, group := range groups {
		if group != nil && len(group) > 1 {
			// Remove duplicates within group
			seen := make(map[string]bool)
			var uniqueUsers []*auth_entities.User
			for _, user := range group {
				if !seen[user.Id] {
					seen[user.Id] = true
					uniqueUsers = append(uniqueUsers, user)
				}
			}
			if len(uniqueUsers) > 1 {
				result = append(result, uniqueUsers)
			}
		}
	}

	return result
}

func (s *AuthGrpcServer) DeduplicateUserTenantList(ctx context.Context, req *authpb.DeduplicateUserTenantListRequest) (*authpb.DeduplicateUserTenantListResponse, error) {
	err := authCheck(ctx, "", tokenexchangeproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, req.UserId)
	if err != nil {
		return nil, err
	}

	currentTenants := user.Tenants
	newTenants := make([]string, 0, len(currentTenants))
	for _, tenant := range currentTenants {
		if !slices.Contains(newTenants, tenant) {
			newTenants = append(newTenants, tenant)
		}
	}

	if len(newTenants) > 1 {
		return nil, status.Error(codes.FailedPrecondition, "User has multiple tenants after deduplication")
	}

	if len(newTenants) != len(currentTenants) {
		if req.MakeChanges {
			_, err = userDAO.TryUpdate(ctx, req.UserId, func(u *auth_entities.User) bool {
				u.Tenants = newTenants
				return true
			}, DefaultRetry)
			if err != nil {
				return nil, err
			}
		} else {
			log.Info().Str("user_id", req.UserId).Msg("Would have made changes to user's tenants list")
		}
	}

	var newTenant string
	if len(newTenants) > 0 {
		newTenant = newTenants[0]
	}

	return &authpb.DeduplicateUserTenantListResponse{
		PreviousTenants: currentTenants,
		NewTenant:       newTenant,
	}, nil
}

func (s *AuthGrpcServer) SyncAddresses(ctx context.Context, req *authpb.SyncAddressesRequest) (*authpb.SyncAddressesResponse, error) {
	requestID := uuid.New().String()
	logger := log.With().Str("requestID", requestID).Logger()

	logger.Info().Bool("make_changes", req.MakeChanges).Msg("SyncAddresses request received")

	authErr := authCheck(ctx, "", tokenexchangeproto.Scope_AUTH_RW)
	if authErr != nil {
		return nil, authErr
	}

	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unknown, "Invalid context")
	}
	iapEmail, ok := authClaims.GetIapEmail()
	if ok {
		s.auditLogger.WriteAuditLog(
			iapEmail,
			authClaims.OpaqueUserIDType,
			authClaims.TenantName,
			fmt.Sprintf("SyncAddresses request received"),
		)
	}

	successfulUserIds := make([]string, 0)
	failedUserIds := make([]string, 0)
	notAttemptedUserIds := make([]string, 0)
	multiplePaymentMethodUserIds := make([]string, 0)

	// Create a wait group to wait for background processing
	var wg sync.WaitGroup
	var err error
	wg.Add(1)
	go func() {
		defer wg.Done()
		backgroundCtx := context.Background()
		if authClaims, ok := auth.GetAugmentClaims(ctx); ok {
			backgroundCtx = authClaims.NewContext(backgroundCtx)
		}

		userDAO := s.daoFactory.GetUserDAO()
		err = userDAO.FindAll(backgroundCtx, func(user *auth_entities.User) bool {
			time.Sleep(time.Millisecond * 20) // Avoid rate limits

			if len(req.IdsToSync) > 0 && !slices.Contains(req.IdsToSync, user.Id) {
				// Not one of the requested users, if we request certain users
				return true
			}

			if user.OrbCustomerId == "" || user.StripeCustomerId == "" {
				logger.Info().Str("user_id", user.Id).Msg("User does not have an Orb customer or Stripe customer, skipping")
				notAttemptedUserIds = append(notAttemptedUserIds, user.Id)
				return true
			}

			// Get the customer's default payment method from Stripe
			paymentMethods, err := s.stripeClient.GetPaymentMethodsForCustomer(user.StripeCustomerId)
			if err != nil {
				logger.Error().Err(err).Str("user_id", user.Id).Msg("Failed to get default payment method from Stripe")
				failedUserIds = append(failedUserIds, user.Id)
				return true
			}
			if len(paymentMethods) == 0 {
				logger.Info().Str("user_id", user.Id).Msg("User has no payment methods in Stripe, skipping")
				notAttemptedUserIds = append(notAttemptedUserIds, user.Id)
				return true
			}
			if len(paymentMethods) > 1 {
				logger.Info().Str("user_id", user.Id).Msg("User has more than one payment method in Stripe, skipping")
				multiplePaymentMethodUserIds = append(multiplePaymentMethodUserIds, user.Id)
				return true
			}
			paymentMethod := paymentMethods[0]
			if paymentMethod == nil || paymentMethod.BillingDetails == nil {
				logger.Error().Str("user_id", user.Id).Msg("User's payment method in Stripe is invalid")
				failedUserIds = append(failedUserIds, user.Id)
				return true
			}
			address := paymentMethod.BillingDetails.Address
			if address == nil {
				logger.Error().Str("user_id", user.Id).Msg("User's payment method in Stripe has no address")
				failedUserIds = append(failedUserIds, user.Id)
				return true
			}
			if req.MakeChanges {
				// Update the customer's address in Orb
				err = s.orbClient.UpdateCustomerBillingAddress(backgroundCtx, user.OrbCustomerId, &orb.Address{
					Line1:      address.Line1,
					Line2:      address.Line2,
					City:       address.City,
					State:      address.State,
					PostalCode: address.PostalCode,
					Country:    address.Country,
				})
				if err != nil {
					logger.Error().Err(err).Str("user_id", user.Id).Msg("Failed to update customer's address in Orb")
					failedUserIds = append(failedUserIds, user.Id)
					return true
				}
				successfulUserIds = append(successfulUserIds, user.Id)
				logger.Info().Str("user_id", user.Id).Msg("Updated customer's address in Orb")
				return true
			} else {
				logger.Info().Str("user_id", user.Id).Msg("Would update customer's address in Orb")
				successfulUserIds = append(successfulUserIds, user.Id)
				return true
			}
		})
	}()
	wg.Wait()

	if err != nil {
		logger.Error().Err(err).Msg("Error iterating through users")
		return nil, err
	}

	logger.Info().Int("num_users_updated", len(successfulUserIds)).Int("num_users_failed", len(failedUserIds)).Int("num_users_not_attempted", len(notAttemptedUserIds)).Int("num_users_multiple_payment_methods", len(multiplePaymentMethodUserIds)).Msg("SyncAddresses request completed")
	logger.Info().Interface("users_updated", successfulUserIds).Interface("users_failed", failedUserIds).Interface("users_not_attempted", notAttemptedUserIds).Interface("users_multiple_payment_methods", multiplePaymentMethodUserIds).Msg("SyncAddresses request results")

	return &authpb.SyncAddressesResponse{
		UsersUpdated:                successfulUserIds,
		UsersFailed:                 failedUserIds,
		UsersNotAttempted:           notAttemptedUserIds,
		UsersMultiplePaymentMethods: multiplePaymentMethodUserIds,
	}, nil
}

func (s *AuthGrpcServer) MigratePopulateIDPUserMappings(ctx context.Context, req *authpb.MigratePopulateIDPUserMappingsRequest) (*authpb.MigratePopulateIDPUserMappingsResponse, error) {
	requestID := uuid.New().String()
	logger := log.With().Str("requestID", requestID).Logger()

	logger.Info().Msg("MigratePopulateIDPUserMappings request received")

	err := authCheck(ctx, "", tokenexchangeproto.Scope_AUTH_RW)
	if err != nil {
		return nil, err
	}

	authInfo, _ := auth.GetAugmentClaims(ctx)
	iapEmail, ok := authInfo.GetIapEmail()
	if ok {
		s.auditLogger.WriteAuditLog(
			iapEmail,
			authInfo.OpaqueUserIDType,
			"",
			"Migrate populate IDP user mappings",
		)
	}

	userDAO := s.daoFactory.GetUserDAO()
	idpUserMappingDAO := s.daoFactory.GetIDPUserMappingDAO()

	idpUserIDToUserId := make(map[string]string)
	err = idpUserMappingDAO.FindAll(ctx, func(mapping *auth_entities.IdpUserMapping) bool {
		idpUserIDToUserId[mapping.IdpUserId] = mapping.AugmentUserId
		return true
	})
	if err != nil {
		logger.Error().Err(err).Msg("Failed to fetch IDP user mappings")
		return nil, err
	}

	var wg sync.WaitGroup
	wg.Add(1)
	var error error

	go func() {
		defer wg.Done()

		ctx := context.Background()

		usersMigrated := 0
		usersFailed := 0
		migrateUser := func(user *auth_entities.User) bool {
			for _, idpUserID := range user.IdpUserIds {
				if _, ok := idpUserIDToUserId[idpUserID]; ok {
					continue // already exists
				}

				mapping, err := idpUserMappingDAO.Get(ctx, idpUserID)
				if err != nil {
					usersFailed++
					logger.Error().Err(err).Str("user_id", user.Id).Str("idp_user_id", idpUserID).Msg("MigratePopulateIDPUserMappings: Failed to get IDP user mapping")
					continue
				}
				if mapping != nil {
					continue // already exists
				}
				// Create the mapping
				mapping = &auth_entities.IdpUserMapping{
					IdpUserId:     idpUserID,
					AugmentUserId: user.Id,
				}
				_, err = idpUserMappingDAO.Create(ctx, mapping)
				if err != nil {
					usersFailed++
					logger.Warn().Err(err).Str("user_id", user.Id).Str("idp_user_id", idpUserID).Msg("MigratePopulateIDPUserMappings: Failed to create IDP user mapping")
					continue
				}
				usersMigrated++
			}
			return true
		}

		// Process all users
		err = userDAO.FindAll(ctx, migrateUser)
		if err != nil {
			logger.Error().Err(err).Msg("MigratePopulateIDPUserMappings: Failed to fetch users")
			error = err
			return
		}

		logger.Info().Int("users_migrated", usersMigrated).Int("users_failed", usersFailed).Msg("MigratePopulateIDPUserMappings: Migration complete")
	}()

	wg.Wait()

	if error != nil {
		return nil, error
	}

	return &authpb.MigratePopulateIDPUserMappingsResponse{}, nil
}
