package main

import (
	"context"
	"fmt"
	"slices"
	"strings"
	"testing"
	"time"

	featureflag "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging/audit"
	authpb "github.com/augmentcode/augment/services/auth/central/server/auth"
	pb "github.com/augmentcode/augment/services/auth/central/server/auth"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/augmentcode/augment/services/integrations/orb"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	tw_client "github.com/augmentcode/augment/services/tenant_watcher/client"
	tw_pb "github.com/augmentcode/augment/services/tenant_watcher/proto"
	tokenexchangeproto "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"github.com/stripe/stripe-go/v80"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	instanceID            = "test-instance"
	tableName             = "test-table"
	projectID             = "test-project"
	tenantName            = "individual"
	tenantID              = "individual-2"
	professionalTenantID  = "professional-tenant-id"
	professionalTenantID2 = "professional-tenant-id2"
	deletedTenantID       = "deleted-tenant-id"
	tenantNamespace       = "individual"
)

func createRequestContext(tenantID string, scopes []string) context.Context {
	claims := &auth.AugmentClaims{
		TenantID:       tenantID,
		TenantName:     tenantName,
		ShardNamespace: tenantNamespace,
		Cloud:          "test-cloud",
		Scope:          scopes,
	}
	return claims.NewContext(context.Background())
}

// TODO(jacqueline): We should really test auth for every endpoint, but the way the tests are set up
// right now makes that difficult. I'm leaving that as a task for after refactoring the tests.
func TestAuthCheck(t *testing.T) {
	// Missing claims should fail.
	err := authCheck(context.Background(), tenantID, tokenexchangeproto.Scope_AUTH_R)
	require.Error(t, err)
	require.Equal(t, codes.Unknown, status.Code(err))

	// Mismatch in tenant ID should fail.
	ctx := createAuthorizedRequestContext()
	err = authCheck(ctx, "different-tenant-id", tokenexchangeproto.Scope_AUTH_R)
	require.Error(t, err)
	require.Equal(t, codes.PermissionDenied, status.Code(err))

	// Empty tenant ID should fail.
	err = authCheck(ctx, "", tokenexchangeproto.Scope_AUTH_R)
	require.Error(t, err)
	require.Equal(t, codes.PermissionDenied, status.Code(err))

	// Missing scope should fail.
	ctx = createRequestContext(tenantID, []string{tokenexchangeproto.Scope_AUTH_R.String()})
	err = authCheck(ctx, tenantID, tokenexchangeproto.Scope_AUTH_RW)
	require.Error(t, err)
	require.Equal(t, codes.PermissionDenied, status.Code(err))

	// Success case.
	err = authCheck(ctx, tenantID, tokenexchangeproto.Scope_AUTH_R)
	require.NoError(t, err)

	// Success case with wildcard tenant ID.
	ctx = createCentralAuthorizedRequestContext()
	err = authCheck(ctx, "different-tenant-id", tokenexchangeproto.Scope_AUTH_R)
	require.NoError(t, err)

	// Success case with wildcard tenant ID and no tenant ID in request.
	err = authCheck(ctx, "", tokenexchangeproto.Scope_AUTH_R)
	require.NoError(t, err)
}

func getUserCount(t *testing.T, ctx context.Context, dao *UserDAO) int {
	count := 0
	err := dao.FindAll(ctx, func(_ *auth_entities.User) bool {
		count++
		return true
	})
	require.NoError(t, err)
	return count
}

type authServicerSUT struct {
	authServicer *AuthGrpcServer
	daoFactory   *DAOFactory
}

func createAuthServicerSUT(t *testing.T, bigtableFixture *BigtableFixture) *authServicerSUT {
	mockClient := &tw_client.MockTenantWatcherClient{
		Tenants: []*tw_pb.Tenant{
			{
				Id:             tenantID,
				Name:           tenantName,
				ShardNamespace: tenantNamespace,
				Cloud:          "test-cloud",
				AuthConfiguration: &tw_pb.AuthConfiguration{
					Domain: "test-tenant1.com",
				},
				Tier: tw_pb.TenantTier_ENTERPRISE,
			},
			{
				Id:             professionalTenantID,
				Name:           "professional-tenant",
				ShardNamespace: "professional-namespace",
				Cloud:          "test-cloud",
				Tier:           tw_pb.TenantTier_PROFESSIONAL,
			},
			{
				Id:             professionalTenantID2,
				Name:           "professional-tenant2",
				ShardNamespace: "professional-namespace",
				Cloud:          "test-cloud",
				Tier:           tw_pb.TenantTier_PROFESSIONAL,
			},
			{
				Id:             deletedTenantID,
				Name:           "deleted-tenant",
				ShardNamespace: "deleted-namespace",
				Cloud:          "test-cloud",
				Tier:           tw_pb.TenantTier_PROFESSIONAL,
				DeletedAt:      "2023-01-01T00:00:00Z",
			},
		},
	}

	daoFactory := NewDAOFactory(bigtableFixture.Table)
	ffHandler := featureflag.NewLocalFeatureFlagHandler()

	ffHandler.Set("check_subscription_status", true)
	ffHandler.Set("enforce_usage_credits", true)

	mockAsyncOpsPublisher := NewMockAsyncOpsPublisher()
	auditLogger := audit.NewDefaultAuditLogger()

	authServicer := NewAuthGrpcServer(
		ffHandler,
		daoFactory,
		NewTenantMap(
			daoFactory,
			mockClient,
			"us-central.api.augmentcode.com",
			ffHandler,
			NewMockAsyncOpsPublisher(),
			auditLogger,
		),
		auditLogger,
		ripublisher.NewRequestInsightPublisherMock(),
		mockAsyncOpsPublisher,
		&StripeConfig{Enabled: false},
		&OrbConfig{
			Enabled:            true,
			ProfessionalPlanID: "orb_developer_plan",
			DeveloperPlanForStripeUsers: struct {
				VersionNumber           int64   `json:"version_number"`
				SeatsPriceID            string  `json:"seats_price_id"`
				IncludedMessagesPriceID string  `json:"included_messages_price_id"`
				MessagesPerSeat         float64 `json:"messages_per_seat"`
			}{
				VersionNumber:           3,
				SeatsPriceID:            "test_seats_price_id",
				IncludedMessagesPriceID: "test_messages_price_id",
				MessagesPerSeat:         1000.0,
			},
			StripeProfessionalPlanPricePerSeat: 30.0,
			Plans: []PlanConfig{
				{
					ID: "orb_community_plan",
					Features: PlanFeatures{
						PlanType: PlanTypeCommunity,
					},
				},
				{
					ID: "orb_trial_plan",
					Features: PlanFeatures{
						PlanType: PlanTypePaidTrial,
					},
				},
				{
					ID: "orb_developer_plan",
					Features: PlanFeatures{
						PlanType: PlanTypePaid,
					},
				},
			},
		},
		NewMockStripeClient(),
	)

	return &authServicerSUT{
		authServicer: authServicer,
		daoFactory:   daoFactory,
	}
}

type createUserOpts struct {
	email     string
	id        string
	idpUserID string
}

func (s *authServicerSUT) createUser(t *testing.T, opts createUserOpts) *auth_entities.User {
	// Should we be using AddUserToTenant ?
	id := uuid.New().String()
	if opts.id != "" {
		id = opts.id
	}
	email := fmt.Sprintf("<EMAIL>", id)
	if opts.email != "" {
		email = opts.email
	}

	idpUserIds := []string{}
	if opts.idpUserID != "" {
		idpUserIds = append(idpUserIds, opts.idpUserID)
	}

	user := &auth_entities.User{
		Id:         id,
		Email:      email,
		Tenants:    []string{tenantID},
		IdpUserIds: idpUserIds,
	}
	created, err := s.daoFactory.GetUserDAO().Create(context.Background(), user)
	require.NoError(t, err)
	return created
}

// Create, Add, List, Remove, List
func caseBasicFlow(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer

	ctx := createAuthorizedRequestContext()

	userEmail := fmt.Sprintf("<EMAIL>", uuid.New().String())
	request := &pb.AddUserToTenantRequest{
		Email:    userEmail,
		TenantId: tenantID,
	}

	// Test: Add user
	addUserResponse, err := authServicer.AddUserToTenant(ctx, request)
	require.NoError(t, err)
	assert.Equal(t, userEmail, addUserResponse.User.Email)

	// Test: Add same user again (should not create duplicate)
	addUserResponse2, err := authServicer.AddUserToTenant(ctx, request)
	require.NoError(t, err)
	assert.Equal(t, addUserResponse.User.Id, addUserResponse2.User.Id)

	// Test: List tenant users
	listRequest := &pb.ListTenantUsersRequest{TenantId: tenantID}
	tenantUsers, err := authServicer.ListTenantUsers(ctx, listRequest)
	require.NoError(t, err)
	assert.Equal(t, 1, len(tenantUsers.Users))
	assert.Equal(t, userEmail, tenantUsers.Users[0].Email)

	// Test: Remove user from tenant
	removeRequest := &pb.RemoveUserFromTenantRequest{
		UserId:   addUserResponse.User.Id,
		TenantId: tenantID,
	}
	_, err = authServicer.RemoveUserFromTenant(ctx, removeRequest)
	require.NoError(t, err)

	// Test: List users after removal
	tenantUsers, err = authServicer.ListTenantUsers(ctx, listRequest)
	require.NoError(t, err)
	assert.Equal(t, 0, len(tenantUsers.Users))
}

func caseTokenLookup(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer

	tokenHashDAO := sut.daoFactory.GetTokenHashDAO()
	ctx := createAuthorizedRequestContext()

	// Populate with a sample entry
	// sha256 of "hello"
	tokenHash := &auth_entities.TokenHash{
		Hash:          "2cf24dba5fb0a30e26e83b2ac5b9e29e1b161e5c1fa7425e73043362938b9824",
		AugmentUserId: "test-user-id",
		EmailAddress:  "<EMAIL>",
		TenantId:      tenantID,
	}
	_, err := tokenHashDAO.Create(ctx, tokenHash)
	require.NoError(t, err)

	// Verify the token hash exists
	stored, err := tokenHashDAO.Get(ctx, tokenHash.Hash)
	require.NoError(t, err)
	require.Equal(t, tokenHash.AugmentUserId, stored.AugmentUserId)

	// Create user
	userDao := sut.daoFactory.GetUserDAO()
	user := &auth_entities.User{
		Id:            "test-user-id",
		Email:         "<EMAIL>",
		Tenants:       []string{tenantID},
		OrbCustomerId: "orb-customer-123",
	}
	_, err = userDao.Create(ctx, user)
	require.NoError(t, err)

	// Create context with required metadata for gRPC call

	// Token matching the entry should succeed
	request := &pb.GetTokenInfoRequest{Token: "hello"}
	response, err := authServicer.GetTokenInfo(ctx, request)
	require.NoError(t, err)
	assert.Equal(t, "<EMAIL>", response.UserId)
	assert.Equal(t, "test-user-id", response.AugmentUserId)
	assert.Equal(t, "<EMAIL>", response.UserEmail)
	assert.Equal(t, tenantID, response.TenantId)
	assert.Equal(t, tenantName, response.TenantName)
	// Check enterprise subscription
	enterpriseSub, ok := response.Subscription.(*pb.GetTokenInfoResponse_Enterprise)
	require.True(t, ok)
	require.NotNil(t, enterpriseSub.Enterprise)

	// Nonexistent token should fail
	request = &pb.GetTokenInfoRequest{Token: "world"}
	_, err = authServicer.GetTokenInfo(ctx, request)
	require.Error(t, err)
	require.Equal(t, codes.NotFound, status.Code(err))
}

func TestUpdateUser(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	mockClient := &tw_client.MockTenantWatcherClient{
		Tenants: []*tw_pb.Tenant{
			{
				Id:             tenantID,
				Name:           tenantName,
				ShardNamespace: tenantNamespace,
				Cloud:          "test-cloud",
				AuthConfiguration: &tw_pb.AuthConfiguration{
					Domain: "test-tenant1.com",
				},
			},
		},
	}

	daoFactory := NewDAOFactory(bigtableFixture.Table)
	auditLogger := audit.NewDefaultAuditLogger()

	authServicer := NewAuthGrpcServer(
		featureflag.NewLocalFeatureFlagHandler(),
		daoFactory,
		NewTenantMap(
			daoFactory,
			mockClient,
			"us-central.api.augmentcode.com",
			featureflag.NewLocalFeatureFlagHandler(),
			NewMockAsyncOpsPublisher(),
			auditLogger,
		),
		auditLogger,
		ripublisher.NewRequestInsightPublisherMock(),
		NewMockAsyncOpsPublisher(),
		&StripeConfig{Enabled: false},
		&OrbConfig{Enabled: false},
		NewMockStripeClient(),
	)

	ctx := createAuthorizedRequestContext()

	userEmail := fmt.Sprintf("<EMAIL>", uuid.New().String())
	request := &pb.AddUserToTenantRequest{
		Email:    userEmail,
		TenantId: tenantID,
	}

	// Test: Add user
	addUserResponse, err := authServicer.AddUserToTenant(ctx, request)
	require.NoError(t, err)
	assert.Equal(t, userEmail, addUserResponse.User.Email)

	// Test: Get user on tenant
	userOnTenantRequest := &pb.GetUserOnTenantRequest{
		UserId:   addUserResponse.User.Id,
		TenantId: tenantID,
	}
	userOnTenantResponse, err := authServicer.GetUserOnTenant(ctx, userOnTenantRequest)
	require.NoError(t, err)
	assert.Equal(t, 0, len(userOnTenantResponse.CustomerUiRoles))

	// Test: Update user on tenant
	updateRequest := &pb.UpdateUserOnTenantRequest{
		UserId:   addUserResponse.User.Id,
		TenantId: tenantID,
		CustomerUiRoles: []auth_entities.CustomerUiRole{
			auth_entities.CustomerUiRole_ADMIN,
		},
	}
	updateResponse, err := authServicer.UpdateUserOnTenant(ctx, updateRequest)
	require.NoError(t, err)
	assert.Equal(t, []auth_entities.CustomerUiRole{auth_entities.CustomerUiRole_ADMIN}, updateResponse.CustomerUiRoles)

	// Test: Get user on tenant after update
	userOnTenantResponse, err = authServicer.GetUserOnTenant(ctx, userOnTenantRequest)
	require.NoError(t, err)
	assert.Equal(t, 1, len(userOnTenantResponse.CustomerUiRoles))
	assert.Equal(t, auth_entities.CustomerUiRole_ADMIN, userOnTenantResponse.CustomerUiRoles[0])

	// Test: Update user on tenant with no roles
	updateRequest = &pb.UpdateUserOnTenantRequest{
		UserId:   addUserResponse.User.Id,
		TenantId: tenantID,
	}
	_, err = authServicer.UpdateUserOnTenant(ctx, updateRequest)
	require.NoError(t, err)

	// Test: Get user on tenant after update with no roles
	userOnTenantResponse, err = authServicer.GetUserOnTenant(ctx, userOnTenantRequest)
	require.NoError(t, err)
	assert.Equal(t, 0, len(userOnTenantResponse.CustomerUiRoles))
}

func TestDoubleDeletionNotFound(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	mockClient := &tw_client.MockTenantWatcherClient{
		Tenants: []*tw_pb.Tenant{
			{
				Id:             tenantID,
				Name:           tenantName,
				ShardNamespace: tenantNamespace,
				Cloud:          "test-cloud",
				AuthConfiguration: &tw_pb.AuthConfiguration{
					Domain: "test-tenant1.com",
				},
			},
		},
	}

	daoFactory := NewDAOFactory(bigtableFixture.Table)
	auditLogger := audit.NewDefaultAuditLogger()

	authServicer := NewAuthGrpcServer(
		featureflag.NewLocalFeatureFlagHandler(),
		daoFactory,
		NewTenantMap(
			daoFactory,
			mockClient,
			"us-central.api.augmentcode.com",
			featureflag.NewLocalFeatureFlagHandler(),
			NewMockAsyncOpsPublisher(),
			auditLogger,
		),
		auditLogger,
		ripublisher.NewRequestInsightPublisherMock(),
		NewMockAsyncOpsPublisher(),
		&StripeConfig{Enabled: false},
		&OrbConfig{Enabled: false},
		NewMockStripeClient(),
	)

	ctx := createAuthorizedRequestContext()

	request := &pb.RemoveUserFromTenantRequest{
		UserId:   "not-found",
		TenantId: tenantID,
	}
	_, err := authServicer.RemoveUserFromTenant(ctx, request)
	require.Error(t, err)
	require.Equal(t, codes.NotFound, status.Code(err))
}

func TestRevokeUserCookie(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	mockClient := &tw_client.MockTenantWatcherClient{
		Tenants: []*tw_pb.Tenant{
			{
				Id:             tenantID,
				Name:           tenantName,
				ShardNamespace: tenantNamespace,
				Cloud:          "test-cloud",
				AuthConfiguration: &tw_pb.AuthConfiguration{
					Domain: "test-tenant1.com",
				},
			},
		},
	}

	daoFactory := NewDAOFactory(bigtableFixture.Table)
	auditLogger := audit.NewDefaultAuditLogger()

	authServicer := NewAuthGrpcServer(
		featureflag.NewLocalFeatureFlagHandler(),
		daoFactory,
		NewTenantMap(
			daoFactory,
			mockClient,
			"us-central.api.augmentcode.com",
			featureflag.NewLocalFeatureFlagHandler(),
			NewMockAsyncOpsPublisher(),
			auditLogger,
		),
		&audit.AuditLogger{},
		ripublisher.NewRequestInsightPublisherMock(),
		NewMockAsyncOpsPublisher(),
		&StripeConfig{Enabled: false},
		&OrbConfig{Enabled: false},
		NewMockStripeClient(),
	)

	ctx := createAuthorizedRequestContext()

	userDao := daoFactory.GetUserDAO()

	// Populate with a sample entry
	startUserCount := getUserCount(t, ctx, userDao)

	userEmail := fmt.Sprintf("<EMAIL>", uuid.New().String())
	request := &pb.AddUserToTenantRequest{
		Email:    userEmail,
		TenantId: tenantID,
	}

	// Test: Add user
	addUserResponse, err := authServicer.AddUserToTenant(ctx, request)
	require.NoError(t, err)
	assert.Equal(t, userEmail, addUserResponse.User.Email)

	// Test: Add duplicate user
	_, err = authServicer.AddUserToTenant(ctx, request)
	require.NoError(t, err)
	currentUserCount := getUserCount(t, ctx, userDao)
	assert.Equal(t, startUserCount+1, currentUserCount)

	// Test: Explicitly revoke the cookie
	user, err := userDao.Get(ctx, addUserResponse.User.Id)
	require.NoError(t, err)
	prevNonce := user.Nonce
	_, err = authServicer.RevokeUserCookies(ctx, &pb.RevokeUserCookiesRequest{
		UserId:   addUserResponse.User.Id,
		TenantId: tenantID,
	})
	require.NoError(t, err)
	user, err = userDao.Get(ctx, addUserResponse.User.Id)
	require.NoError(t, err)
	nonce := user.Nonce
	assert.NotEqual(t, prevNonce, nonce)

	// Test: Remove user from tenant
	prevNonce = nonce
	_, err = authServicer.RemoveUserFromTenant(ctx, &pb.RemoveUserFromTenantRequest{
		UserId:   addUserResponse.User.Id,
		TenantId: tenantID,
	})
	require.NoError(t, err)
	user, err = userDao.Get(ctx, addUserResponse.User.Id)
	require.NoError(t, err)
	nonce = user.Nonce
	assert.NotEqual(t, prevNonce, nonce)
}

func TestCreateUserSuspensionWithDifferentBillingMethods(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	daoFactory := sut.daoFactory
	userDAO := daoFactory.GetUserDAO()
	subscriptionDAO := daoFactory.GetSubscriptionDAO()
	ctx := createAuthorizedRequestContext()

	// Test cases for different billing methods
	testCases := []struct {
		name                string
		billingMethod       auth_entities.BillingMethod
		subscriptionId      *string
		stripeSubStatus     auth_entities.Subscription_Status
		orbSubId            string
		hasPaymentMethod    bool
		expectError         bool
		errorContainsString string
	}{
		{
			name:                "Stripe billing method with subscription and no payment method",
			billingMethod:       auth_entities.BillingMethod_BILLING_METHOD_STRIPE,
			subscriptionId:      func() *string { s := "stripe-sub-123"; return &s }(),
			stripeSubStatus:     auth_entities.Subscription_ACTIVE,
			orbSubId:            "",
			hasPaymentMethod:    false,
			expectError:         true,
			errorContainsString: "User not on Orb billing method",
		},
		{
			name:                "Stripe billing method with subscription and payment method",
			billingMethod:       auth_entities.BillingMethod_BILLING_METHOD_STRIPE,
			subscriptionId:      func() *string { s := "stripe-sub-124"; return &s }(),
			stripeSubStatus:     auth_entities.Subscription_ACTIVE,
			orbSubId:            "",
			hasPaymentMethod:    true,
			expectError:         true,
			errorContainsString: "User not on Orb billing method",
		},
		{
			name:                "Stripe billing method without subscription",
			billingMethod:       auth_entities.BillingMethod_BILLING_METHOD_STRIPE,
			subscriptionId:      nil,
			stripeSubStatus:     auth_entities.Subscription_ACTIVE,
			orbSubId:            "",
			hasPaymentMethod:    false,
			expectError:         true,
			errorContainsString: "User not on Orb billing method",
		},
		{
			name:                "Orb billing method with subscription and no payment method",
			billingMethod:       auth_entities.BillingMethod_BILLING_METHOD_ORB,
			subscriptionId:      nil,
			stripeSubStatus:     auth_entities.Subscription_ACTIVE,
			orbSubId:            "orb-sub-123",
			hasPaymentMethod:    false,
			expectError:         false,
			errorContainsString: "",
		},
		{
			name:                "Orb billing method with subscription and payment method",
			billingMethod:       auth_entities.BillingMethod_BILLING_METHOD_ORB,
			subscriptionId:      nil,
			stripeSubStatus:     auth_entities.Subscription_ACTIVE,
			orbSubId:            "orb-sub-124",
			hasPaymentMethod:    true,
			expectError:         true,
			errorContainsString: "User has a payment method configured",
		},
		{
			name:                "Orb billing method without subscription",
			billingMethod:       auth_entities.BillingMethod_BILLING_METHOD_ORB,
			subscriptionId:      nil,
			stripeSubStatus:     auth_entities.Subscription_ACTIVE,
			orbSubId:            "",
			hasPaymentMethod:    false,
			expectError:         true,
			errorContainsString: "Orb user does not have a subscription",
		},
		{
			name:                "Unknown billing method with Stripe subscription and no payment method",
			billingMethod:       auth_entities.BillingMethod_BILLING_METHOD_UNKNOWN,
			subscriptionId:      func() *string { s := "stripe-sub-127"; return &s }(),
			stripeSubStatus:     auth_entities.Subscription_ACTIVE,
			orbSubId:            "",
			hasPaymentMethod:    false,
			expectError:         true,
			errorContainsString: "User not on Orb billing method",
		},
		{
			name:                "Unknown billing method with Orb subscription and no payment method",
			billingMethod:       auth_entities.BillingMethod_BILLING_METHOD_UNKNOWN,
			subscriptionId:      nil,
			stripeSubStatus:     auth_entities.Subscription_ACTIVE,
			orbSubId:            "orb-sub-128",
			hasPaymentMethod:    false,
			expectError:         true,
			errorContainsString: "User not on Orb billing method",
		},
		{
			name:                "Unknown billing method with subscription and payment method",
			billingMethod:       auth_entities.BillingMethod_BILLING_METHOD_UNKNOWN,
			subscriptionId:      func() *string { s := "stripe-sub-129"; return &s }(),
			stripeSubStatus:     auth_entities.Subscription_ACTIVE,
			orbSubId:            "",
			hasPaymentMethod:    true,
			expectError:         true,
			errorContainsString: "User not on Orb billing method",
		},
		{
			name:                "Unknown billing method without any subscriptions",
			billingMethod:       auth_entities.BillingMethod_BILLING_METHOD_UNKNOWN,
			subscriptionId:      nil,
			stripeSubStatus:     auth_entities.Subscription_ACTIVE,
			orbSubId:            "",
			hasPaymentMethod:    false,
			expectError:         true,
			errorContainsString: "User not on Orb billing method",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create a user with the specified billing method and subscription IDs
			userId := uuid.New().String()
			user := &auth_entities.User{
				Id:                userId,
				Email:             fmt.Sprintf("<EMAIL>", userId),
				Tenants:           []string{professionalTenantID},
				BillingMethod:     tc.billingMethod,
				SubscriptionId:    tc.subscriptionId,
				OrbSubscriptionId: tc.orbSubId,
			}
			_, err := userDAO.Create(ctx, user)
			require.NoError(t, err)

			// Create a Stripe subscription for the user if they have one
			if tc.subscriptionId != nil {
				subscription := &auth_entities.Subscription{
					SubscriptionId:   *tc.subscriptionId,
					Status:           tc.stripeSubStatus,
					HasPaymentMethod: tc.hasPaymentMethod,
				}
				_, err := subscriptionDAO.Create(ctx, subscription)
				require.NoError(t, err)
			}

			// Create an Orb subscription for the user if they have one
			if tc.orbSubId != "" {
				subscription := &auth_entities.Subscription{
					SubscriptionId:   tc.orbSubId,
					Status:           auth_entities.Subscription_TRIALING,
					HasPaymentMethod: tc.hasPaymentMethod,
					ExternalPlanId:   "orb_trial_plan",
				}
				_, err := subscriptionDAO.Create(ctx, subscription)
				require.NoError(t, err)
			}

			// Try to create a suspension for the user
			_, err = authServicer.CreateUserSuspension(ctx, &pb.CreateUserSuspensionRequest{
				UserId:         userId,
				TenantId:       tenantID,
				SuspensionType: auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE,
				Evidence:       "Test evidence",
			})

			if tc.expectError {
				require.Error(t, err)
				if tc.errorContainsString != "" {
					require.Contains(t, err.Error(), tc.errorContainsString)
				}
			} else {
				require.NoError(t, err)

				// Verify the suspension was created
				user, err := userDAO.Get(ctx, userId)
				require.NoError(t, err)
				require.Equal(t, 1, len(user.Suspensions))
				require.Equal(t, auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE, user.Suspensions[0].SuspensionType)
			}
		})
	}
}

func TestRevokeUser(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	mockClient := &tw_client.MockTenantWatcherClient{
		Tenants: []*tw_pb.Tenant{
			{
				Id:             tenantID,
				Name:           tenantName,
				ShardNamespace: tenantNamespace,
				Cloud:          "test-cloud",
				AuthConfiguration: &tw_pb.AuthConfiguration{
					Domain: "test-tenant1.com",
				},
			},
		},
	}

	daoFactory := NewDAOFactory(bigtableFixture.Table)
	auditLogger := audit.NewDefaultAuditLogger()

	authServicer := NewAuthGrpcServer(
		featureflag.NewLocalFeatureFlagHandler(),
		daoFactory,
		NewTenantMap(
			daoFactory,
			mockClient,
			"us-central.api.augmentcode.com",
			featureflag.NewLocalFeatureFlagHandler(),
			NewMockAsyncOpsPublisher(),
			auditLogger,
		),
		auditLogger,
		ripublisher.NewRequestInsightPublisherMock(),
		NewMockAsyncOpsPublisher(),
		&StripeConfig{Enabled: false},
		&OrbConfig{Enabled: false},
		NewMockStripeClient(),
	)

	ctx := createAuthorizedRequestContext()

	userEmail := fmt.Sprintf("<EMAIL>", uuid.New().String())
	request := &pb.AddUserToTenantRequest{
		Email:    userEmail,
		TenantId: tenantID,
	}

	// Test: Add user
	addUserResponse, err := authServicer.AddUserToTenant(ctx, request)
	require.NoError(t, err)
	assert.Equal(t, userEmail, addUserResponse.User.Email)

	tokenHashDAO := daoFactory.GetTokenHashDAO()

	// Populate with a sample token
	// sha256 of "hello"
	tokenHash := &auth_entities.TokenHash{
		Hash:          "2cf24dba5fb0a30e26e83b2ac5b9e29e1b161e5c1fa7425e73043362938b9824",
		AugmentUserId: addUserResponse.User.Id,
		TenantId:      tenantID,
	}
	_, err = tokenHashDAO.Create(ctx, tokenHash)
	require.NoError(t, err)

	// Verify the token hash exists
	stored, err := tokenHashDAO.Get(ctx, tokenHash.Hash)
	require.NoError(t, err)
	require.Equal(t, tokenHash.AugmentUserId, stored.AugmentUserId)

	userDao := daoFactory.GetUserDAO()

	// Get current user nonce
	user, err := userDao.Get(ctx, addUserResponse.User.Id)
	require.NoError(t, err)
	prevNonce := user.Nonce

	// Test: Get user on tenant
	userOnTenantRequest := &pb.GetUserOnTenantRequest{
		UserId:   addUserResponse.User.Id,
		TenantId: tenantID,
	}
	_, err = authServicer.GetUserOnTenant(ctx, userOnTenantRequest)
	require.NoError(t, err)

	// Test: Revoke user
	revokeResponse, err := authServicer.RevokeUser(ctx, &pb.RevokeUserRequest{
		Email:    userEmail,
		TenantId: tenantID,
	})
	require.NoError(t, err)
	assert.Equal(t, int32(1), revokeResponse.TokensDeleted)

	// Test: nonce changed
	user, err = userDao.Get(ctx, addUserResponse.User.Id)
	require.NoError(t, err)
	nonce := user.Nonce
	assert.NotEqual(t, prevNonce, nonce)

	// Test: Token hash deleted
	stored, err = tokenHashDAO.Get(ctx, tokenHash.Hash)
	require.NoError(t, err)
	require.Nil(t, stored)

	// User access has been revoked, but user still assigned to tenant.
	userOnTenantRequest = &pb.GetUserOnTenantRequest{
		UserId:   addUserResponse.User.Id,
		TenantId: tenantID,
	}
	userOnTenantResponse, err := authServicer.GetUserOnTenant(ctx, userOnTenantRequest)
	require.NoError(t, err)
	assert.Equal(t, 0, len(userOnTenantResponse.CustomerUiRoles))
}

func TestUpdateUserEmail(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	mockClient := &tw_client.MockTenantWatcherClient{
		Tenants: []*tw_pb.Tenant{
			{
				Id:             tenantID,
				Name:           tenantName,
				ShardNamespace: tenantNamespace,
				Cloud:          "test-cloud",
				AuthConfiguration: &tw_pb.AuthConfiguration{
					Domain: "test-tenant1.com",
				},
			},
		},
	}

	daoFactory := NewDAOFactory(bigtableFixture.Table)
	auditLogger := audit.NewDefaultAuditLogger()

	authServicer := NewAuthGrpcServer(
		featureflag.NewLocalFeatureFlagHandler(),
		daoFactory,
		NewTenantMap(
			daoFactory,
			mockClient,
			"us-central.api.augmentcode.com",
			featureflag.NewLocalFeatureFlagHandler(),
			NewMockAsyncOpsPublisher(),
			auditLogger,
		),
		auditLogger,
		ripublisher.NewRequestInsightPublisherMock(),
		NewMockAsyncOpsPublisher(),
		&StripeConfig{Enabled: false},
		&OrbConfig{Enabled: false},
		NewMockStripeClient(),
	)

	ctx := createAuthorizedRequestContext()
	userDao := daoFactory.GetUserDAO()
	tokenHashDAO := daoFactory.GetTokenHashDAO()

	userEmail := fmt.Sprintf("<EMAIL>", uuid.New().String())
	request := &pb.AddUserToTenantRequest{
		Email:    userEmail,
		TenantId: tenantID,
	}

	// Test: Add user
	addUserResponse, err := authServicer.AddUserToTenant(ctx, request)
	require.NoError(t, err)
	assert.Equal(t, userEmail, addUserResponse.User.Email)
	userId := addUserResponse.User.Id

	// Create a token hash for the user
	tokenHash := &auth_entities.TokenHash{
		Hash:          "2cf24dba5fb0a30e26e83b2ac5b9e29e1b161e5c1fa7425e73043362938b9824",
		AugmentUserId: userId,
		TenantId:      tenantID,
		EmailAddress:  userEmail,
	}
	_, err = tokenHashDAO.Create(ctx, tokenHash)
	require.NoError(t, err)

	// Test cases
	testCases := []struct {
		name          string
		userId        string
		currentEmail  string
		newEmail      string
		expectError   bool
		errorCode     codes.Code
		errorContains string
	}{
		{
			name:         "Success - Valid email update",
			userId:       userId,
			currentEmail: userEmail,
			newEmail:     fmt.Sprintf("<EMAIL>", uuid.New().String()),
			expectError:  false,
		},
		{
			name:          "Error - Current email doesn't match",
			userId:        userId,
			currentEmail:  "<EMAIL>",
			newEmail:      "<EMAIL>",
			expectError:   true,
			errorCode:     codes.InvalidArgument,
			errorContains: "Current email doesn't match",
		},
		{
			name:          "Error - User not found",
			userId:        "non-existent-user-id",
			currentEmail:  userEmail,
			newEmail:      "<EMAIL>",
			expectError:   true,
			errorCode:     codes.NotFound,
			errorContains: "User not found",
		},
	}

	changeEmailCtx := createCentralAuthorizedRequestContext()
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Test: Update user email
			_, err = authServicer.UpdateUserEmail(changeEmailCtx, &pb.UpdateUserEmailRequest{
				UserId:       tc.userId,
				CurrentEmail: tc.currentEmail,
				NewEmail:     tc.newEmail,
			})

			if tc.expectError {
				require.Error(t, err)
				require.Equal(t, tc.errorCode, status.Code(err))
				require.Contains(t, err.Error(), tc.errorContains)
			} else {
				require.NoError(t, err)

				// Verify user email was updated
				user, err := userDao.Get(ctx, tc.userId)
				require.NoError(t, err)
				assert.Equal(t, tc.newEmail, user.Email)
			}
		})
	}
}

func TestAuthServiceDeleteUserInDeletedTenant(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	tenantChangeChannel := make(chan tw_client.TenantChange, 1)

	mockClient := &tw_client.MockTenantWatcherClient{
		Changes: []chan tw_client.TenantChange{
			tenantChangeChannel,
		},
	}

	mockTenant := tw_pb.Tenant{
		Id:             tenantID,
		Name:           tenantName,
		ShardNamespace: tenantNamespace,
		Cloud:          "test-cloud",
	}
	mockTenantVersion1 := mockTenant

	tenantChangeChannel <- tw_client.TenantChange{
		Response: &tw_pb.WatchTenantsResponse{
			Tenants: []*tw_pb.TenantChange{
				{
					Type: &tw_pb.TenantChange_Updated{
						Updated: &tw_pb.TenantUpdate{
							Tenant: &mockTenantVersion1,
						},
					},
				},
			},
			IsInitial: true,
		},
	}
	auditLogger := audit.NewDefaultAuditLogger()

	daoFactory := NewDAOFactory(bigtableFixture.Table)
	tenantMap := NewTenantMap(
		daoFactory,
		mockClient,
		"us-central.api.augmentcode.com",
		featureflag.NewLocalFeatureFlagHandler(),
		NewMockAsyncOpsPublisher(),
		auditLogger,
	)

	authServicer := NewAuthGrpcServer(
		featureflag.NewLocalFeatureFlagHandler(),
		daoFactory,
		tenantMap,
		auditLogger,
		ripublisher.NewRequestInsightPublisherMock(),
		NewMockAsyncOpsPublisher(),
		&StripeConfig{Enabled: false},
		&OrbConfig{Enabled: false},
		NewMockStripeClient(),
	)

	ctx := createAuthorizedRequestContext()

	userEmail := fmt.Sprintf("<EMAIL>", uuid.New().String())
	request := &pb.AddUserToTenantRequest{
		Email:    userEmail,
		TenantId: tenantID,
	}

	// Test: Add user
	addUserResponse, err := authServicer.AddUserToTenant(ctx, request)
	require.NoError(t, err)
	assert.Equal(t, userEmail, addUserResponse.User.Email)

	// Delete tenant - note this will be processed asynchronously
	mockTenant.DeletedAt = "2023-01-01T00:00:00Z"
	mockTenantVersion2 := mockTenant

	tenantChangeChannel <- tw_client.TenantChange{
		Response: &tw_pb.WatchTenantsResponse{
			Tenants: []*tw_pb.TenantChange{
				{
					Type: &tw_pb.TenantChange_Updated{
						Updated: &tw_pb.TenantUpdate{
							Tenant: &mockTenantVersion2,
						},
					},
				},
			},
		},
	}

	// Wait until tenant map sees the update
	retries := 0
	backoff := 10 * time.Millisecond
	for {
		tenant, err := tenantMap.GetTenantByID(tenantID)
		require.NoError(t, err)
		if tenant == nil {
			break
		}
		if retries > 10 {
			require.Fail(t, "timed out waiting for tenant deletion to be observed")
			break
		}
		backoff = min(backoff*2, 1000*time.Millisecond)
		time.Sleep(backoff)
		retries++
	}

	// Test: List tenant users should still work
	listRequest := &pb.ListTenantUsersRequest{TenantId: tenantID}
	_, err = authServicer.ListTenantUsers(ctx, listRequest)
	require.NoError(t, err)

	// Test: remove user from tenant even though it's deleted
	_, err = authServicer.RemoveUserFromTenant(ctx, &pb.RemoveUserFromTenantRequest{
		UserId:   addUserResponse.User.Id,
		TenantId: tenantID,
	})
	require.NoError(t, err)
}

func TestAuthServiceGetTokenInfoDeletedTenant(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	tenantChangeChannel := make(chan tw_client.TenantChange, 1)

	mockClient := &tw_client.MockTenantWatcherClient{
		Changes: []chan tw_client.TenantChange{
			tenantChangeChannel,
		},
	}

	mockTenant := tw_pb.Tenant{
		Id:             tenantID,
		Name:           tenantName,
		ShardNamespace: tenantNamespace,
		Cloud:          "test-cloud",
		Tier:           tw_pb.TenantTier_ENTERPRISE,
	}
	mockTenantVersion1 := mockTenant

	tenantChangeChannel <- tw_client.TenantChange{
		Response: &tw_pb.WatchTenantsResponse{
			Tenants: []*tw_pb.TenantChange{
				{
					Type: &tw_pb.TenantChange_Updated{
						Updated: &tw_pb.TenantUpdate{
							Tenant: &mockTenantVersion1,
						},
					},
				},
			},
			IsInitial: true,
		},
	}

	ffHandler := featureflag.NewLocalFeatureFlagHandler()
	ffHandler.Set("check_subscription_status", true)
	ffHandler.Set("enforce_usage_credits", true)
	auditLogger := audit.NewDefaultAuditLogger()

	daoFactory := NewDAOFactory(bigtableFixture.Table)
	tenantMap := NewTenantMap(
		daoFactory,
		mockClient,
		"us-central.api.augmentcode.com",
		ffHandler,
		NewMockAsyncOpsPublisher(),
		auditLogger,
	)

	authServicer := NewAuthGrpcServer(
		ffHandler,
		daoFactory,
		tenantMap,
		auditLogger,
		ripublisher.NewRequestInsightPublisherMock(),
		NewMockAsyncOpsPublisher(),
		&StripeConfig{Enabled: false},
		&OrbConfig{Enabled: false},
		NewMockStripeClient(),
	)

	ctx := createAuthorizedRequestContext()

	userEmail := fmt.Sprintf("<EMAIL>", uuid.New().String())
	request := &pb.AddUserToTenantRequest{
		Email:    userEmail,
		TenantId: tenantID,
	}

	// Test: Add user
	addUserResponse, err := authServicer.AddUserToTenant(ctx, request)
	require.NoError(t, err)
	assert.Equal(t, userEmail, addUserResponse.User.Email)

	tokenHashDAO := daoFactory.GetTokenHashDAO()

	// Populate with a sample token
	// sha256 of "hello"
	tokenHash := &auth_entities.TokenHash{
		Hash:          "2cf24dba5fb0a30e26e83b2ac5b9e29e1b161e5c1fa7425e73043362938b9824",
		AugmentUserId: addUserResponse.User.Id,
		TenantId:      tenantID,
		EmailAddress:  userEmail,
	}
	_, err = tokenHashDAO.Create(ctx, tokenHash)
	require.NoError(t, err)

	// Verify that we can get the token info
	getTokenInfoResponse, err := authServicer.GetTokenInfo(ctx, &pb.GetTokenInfoRequest{
		Token: "hello",
	})
	require.NoError(t, err)
	assert.Equal(t, addUserResponse.User.Email, getTokenInfoResponse.UserId)
	assert.Equal(t, addUserResponse.User.Id, getTokenInfoResponse.AugmentUserId)
	assert.Equal(t, addUserResponse.User.Email, getTokenInfoResponse.UserEmail)
	assert.Equal(t, tenantID, getTokenInfoResponse.TenantId)
	assert.Equal(t, tenantName, getTokenInfoResponse.TenantName)
	// Check enterprise subscription
	enterpriseSub, ok := getTokenInfoResponse.Subscription.(*pb.GetTokenInfoResponse_Enterprise)
	require.True(t, ok)
	require.NotNil(t, enterpriseSub.Enterprise)

	// Delete tenant - note this will be processed asynchronously
	mockTenant.DeletedAt = "2023-01-01T00:00:00Z"
	mockTenantVersion2 := mockTenant

	tenantChangeChannel <- tw_client.TenantChange{
		Response: &tw_pb.WatchTenantsResponse{
			Tenants: []*tw_pb.TenantChange{
				{
					Type: &tw_pb.TenantChange_Updated{
						Updated: &tw_pb.TenantUpdate{
							Tenant: &mockTenantVersion2,
						},
					},
				},
			},
		},
	}

	// Wait until tenant map sees the update
	retries := 0
	backoff := 10 * time.Millisecond
	for {
		tenant, err := tenantMap.GetTenantByID(tenantID)
		require.NoError(t, err)
		if tenant == nil {
			break
		}
		if retries > 10 {
			require.Fail(t, "timed out waiting for tenant deletion to be observed")
			break
		}
		backoff = min(backoff*2, 1000*time.Millisecond)
		time.Sleep(backoff)
		retries++
	}

	// Assert that GetTokenInfo returns 401 after tenant delete
	getTokenInfoResponse, err = authServicer.GetTokenInfo(ctx, &pb.GetTokenInfoRequest{
		Token: "hello",
	})
	require.Error(t, err)
	assert.Equal(t, codes.NotFound, status.Code(err))
}

func TestAuthServiceGetTokenInfoWithSubscription(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	trialEndTime := time.Now().Add(24 * time.Hour)

	// Define test cases
	tests := []struct {
		name                 string
		tenantTier           tw_pb.TenantTier
		subscriptionStatus   auth_entities.Subscription_OrbStatus
		hasSubscription      bool
		expectedSubscription interface{} // Will hold the expected oneof type
		expectedTrialEnd     *timestamppb.Timestamp
	}{
		{
			name:            "Enterprise tenant",
			tenantTier:      tw_pb.TenantTier_ENTERPRISE,
			hasSubscription: false,
			expectedSubscription: &pb.GetTokenInfoResponse_Enterprise{
				Enterprise: &pb.EnterpriseSubscription{},
			},
		},
		{
			name:               "Community subscription that is active",
			tenantTier:         tw_pb.TenantTier_COMMUNITY,
			subscriptionStatus: auth_entities.Subscription_ORB_STATUS_ACTIVE,
			hasSubscription:    true,
			expectedSubscription: &pb.GetTokenInfoResponse_ActiveSubscription{
				ActiveSubscription: &pb.ActiveSubscription{},
			},
		},
		{
			name:               "Professional subscription that is trialing",
			tenantTier:         tw_pb.TenantTier_PROFESSIONAL,
			subscriptionStatus: auth_entities.Subscription_ORB_STATUS_ACTIVE,
			hasSubscription:    true,
			expectedSubscription: &pb.GetTokenInfoResponse_ActiveSubscription{
				ActiveSubscription: &pb.ActiveSubscription{
					EndDate: timestamppb.New(trialEndTime),
				},
			},
			expectedTrialEnd: timestamppb.New(trialEndTime),
		},
		{
			name:               "Professional subscription that is inactive",
			tenantTier:         tw_pb.TenantTier_PROFESSIONAL,
			subscriptionStatus: auth_entities.Subscription_ORB_STATUS_ENDED,
			hasSubscription:    true,
			expectedSubscription: &pb.GetTokenInfoResponse_InactiveSubscription{
				InactiveSubscription: &pb.InactiveSubscription{},
			},
		},
		{
			name:            "Professional tenant with no subscription",
			tenantTier:      tw_pb.TenantTier_PROFESSIONAL,
			hasSubscription: false,
			expectedSubscription: &pb.GetTokenInfoResponse_ActiveSubscription{
				ActiveSubscription: &pb.ActiveSubscription{},
			},
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			mockClient := &tw_client.MockTenantWatcherClient{
				Tenants: []*tw_pb.Tenant{
					{
						Id:             tenantID,
						Name:           tenantName,
						ShardNamespace: tenantNamespace,
						Cloud:          "test-cloud",
						AuthConfiguration: &tw_pb.AuthConfiguration{
							Domain: "test-tenant1.com",
						},
						Tier: tc.tenantTier,
					},
				},
			}

			daoFactory := NewDAOFactory(bigtableFixture.Table)
			featureFlagHandle := featureflag.NewLocalFeatureFlagHandler()
			featureFlagHandle.Set("check_subscription_status", true)
			featureFlagHandle.Set("enforce_usage_credits", true)
			auditLogger := audit.NewDefaultAuditLogger()

			tenantMap := NewTenantMap(
				daoFactory,
				mockClient,
				"us-central.api.augmentcode.com",
				featureFlagHandle,
				NewMockAsyncOpsPublisher(),
				auditLogger,
			)

			authServicer := NewAuthGrpcServer(
				featureFlagHandle,
				daoFactory,
				tenantMap,
				auditLogger,
				ripublisher.NewRequestInsightPublisherMock(),
				NewMockAsyncOpsPublisher(),
				&StripeConfig{Enabled: false},
				&OrbConfig{Enabled: false},
				NewMockStripeClient(),
			)

			ctx := createAuthorizedRequestContext()

			// Add a user to the tenant
			userEmail := fmt.Sprintf("<EMAIL>", uuid.New().String())
			request := &pb.AddUserToTenantRequest{
				Email:    userEmail,
				TenantId: tenantID,
			}

			// Test: Add user
			addUserResponse, err := authServicer.AddUserToTenant(ctx, request)
			require.NoError(t, err)
			assert.Equal(t, userEmail, addUserResponse.User.Email)

			claims := &auth.AugmentClaims{
				UserID:         addUserResponse.User.Id,
				TenantID:       tenantID,
				TenantName:     tenantName,
				ShardNamespace: tenantNamespace,
				Cloud:          "test-cloud",
				Scope:          []string{tokenexchangeproto.Scope_AUTH_RW.String()},
			}
			ctx = claims.NewContext(context.Background())

			tokenHashDAO := daoFactory.GetTokenHashDAO()

			// Create token hash
			tokenHash := &auth_entities.TokenHash{
				Hash:          "2cf24dba5fb0a30e26e83b2ac5b9e29e1b161e5c1fa7425e73043362938b9824",
				AugmentUserId: addUserResponse.User.Id,
				TenantId:      tenantID,
				EmailAddress:  userEmail,
			}
			_, err = tokenHashDAO.Create(ctx, tokenHash)
			require.NoError(t, err)

			// Create subscription if needed for this test case
			if tc.hasSubscription {
				subscriptionId := "test-subscription-id"
				subscription := &auth_entities.Subscription{
					SubscriptionId: subscriptionId,
					OrbStatus:      tc.subscriptionStatus,
				}
				if tc.expectedTrialEnd != nil {
					subscription.EndDate = tc.expectedTrialEnd
					subscription.HasPaymentMethod = false
				}

				// Save the subscription to the database
				subscriptionDAO := NewSubscriptionDAO(bigtableFixture.Table)
				_, err = subscriptionDAO.Create(ctx, subscription)
				require.NoError(t, err)

				// Update the user to reference this subscription
				userDAO := daoFactory.GetUserDAO()
				updateFn := func(u *auth_entities.User) bool {
					u.OrbSubscriptionId = subscriptionId
					u.BillingMethod = auth_entities.BillingMethod_BILLING_METHOD_ORB
					u.SubscriptionCreationInfo = &auth_entities.User_SubscriptionCreationInfo{
						Id:        "test-subscription-creation-id",
						Status:    auth_entities.User_SubscriptionCreationInfo_SUCCESS,
						CreatedAt: timestamppb.Now(),
						UpdatedAt: timestamppb.Now(),
					}
					return true
				}
				_, err = userDAO.TryUpdate(ctx, addUserResponse.User.Id, updateFn, DefaultRetry)
				require.NoError(t, err)
			}

			// Get token info
			getTokenInfoResponse, err := authServicer.GetTokenInfo(ctx, &pb.GetTokenInfoRequest{
				Token: "hello",
			})
			require.NoError(t, err)

			// Verify basic fields
			assert.Equal(t, userEmail, getTokenInfoResponse.UserId)
			assert.Equal(t, addUserResponse.User.Id, getTokenInfoResponse.AugmentUserId)
			assert.Equal(t, userEmail, getTokenInfoResponse.UserEmail)
			assert.Equal(t, tenantID, getTokenInfoResponse.TenantId)
			assert.Equal(t, tenantName, getTokenInfoResponse.TenantName)

			// Verify subscription matches expected type
			assert.IsType(t, tc.expectedSubscription, getTokenInfoResponse.Subscription)

			// Additional type-specific checks
			switch expected := tc.expectedSubscription.(type) {
			case *pb.GetTokenInfoResponse_ActiveSubscription:
				actual, ok := getTokenInfoResponse.Subscription.(*pb.GetTokenInfoResponse_ActiveSubscription)
				require.True(t, ok)
				if expected.ActiveSubscription.EndDate != nil {
					assert.Equal(t, expected.ActiveSubscription.EndDate.AsTime(), actual.ActiveSubscription.EndDate.AsTime())
				}
			}
		})
	}
}

func TestTokenExpired(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	tenantChangeChannel := make(chan tw_client.TenantChange, 1)

	mockClient := &tw_client.MockTenantWatcherClient{
		Changes: []chan tw_client.TenantChange{
			tenantChangeChannel,
		},
	}

	mockTenant := tw_pb.Tenant{
		Id:             tenantID,
		Name:           tenantName,
		ShardNamespace: tenantNamespace,
		Cloud:          "test-cloud",
	}
	mockTenantVersion1 := mockTenant

	tenantChangeChannel <- tw_client.TenantChange{
		Response: &tw_pb.WatchTenantsResponse{
			Tenants: []*tw_pb.TenantChange{
				{
					Type: &tw_pb.TenantChange_Updated{
						Updated: &tw_pb.TenantUpdate{
							Tenant: &mockTenantVersion1,
						},
					},
				},
			},
			IsInitial: true,
		},
	}

	daoFactory := NewDAOFactory(bigtableFixture.Table)
	auditLogger := audit.NewDefaultAuditLogger()
	tenantMap := NewTenantMap(
		daoFactory,
		mockClient,
		"us-central.api.augmentcode.com",
		featureflag.NewLocalFeatureFlagHandler(),
		NewMockAsyncOpsPublisher(),
		auditLogger,
	)

	authServicer := NewAuthGrpcServer(
		featureflag.NewLocalFeatureFlagHandler(),
		daoFactory,
		tenantMap,
		auditLogger,
		ripublisher.NewRequestInsightPublisherMock(),
		NewMockAsyncOpsPublisher(),
		&StripeConfig{Enabled: false},
		&OrbConfig{Enabled: false},
		NewMockStripeClient(),
	)

	ctx := createAuthorizedRequestContext()

	userEmail := fmt.Sprintf("<EMAIL>", uuid.New().String())
	request := &pb.AddUserToTenantRequest{
		Email:    userEmail,
		TenantId: tenantID,
	}

	// Test: Add user
	addUserResponse, err := authServicer.AddUserToTenant(ctx, request)
	require.NoError(t, err)
	assert.Equal(t, userEmail, addUserResponse.User.Email)

	tokenHashDAO := daoFactory.GetTokenHashDAO()

	// Populate with a sample token
	// sha256 of "hello"
	tokenHash := &auth_entities.TokenHash{
		Hash:                  "2cf24dba5fb0a30e26e83b2ac5b9e29e1b161e5c1fa7425e73043362938b9824",
		AugmentUserId:         addUserResponse.User.Id,
		TenantId:              tenantID,
		EmailAddress:          userEmail,
		CreationTime:          &timestamppb.Timestamp{Seconds: 1},
		ExpirationTimeSeconds: 1, // 1 second
	}
	_, err = tokenHashDAO.Create(ctx, tokenHash)
	require.NoError(t, err)

	// Verify that we can get the token info
	getTokenInfoResponse, err := authServicer.GetTokenInfo(ctx, &pb.GetTokenInfoRequest{
		Token: "hello",
	})
	assert.Empty(t, getTokenInfoResponse)
	assert.Equal(t, codes.NotFound, status.Code(err))
	assert.Contains(t, err.Error(), "Token expired")
}

func TestGetUserBillingInfo(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	mockClient := &tw_client.MockTenantWatcherClient{
		Tenants: []*tw_pb.Tenant{
			{
				Id:             tenantID,
				Name:           tenantName,
				ShardNamespace: tenantNamespace,
				Cloud:          "test-cloud",
				AuthConfiguration: &tw_pb.AuthConfiguration{
					Domain: "test-tenant1.com",
				},
			},
			{
				Id:             "self-serve-team-id",
				Name:           "self-serve-team",
				ShardNamespace: tenantNamespace,
				Cloud:          "test-cloud",
				AuthConfiguration: &tw_pb.AuthConfiguration{
					Domain: "self-serve-team.com",
				},
				Config: &tw_pb.Config{
					Configs: map[string]string{
						"is_self_serve_team": "true",
					},
				},
			},
		},
	}

	daoFactory := NewDAOFactory(bigtableFixture.Table)
	auditLogger := audit.NewDefaultAuditLogger()

	authServicer := NewAuthGrpcServer(
		featureflag.NewLocalFeatureFlagHandler(),
		daoFactory,
		NewTenantMap(
			daoFactory,
			mockClient,
			"us-central.api.augmentcode.com",
			featureflag.NewLocalFeatureFlagHandler(),
			NewMockAsyncOpsPublisher(),
			auditLogger,
		),
		auditLogger,
		ripublisher.NewRequestInsightPublisherMock(),
		NewMockAsyncOpsPublisher(),
		&StripeConfig{Enabled: false},
		&OrbConfig{Enabled: false},
		NewMockStripeClient(),
	)

	ctx := createAuthorizedRequestContext()
	ctxTeam := createAuthorizedRequestContextForTeam()

	// Create a test user
	userEmail := fmt.Sprintf("<EMAIL>", uuid.New().String())
	userDao := daoFactory.GetUserDAO()
	user := &auth_entities.User{
		Id:            "test-user-id",
		Email:         userEmail,
		Tenants:       []string{tenantID},
		OrbCustomerId: "orb-customer-123",
	}
	teamUser := &auth_entities.User{
		Id:            "test-team-user-id",
		Email:         userEmail,
		Tenants:       []string{"self-serve-team-id"},
		OrbCustomerId: "orb-team-customer-456",
	}
	_, err := userDao.Create(ctx, user)
	require.NoError(t, err)
	_, err = userDao.Create(ctx, teamUser)
	require.NoError(t, err)

	// Create user tenant mappings
	userTenantMappingDAO := daoFactory.GetUserTenantMappingDAO("individual")
	userMapping := &auth_entities.UserTenantMapping{
		UserId: "test-user-id",
		Tenant: "individual",
	}
	_, err = userTenantMappingDAO.Create(ctx, userMapping)
	require.NoError(t, err)

	teamUserTenantMappingDAO := daoFactory.GetUserTenantMappingDAO("self-serve-team")
	teamUserMapping := &auth_entities.UserTenantMapping{
		UserId: "test-team-user-id",
		Tenant: "self-serve-team",
	}
	_, err = teamUserTenantMappingDAO.Create(ctx, teamUserMapping)
	require.NoError(t, err)

	// Create a tenant subscription mapping for the self-serve team
	tenantSubscriptionMappingDAO := daoFactory.GetTenantSubscriptionMappingDAO()
	tenantSubscriptionMapping := &auth_entities.TenantSubscriptionMapping{
		TenantId:             "self-serve-team-id",
		StripeSubscriptionId: "sub-123",
		OrbCustomerId:        "orb-team-customer-456",
	}
	_, err = tenantSubscriptionMappingDAO.Create(ctx, tenantSubscriptionMapping)
	require.NoError(t, err)

	// Test 1: Get user billing info for regular tenant
	request := &pb.GetUserBillingInfoRequest{
		UserId:   "test-user-id",
		TenantId: tenantID,
	}
	response, err := authServicer.GetUserBillingInfo(ctx, request)
	require.NoError(t, err)
	assert.Equal(t, "test-user-id", response.UserId)
	assert.Equal(t, userEmail, response.Email)
	assert.Equal(t, "orb-customer-123", response.OrbCustomerId)
	assert.False(t, response.IsSelfServeTeam)

	// Test 2: Get user billing info for self-serve team tenant
	request = &pb.GetUserBillingInfoRequest{
		UserId:   "test-team-user-id",
		TenantId: "self-serve-team-id",
	}
	response, err = authServicer.GetUserBillingInfo(ctxTeam, request)
	require.NoError(t, err)
	assert.Equal(t, "test-team-user-id", response.UserId)
	assert.Equal(t, userEmail, response.Email)
	assert.Equal(t, "orb-team-customer-456", response.OrbCustomerId)
	assert.True(t, response.IsSelfServeTeam)

	// Test 3: User not found
	request = &pb.GetUserBillingInfoRequest{
		UserId:   "non-existent-user",
		TenantId: tenantID,
	}
	_, err = authServicer.GetUserBillingInfo(ctx, request)
	require.Error(t, err)
	assert.Equal(t, codes.NotFound, status.Code(err))

	// Test 4: Tenant not found
	request = &pb.GetUserBillingInfoRequest{
		UserId:   "test-user-id",
		TenantId: "non-existent-tenant",
	}
	_, err = authServicer.GetUserBillingInfo(ctx, request)
	require.Error(t, err)
}

func TestUpdateUserBillingInfo(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	mockClient := &tw_client.MockTenantWatcherClient{
		Tenants: []*tw_pb.Tenant{
			{
				Id:             tenantID,
				Name:           tenantName,
				ShardNamespace: tenantNamespace,
				Cloud:          "test-cloud",
				AuthConfiguration: &tw_pb.AuthConfiguration{
					Domain: "test-tenant1.com",
				},
			},
		},
	}

	daoFactory := NewDAOFactory(bigtableFixture.Table)
	auditLogger := audit.NewDefaultAuditLogger()

	authServicer := NewAuthGrpcServer(
		featureflag.NewLocalFeatureFlagHandler(),
		daoFactory,
		NewTenantMap(
			daoFactory,
			mockClient,
			"us-central.api.augmentcode.com",
			featureflag.NewLocalFeatureFlagHandler(),
			NewMockAsyncOpsPublisher(),
			auditLogger,
		),
		auditLogger,
		ripublisher.NewRequestInsightPublisherMock(),
		NewMockAsyncOpsPublisher(),
		&StripeConfig{Enabled: false},
		&OrbConfig{Enabled: false},
		NewMockStripeClient(),
	)

	ctx := createAuthorizedRequestContext()

	// Create a test user
	userEmail := fmt.Sprintf("<EMAIL>", uuid.New().String())
	userDao := daoFactory.GetUserDAO()
	user := &auth_entities.User{
		Id:                "test-user-id",
		Email:             userEmail,
		Tenants:           []string{tenantID},
		OrbCustomerId:     "orb-customer-123",
		BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_STRIPE,
		OrbSubscriptionId: "orb-subscription-123",
		StripeCustomerId:  "stripe-customer-123",
	}
	_, err := userDao.Create(ctx, user)
	require.NoError(t, err)

	// Test scenarios
	testCases := []struct {
		name              string
		userId            string
		tenantId          string
		billingMethod     *auth_entities.BillingMethod
		orbCustomerId     *string
		orbSubscriptionId *string
		stripeCustomerId  *string
		expectError       bool
		expectedErrorCode codes.Code
		validateFunc      func(t *testing.T, updatedUser *auth_entities.User)
	}{
		{
			name:     "Update billing method",
			userId:   "test-user-id",
			tenantId: tenantID,
			billingMethod: func() *auth_entities.BillingMethod {
				method := auth_entities.BillingMethod_BILLING_METHOD_ORB
				return &method
			}(),
			validateFunc: func(t *testing.T, updatedUser *auth_entities.User) {
				assert.Equal(t, auth_entities.BillingMethod_BILLING_METHOD_ORB, updatedUser.BillingMethod)
				assert.Equal(t, "orb-customer-123", updatedUser.OrbCustomerId)
				assert.Equal(t, "orb-subscription-123", updatedUser.OrbSubscriptionId)
			},
		},
		{
			name:     "Update Orb customer ID",
			userId:   "test-user-id",
			tenantId: tenantID,
			orbCustomerId: func() *string {
				id := "new-orb-customer-456"
				return &id
			}(),
			validateFunc: func(t *testing.T, updatedUser *auth_entities.User) {
				assert.Equal(t, auth_entities.BillingMethod_BILLING_METHOD_ORB, updatedUser.BillingMethod) // From previous test
				assert.Equal(t, "new-orb-customer-456", updatedUser.OrbCustomerId)
				assert.Equal(t, "orb-subscription-123", updatedUser.OrbSubscriptionId)
			},
		},
		{
			name:     "Update Orb subscription ID",
			userId:   "test-user-id",
			tenantId: tenantID,
			orbSubscriptionId: func() *string {
				id := "new-orb-subscription-456"
				return &id
			}(),
			validateFunc: func(t *testing.T, updatedUser *auth_entities.User) {
				assert.Equal(t, auth_entities.BillingMethod_BILLING_METHOD_ORB, updatedUser.BillingMethod) // From previous test
				assert.Equal(t, "new-orb-customer-456", updatedUser.OrbCustomerId)                         // From previous test
				assert.Equal(t, "new-orb-subscription-456", updatedUser.OrbSubscriptionId)
			},
		},
		{
			name:     "Update Stripe customer ID",
			userId:   "test-user-id",
			tenantId: tenantID,
			stripeCustomerId: func() *string {
				id := "new-stripe-customer-456"
				return &id
			}(),
			validateFunc: func(t *testing.T, updatedUser *auth_entities.User) {
				assert.Equal(t, auth_entities.BillingMethod_BILLING_METHOD_ORB, updatedUser.BillingMethod) // From previous test
				assert.Equal(t, "new-orb-customer-456", updatedUser.OrbCustomerId)                         // From previous test
				assert.Equal(t, "new-orb-subscription-456", updatedUser.OrbSubscriptionId)                 // From previous test
				assert.Equal(t, "new-stripe-customer-456", updatedUser.StripeCustomerId)
			},
		},
		{
			name:     "Update all fields",
			userId:   "test-user-id",
			tenantId: tenantID,
			billingMethod: func() *auth_entities.BillingMethod {
				method := auth_entities.BillingMethod_BILLING_METHOD_ORB
				return &method
			}(),
			orbCustomerId: func() *string {
				id := "final-orb-customer-789"
				return &id
			}(),
			orbSubscriptionId: func() *string {
				id := "final-orb-subscription-789"
				return &id
			}(),
			validateFunc: func(t *testing.T, updatedUser *auth_entities.User) {
				assert.Equal(t, auth_entities.BillingMethod_BILLING_METHOD_ORB, updatedUser.BillingMethod)
				assert.Equal(t, "final-orb-customer-789", updatedUser.OrbCustomerId)
				assert.Equal(t, "final-orb-subscription-789", updatedUser.OrbSubscriptionId)
			},
		},
		{
			name:     "No changes when no fields provided",
			userId:   "test-user-id",
			tenantId: tenantID,
			validateFunc: func(t *testing.T, updatedUser *auth_entities.User) {
				// Should remain the same as the last test
				assert.Equal(t, auth_entities.BillingMethod_BILLING_METHOD_ORB, updatedUser.BillingMethod)
				assert.Equal(t, "final-orb-customer-789", updatedUser.OrbCustomerId)
				assert.Equal(t, "final-orb-subscription-789", updatedUser.OrbSubscriptionId)
			},
		},
		{
			name:              "User not found",
			userId:            "non-existent-user",
			tenantId:          tenantID,
			expectError:       true,
			expectedErrorCode: codes.NotFound,
		},
		{
			name:              "Invalid tenant",
			userId:            "test-user-id",
			tenantId:          "non-existent-tenant",
			expectError:       true,
			expectedErrorCode: codes.PermissionDenied,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			request := &pb.UpdateUserBillingInfoRequest{
				UserId:            tc.userId,
				TenantId:          tc.tenantId,
				BillingMethod:     tc.billingMethod,
				OrbCustomerId:     tc.orbCustomerId,
				OrbSubscriptionId: tc.orbSubscriptionId,
				StripeCustomerId:  tc.stripeCustomerId,
			}

			response, err := authServicer.UpdateUserBillingInfo(ctx, request)

			if tc.expectError {
				require.Error(t, err)
				assert.Equal(t, tc.expectedErrorCode, status.Code(err))
			} else {
				require.NoError(t, err)
				require.NotNil(t, response)

				// Verify the user was updated correctly
				updatedUser, err := userDao.Get(ctx, tc.userId)
				require.NoError(t, err)
				require.NotNil(t, updatedUser)

				if tc.validateFunc != nil {
					tc.validateFunc(t, updatedUser)
				}
			}
		})
	}
}

func TestRemoveSelfServeAccountsForTeam(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	daoFactory := sut.daoFactory

	userDao := daoFactory.GetUserDAO()

	t.Run("Auth checks", func(t *testing.T) {
		// Test with insufficient permissions
		claims := &auth.AugmentClaims{
			TenantID:         "admin-tenant-id",
			TenantName:       "admin-tenant",
			UserID:           "admin-user-id",
			OpaqueUserID:     "admin-user-id",
			OpaqueUserIDType: auth_entities.UserId_INTERNAL_IAP.String(),
			Scope:            []string{tokenexchangeproto.Scope_AUTH_R.String()},
		}
		req := &pb.RemoveSelfServeAccountsForTeamRequest{
			DryRun: true,
		}
		ctx := claims.NewContext(context.Background())
		_, err := authServicer.RemoveSelfServeAccountsForTeam(ctx, req)
		require.Error(t, err)
		require.Equal(t, codes.PermissionDenied, status.Code(err))
	})

	t.Run("DryRun", func(t *testing.T) {
		// Create a user with multiple tenants
		user := &auth_entities.User{
			Id:      "multi-tenant-user",
			Email:   "<EMAIL>",
			Tenants: []string{tenantID, professionalTenantID},
		}
		_, err := userDao.Create(context.Background(), user)
		require.NoError(t, err)

		ctx := createCentralAuthorizedRequestContext()

		// Run in dry-run mode
		req := &pb.RemoveSelfServeAccountsForTeamRequest{
			DryRun: true,
		}
		resp, err := authServicer.RemoveSelfServeAccountsForTeam(ctx, req)
		require.NoError(t, err)

		// Verify response
		require.NotNil(t, resp)
		require.GreaterOrEqual(t, len(resp.RemovedUsers), 1)

		// Verify user still has both tenants
		updatedUser, err := userDao.Get(context.Background(), "multi-tenant-user")
		require.NoError(t, err)
		require.Contains(t, updatedUser.Tenants, tenantID)
		require.Contains(t, updatedUser.Tenants, professionalTenantID)
	})

	t.Run("ActualRemoval", func(t *testing.T) {
		// Create a user with multiple tenants
		user := &auth_entities.User{
			Id:      "multi-tenant-user-2",
			Email:   "<EMAIL>",
			Tenants: []string{tenantID, professionalTenantID},
		}
		_, err := userDao.Create(context.Background(), user)
		require.NoError(t, err)

		// Add user to tenants
		userTenantMappingDAO := daoFactory.GetUserTenantMappingDAO(tenantName)
		mapping := &auth_entities.UserTenantMapping{
			UserId: "multi-tenant-user-2",
			Tenant: tenantName,
		}
		_, err = userTenantMappingDAO.Create(context.Background(), mapping)
		require.NoError(t, err)

		userTenantMappingDAO = daoFactory.GetUserTenantMappingDAO("professional-tenant")
		mapping = &auth_entities.UserTenantMapping{
			UserId: "multi-tenant-user-2",
			Tenant: "professional-tenant",
		}
		_, err = userTenantMappingDAO.Create(context.Background(), mapping)
		require.NoError(t, err)

		ctx := createCentralAuthorizedRequestContext()

		// Run with actual removal
		req := &pb.RemoveSelfServeAccountsForTeamRequest{
			DryRun: false,
		}
		resp, err := authServicer.RemoveSelfServeAccountsForTeam(ctx, req)
		require.NoError(t, err)

		// Verify response
		require.NotNil(t, resp)

		// Find our test user in the response
		var userRemoval *pb.RemoveSelfServeAccountsForTeamResponse_UserRemovals
		for _, removal := range resp.RemovedUsers {
			if removal.UserId == "multi-tenant-user-2" {
				userRemoval = removal
				break
			}
		}

		// Verify user was processed
		require.NotNil(t, userRemoval)
		require.Equal(t, "multi-tenant-user-2", userRemoval.UserId)
		require.Len(t, userRemoval.PrimaryTenants, 1)
		require.Equal(t, tenantID, userRemoval.PrimaryTenants[0].TenantId)
		require.Len(t, userRemoval.RemovedTenants, 1)
		require.Equal(t, professionalTenantID, userRemoval.RemovedTenants[0].TenantId)

		// Verify user now only has the team tenant
		updatedUser, err := userDao.Get(context.Background(), "multi-tenant-user-2")
		require.NoError(t, err)
		require.NotContains(t, updatedUser.Tenants, professionalTenantID)
		require.Contains(t, updatedUser.Tenants, tenantID)
	})
}

func caseRemoveExtraSelfServeTenantsFromUsers(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	daoFactory := sut.daoFactory
	userDao := daoFactory.GetUserDAO()

	t.Run("Auth checks", func(t *testing.T) {
		// Test with insufficient permissions
		claims := &auth.AugmentClaims{
			TenantID:         "admin-tenant-id",
			TenantName:       "admin-tenant",
			UserID:           "admin-user-id",
			OpaqueUserID:     "admin-user-id",
			OpaqueUserIDType: auth_entities.UserId_INTERNAL_IAP.String(),
			Scope:            []string{tokenexchangeproto.Scope_AUTH_R.String()},
		}
		req := &pb.RemoveExtraSelfServeTenantsFromUsersRequest{}
		ctx := claims.NewContext(context.Background())
		_, err := authServicer.RemoveExtraSelfServeTenantsFromUsers(ctx, req)
		require.Error(t, err)
		require.Equal(t, codes.PermissionDenied, status.Code(err))
	})

	t.Run("DryRun", func(t *testing.T) {
		// Create a user with multiple tenants
		user := &auth_entities.User{
			Id:      "multi-tenant-user-extra",
			Email:   "<EMAIL>",
			Tenants: []string{professionalTenantID2, professionalTenantID},
		}
		_, err := userDao.Create(context.Background(), user)
		require.NoError(t, err)

		// Add user to tenants
		userTenantMappingDAO := daoFactory.GetUserTenantMappingDAO("professional-tenant")
		mapping := &auth_entities.UserTenantMapping{
			UserId: "multi-tenant-user-extra",
			Tenant: "professional-tenant",
		}
		_, err = userTenantMappingDAO.Create(context.Background(), mapping)
		require.NoError(t, err)

		userTenantMappingDAO = daoFactory.GetUserTenantMappingDAO("professional-tenant2")
		mapping = &auth_entities.UserTenantMapping{
			UserId: "multi-tenant-user-extra",
			Tenant: "professional-tenant2",
		}
		_, err = userTenantMappingDAO.Create(context.Background(), mapping)
		require.NoError(t, err)

		ctx := createCentralAuthorizedRequestContext()

		// Run in dry-run mode
		req := &pb.RemoveExtraSelfServeTenantsFromUsersRequest{}
		resp, err := authServicer.RemoveExtraSelfServeTenantsFromUsers(ctx, req)
		require.NoError(t, err)

		// Verify response
		require.NotNil(t, resp)
		require.GreaterOrEqual(t, len(resp.RemovedUsers), 1)

		// Verify user still has both tenants
		updatedUser, err := userDao.Get(context.Background(), "multi-tenant-user-extra")
		require.NoError(t, err)
		require.Contains(t, updatedUser.Tenants, professionalTenantID)
		require.Contains(t, updatedUser.Tenants, professionalTenantID2)
	})

	t.Run("ActualRemoval", func(t *testing.T) {
		ctx := createCentralAuthorizedRequestContext()

		// Create a user with multiple tenants
		user := &auth_entities.User{
			Id:      "multi-tenant-user-extra-2",
			Email:   "<EMAIL>",
			Tenants: []string{professionalTenantID, professionalTenantID2},
		}
		_, err := userDao.Create(ctx, user)
		require.NoError(t, err)

		// Add user to tenants
		userTenantMappingDAO := daoFactory.GetUserTenantMappingDAO("professional-tenant2")
		mapping := &auth_entities.UserTenantMapping{
			UserId: "multi-tenant-user-extra-2",
			Tenant: "professional-tenant2",
		}
		_, err = userTenantMappingDAO.Create(ctx, mapping)
		require.NoError(t, err)

		userTenantMappingDAO = daoFactory.GetUserTenantMappingDAO("professional-tenant")
		mapping = &auth_entities.UserTenantMapping{
			UserId: "multi-tenant-user-extra-2",
			Tenant: "professional-tenant",
		}
		_, err = userTenantMappingDAO.Create(ctx, mapping)
		require.NoError(t, err)

		// Run with actual removal
		req := &pb.RemoveExtraSelfServeTenantsFromUsersRequest{
			MakeChanges: true,
		}
		resp, err := authServicer.RemoveExtraSelfServeTenantsFromUsers(ctx, req)
		require.NoError(t, err)

		// Verify response
		require.NotNil(t, resp)
		require.GreaterOrEqual(t, len(resp.RemovedUsers), 1)

		// Find our test user in the response
		var userRemoval *pb.RemoveExtraSelfServeTenantsFromUsersResponse_UserRemovals
		for _, removal := range resp.RemovedUsers {
			if removal.UserId == "multi-tenant-user-extra-2" {
				userRemoval = removal
				break
			}
		}

		// Verify user was processed
		require.NotNil(t, userRemoval)
		require.Equal(t, "multi-tenant-user-extra-2", userRemoval.UserId)
		require.Equal(t, professionalTenantID, userRemoval.PrimaryTenant.TenantId)
		require.Len(t, userRemoval.RemovedTenants, 1)
		require.Equal(t, professionalTenantID2, userRemoval.RemovedTenants[0].TenantId)

		// Verify user now only has the team tenant
		updatedUser, err := userDao.Get(context.Background(), "multi-tenant-user-extra-2")
		require.NoError(t, err)
		require.NotContains(t, updatedUser.Tenants, professionalTenantID2)
		require.Contains(t, updatedUser.Tenants, professionalTenantID)
	})

	t.Run("WithNamespaceFilter", func(t *testing.T) {
		// Create a user with multiple tenants
		ctx := createCentralAuthorizedRequestContext()

		user := &auth_entities.User{
			Id:      "multi-tenant-user-extra-3",
			Email:   "<EMAIL>",
			Tenants: []string{professionalTenantID, professionalTenantID2},
		}
		_, err := userDao.Create(ctx, user)
		require.NoError(t, err)

		// Add user to tenants
		userTenantMappingDAO := daoFactory.GetUserTenantMappingDAO("professional-tenant2")
		mapping := &auth_entities.UserTenantMapping{
			UserId: "multi-tenant-user-extra-3",
			Tenant: "professional-tenant2",
		}
		_, err = userTenantMappingDAO.Create(ctx, mapping)
		require.NoError(t, err)

		userTenantMappingDAO = daoFactory.GetUserTenantMappingDAO("professional-tenant")
		mapping = &auth_entities.UserTenantMapping{
			UserId: "multi-tenant-user-extra-3",
			Tenant: "professional-tenant",
		}
		_, err = userTenantMappingDAO.Create(ctx, mapping)
		require.NoError(t, err)

		// Run with actual removal
		req := &pb.RemoveExtraSelfServeTenantsFromUsersRequest{
			MakeChanges:  true,
			NamespaceIds: []string{"professional-namespace"},
		}
		resp, err := authServicer.RemoveExtraSelfServeTenantsFromUsers(ctx, req)
		require.NoError(t, err)

		// Verify response
		require.NotNil(t, resp)
		require.GreaterOrEqual(t, len(resp.RemovedUsers), 1)

		// Find our test user in the response
		var userRemoval *pb.RemoveExtraSelfServeTenantsFromUsersResponse_UserRemovals
		for _, removal := range resp.RemovedUsers {
			if removal.UserId == "multi-tenant-user-extra-3" {
				userRemoval = removal
				break
			}
		}

		// Verify user was processed
		require.NotNil(t, userRemoval)
		require.Equal(t, "multi-tenant-user-extra-3", userRemoval.UserId)
		require.Equal(t, professionalTenantID, userRemoval.PrimaryTenant.TenantId)
		require.Len(t, userRemoval.RemovedTenants, 1)
		require.Equal(t, professionalTenantID2, userRemoval.RemovedTenants[0].TenantId)

		// Verify user now only has the team tenant
		updatedUser, err := userDao.Get(context.Background(), "multi-tenant-user-extra-3")
		require.NoError(t, err)
		require.NotContains(t, updatedUser.Tenants, professionalTenantID2)
		require.Contains(t, updatedUser.Tenants, professionalTenantID)
	})
}

func caseRemoveDeletedTenantsFromUsers(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	daoFactory := sut.daoFactory
	userDao := daoFactory.GetUserDAO()

	t.Run("DryRun", func(t *testing.T) {
		// Create a user with multiple tenants
		user := &auth_entities.User{
			Id:      "multi-tenant-user-deleted",
			Email:   "<EMAIL>",
			Tenants: []string{professionalTenantID, deletedTenantID},
		}
		_, err := userDao.Create(context.Background(), user)
		require.NoError(t, err)

		// Add user to tenants
		userTenantMappingDAO := daoFactory.GetUserTenantMappingDAO("professional-tenant")
		mapping := &auth_entities.UserTenantMapping{
			UserId: "multi-tenant-user-deleted",
			Tenant: "professional-tenant",
		}
		_, err = userTenantMappingDAO.Create(context.Background(), mapping)
		require.NoError(t, err)

		userTenantMappingDAO = daoFactory.GetUserTenantMappingDAO("deleted-tenant")
		mapping = &auth_entities.UserTenantMapping{
			UserId: "multi-tenant-user-deleted",
			Tenant: "deleted-tenant",
		}
		_, err = userTenantMappingDAO.Create(context.Background(), mapping)
		require.NoError(t, err)

		ctx := createCentralAuthorizedRequestContext()

		// Run in dry-run mode
		req := &pb.RemoveDeletedTenantsFromUsersRequest{}
		resp, err := authServicer.RemoveDeletedTenantsFromUsers(ctx, req)
		require.NoError(t, err)

		// Verify response
		require.NotNil(t, resp)
		require.GreaterOrEqual(t, len(resp.RemovedUsers), 1)

		// Verify user still has both tenants
		updatedUser, err := userDao.Get(context.Background(), "multi-tenant-user-deleted")
		require.NoError(t, err)
		require.Contains(t, updatedUser.Tenants, professionalTenantID)
		require.Contains(t, updatedUser.Tenants, deletedTenantID)
	})

	t.Run("ActualRemoval", func(t *testing.T) {
		// Create a user with multiple tenants
		user := &auth_entities.User{
			Id:      "multi-tenant-user-deleted-2",
			Email:   "<EMAIL>",
			Tenants: []string{professionalTenantID, deletedTenantID},
		}
		_, err := userDao.Create(context.Background(), user)
		require.NoError(t, err)

		// Add user to tenants
		userTenantMappingDAO := daoFactory.GetUserTenantMappingDAO("professional-tenant")
		mapping := &auth_entities.UserTenantMapping{
			UserId: "multi-tenant-user-deleted-2",
			Tenant: "professional-tenant",
		}
		_, err = userTenantMappingDAO.Create(context.Background(), mapping)
		require.NoError(t, err)

		userTenantMappingDAO = daoFactory.GetUserTenantMappingDAO("deleted-tenant")
		mapping = &auth_entities.UserTenantMapping{
			UserId: "multi-tenant-user-deleted-2",
			Tenant: "deleted-tenant",
		}
		_, err = userTenantMappingDAO.Create(context.Background(), mapping)
		require.NoError(t, err)

		ctx := createCentralAuthorizedRequestContext()

		// Run with actual removal
		req := &pb.RemoveDeletedTenantsFromUsersRequest{
			MakeChanges: true,
		}
		resp, err := authServicer.RemoveDeletedTenantsFromUsers(ctx, req)
		require.NoError(t, err)

		// Verify response
		require.NotNil(t, resp)
		require.GreaterOrEqual(t, len(resp.RemovedUsers), 1)

		// Verify user now only has the team tenant
		updatedUser, err := userDao.Get(context.Background(), "multi-tenant-user-deleted-2")
		require.NoError(t, err)
		require.NotContains(t, updatedUser.Tenants, deletedTenantID)
		require.Contains(t, updatedUser.Tenants, professionalTenantID)
	})

	t.Run("ActualRemovalWithTenantMappings", func(t *testing.T) {
		// Create a user with multiple tenants
		user := &auth_entities.User{
			Id:      "multi-tenant-user-deleted-3",
			Email:   "<EMAIL>",
			Tenants: []string{professionalTenantID, "deleted-tenant-id-2"}, // using a tenant that is not setup in tenant map
		}
		_, err := userDao.Create(context.Background(), user)
		require.NoError(t, err)

		// Add user to tenants
		userTenantMappingDAO := daoFactory.GetUserTenantMappingDAO("professional-tenant")
		mapping := &auth_entities.UserTenantMapping{
			UserId: "multi-tenant-user-deleted-3",
			Tenant: "professional-tenant",
		}
		_, err = userTenantMappingDAO.Create(context.Background(), mapping)
		require.NoError(t, err)

		userTenantMappingDAO = daoFactory.GetUserTenantMappingDAO("deleted-tenant-name-2")
		mapping = &auth_entities.UserTenantMapping{
			UserId: "multi-tenant-user-deleted-3",
			Tenant: "deleted-tenant-name-2",
		}
		_, err = userTenantMappingDAO.Create(context.Background(), mapping)
		require.NoError(t, err)

		ctx := createCentralAuthorizedRequestContext()

		// Run with actual removal
		req := &pb.RemoveDeletedTenantsFromUsersRequest{
			MakeChanges: true,
			TenantMappingsForDeletedTenants: map[string]string{
				"deleted-tenant-id-2": "deleted-tenant-name-2",
			},
		}
		resp, err := authServicer.RemoveDeletedTenantsFromUsers(ctx, req)
		require.NoError(t, err)

		// Verify response
		require.NotNil(t, resp)
		require.GreaterOrEqual(t, len(resp.RemovedUsers), 1)

		// Verify user now only has the team tenant
		updatedUser, err := userDao.Get(context.Background(), "multi-tenant-user-deleted-3")
		require.NoError(t, err)
		require.NotContains(t, updatedUser.Tenants, "deleted-tenant-name-2")
		require.Contains(t, updatedUser.Tenants, professionalTenantID)
	})
}

func TestSetUsersBillingMethodToOrb(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	daoFactory := sut.daoFactory

	userDao := daoFactory.GetUserDAO()

	// Create users with different billing methods
	users := []*auth_entities.User{
		{
			Id:            "user-with-unknown-billing",
			Email:         "<EMAIL>",
			Tenants:       []string{"tenant-id"},
			BillingMethod: auth_entities.BillingMethod_BILLING_METHOD_UNKNOWN,
		},
		{
			Id:            "user-with-stripe-billing",
			Email:         "<EMAIL>",
			Tenants:       []string{"tenant-id"},
			BillingMethod: auth_entities.BillingMethod_BILLING_METHOD_STRIPE,
		},
		{
			Id:            "user-with-orb-billing",
			Email:         "<EMAIL>",
			Tenants:       []string{"tenant-id"},
			BillingMethod: auth_entities.BillingMethod_BILLING_METHOD_ORB,
		},
	}

	// Create the users
	for _, user := range users {
		_, err := userDao.Create(context.Background(), user)
		require.NoError(t, err)
	}

	t.Run("DryRun", func(t *testing.T) {
		ctx := createCentralAuthorizedRequestContext()

		// Run in dry-run mode
		req := &pb.SetUsersBillingMethodToOrbRequest{}
		resp, err := authServicer.SetUsersBillingMethodToOrb(ctx, req)
		require.NoError(t, err)

		// Verify response
		require.NotNil(t, resp)
		require.Equal(t, 2, len(resp.UpdatedUserIds)) // All users except the one already with ORB billing

		// Verify users still have their original billing methods
		updatedUser, err := userDao.Get(context.Background(), "user-with-unknown-billing")
		require.NoError(t, err)
		require.Equal(t, auth_entities.BillingMethod_BILLING_METHOD_UNKNOWN, updatedUser.BillingMethod)

		updatedUser, err = userDao.Get(context.Background(), "user-with-stripe-billing")
		require.NoError(t, err)
		require.Equal(t, auth_entities.BillingMethod_BILLING_METHOD_STRIPE, updatedUser.BillingMethod)
	})

	t.Run("ActualUpdate", func(t *testing.T) {
		ctx := createCentralAuthorizedRequestContext()

		// Run with actual updates
		req := &pb.SetUsersBillingMethodToOrbRequest{
			MakeChanges: true,
		}
		resp, err := authServicer.SetUsersBillingMethodToOrb(ctx, req)
		require.NoError(t, err)

		// Verify response
		require.NotNil(t, resp)
		require.Equal(t, 2, len(resp.UpdatedUserIds)) // All users except the one already with ORB billing

		// Verify users now have ORB billing method
		updatedUser, err := userDao.Get(context.Background(), "user-with-unknown-billing")
		require.NoError(t, err)
		require.Equal(t, auth_entities.BillingMethod_BILLING_METHOD_ORB, updatedUser.BillingMethod)

		updatedUser, err = userDao.Get(context.Background(), "user-with-stripe-billing")
		require.NoError(t, err)
		require.Equal(t, auth_entities.BillingMethod_BILLING_METHOD_ORB, updatedUser.BillingMethod)

		// Verify the user that was already ORB is still ORB
		updatedUser, err = userDao.Get(context.Background(), "user-with-orb-billing")
		require.NoError(t, err)
		require.Equal(t, auth_entities.BillingMethod_BILLING_METHOD_ORB, updatedUser.BillingMethod)
	})

	t.Run("AuthChecks", func(t *testing.T) {
		// Test with insufficient permissions
		claims := &auth.AugmentClaims{
			TenantID:         "admin-tenant-id",
			TenantName:       "admin-tenant",
			UserID:           "admin-user-id",
			OpaqueUserID:     "admin-user-id",
			OpaqueUserIDType: auth_entities.UserId_INTERNAL_IAP.String(),
			Scope:            []string{tokenexchangeproto.Scope_AUTH_R.String()},
		}
		req := &pb.SetUsersBillingMethodToOrbRequest{}
		ctx := claims.NewContext(context.Background())
		_, err := authServicer.SetUsersBillingMethodToOrb(ctx, req)
		require.Error(t, err)
		require.Equal(t, codes.PermissionDenied, status.Code(err))
	})
}

func caseGetUsers(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createAuthServicerSUT(t, bigtableFixture)

	t.Run("standard context should fail", func(t *testing.T) {
		ctx := createAuthorizedRequestContext()
		_, err := sut.authServicer.GetUsers(ctx, &pb.GetUsersRequest{})
		require.Error(t, err)
	})

	ctx := createCentralAuthorizedRequestContext()

	// Create users
	users := make([]*auth_entities.User, 0)
	for i := 0; i < 9; i++ {
		users = append(users, sut.createUser(t, createUserOpts{}))
	}
	fred := sut.createUser(t, createUserOpts{email: "<EMAIL>"})
	users = append(users, fred)

	userIDs := make([]string, 0, len(users))
	for _, user := range users {
		userIDs = append(userIDs, user.Id)
	}
	slices.Sort(userIDs)

	// Get all users
	cases := []struct {
		name          string
		request       *pb.GetUsersRequest
		expectedUsers []string
	}{
		{
			name: "All users",
			request: &pb.GetUsersRequest{
				PageSize: 10,
			},
			expectedUsers: userIDs,
		},
		{
			name: "Five at a time",
			request: &pb.GetUsersRequest{
				PageSize: 5,
			},
			expectedUsers: userIDs,
		},
		{
			name:          "No page size requested",
			request:       &pb.GetUsersRequest{},
			expectedUsers: userIDs,
		},
		{
			name: "Search for FRED",
			request: &pb.GetUsersRequest{
				PageSize:     10,
				SearchString: "fred",
			},
			expectedUsers: []string{fred.Id},
		},
		{
			name: "Search for fred",
			request: &pb.GetUsersRequest{
				PageSize:     10,
				SearchString: "FRED",
			},
			expectedUsers: []string{fred.Id},
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			request := tc.request

			var nextPageToken *string = nil
			users := make([]*auth_entities.User, 0, len(tc.expectedUsers))
			for {
				if nextPageToken != nil {
					request.PageToken = *nextPageToken
				}
				response, err := sut.authServicer.GetUsers(ctx, request)
				require.NoError(t, err)
				for _, user := range response.Users {
					users = append(users, user)
				}
				if response.NextPageToken == "" {
					break
				}
				nextPageToken = &response.NextPageToken
			}
			require.Len(t, users, len(tc.expectedUsers))
			for idx, user := range users {
				require.Equal(t, tc.expectedUsers[idx], user.Id)
			}
		})
	}

	request := &pb.GetUsersRequest{
		PageSize: 10,
	}
	response, err := sut.authServicer.GetUsers(ctx, request)
	require.NoError(t, err)
	require.Len(t, response.Users, 10)
	require.Empty(t, response.NextPageToken)
	for idx, user := range response.Users {
		require.Equal(t, userIDs[idx], user.Id)
	}

	// Get users with a search string
}

func TestGetOrbSubscriptionTypeWithNilSubscription(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	daoFactory := sut.daoFactory

	ctx := createAuthorizedRequestContext()

	// Create a user with Orb billing method and a subscription ID that doesn't exist in the database
	userDao := daoFactory.GetUserDAO()
	user := &auth_entities.User{
		Id:                "test-orb-user-id",
		Email:             "<EMAIL>",
		Tenants:           []string{professionalTenantID},
		BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
		OrbCustomerId:     "orb-customer-123",
		OrbSubscriptionId: "non-existent-orb-subscription-id", // This ID doesn't exist in the database
	}
	_, err := userDao.Create(ctx, user)
	require.NoError(t, err)

	// Create user tenant mapping
	userTenantMappingDAO := daoFactory.GetUserTenantMappingDAO("professional-tenant")
	userMapping := &auth_entities.UserTenantMapping{
		UserId: "test-orb-user-id",
		Tenant: "professional-tenant",
	}
	_, err = userTenantMappingDAO.Create(ctx, userMapping)
	require.NoError(t, err)

	// Create a token hash for the user
	tokenHashDAO := daoFactory.GetTokenHashDAO()
	tokenHash := &auth_entities.TokenHash{
		Hash:          "2cf24dba5fb0a30e26e83b2ac5b9e29e1b161e5c1fa7425e73043362938b9824", // sha256 of "hello"
		AugmentUserId: "test-orb-user-id",
		EmailAddress:  "<EMAIL>",
		TenantId:      professionalTenantID,
	}
	_, err = tokenHashDAO.Create(ctx, tokenHash)
	require.NoError(t, err)

	// Call GetTokenInfo - this should not crash even though the subscription doesn't exist
	request := &pb.GetTokenInfoRequest{Token: "hello"}
	response, err := authServicer.GetTokenInfo(ctx, request)

	// Verify the call succeeds and returns a response
	require.NoError(t, err)
	require.NotNil(t, response)

	// With our fix, we should get an active subscription when subscription is nil
	// The code treats a nil subscription as active with a future end date
	activeSub, ok := response.Subscription.(*pb.GetTokenInfoResponse_ActiveSubscription)
	require.True(t, ok, "Expected active subscription but got %T", response.Subscription)
	require.NotNil(t, activeSub.ActiveSubscription)
	require.NotNil(t, activeSub.ActiveSubscription.EndDate, "EndDate should not be nil")
	// EndDate should be in the future (14 days from now)
	require.True(t, time.Now().Add(13*24*time.Hour).Before(activeSub.ActiveSubscription.EndDate.AsTime()),
		"EndDate should be at least 13 days in the future")
}

func TestGetOrbSubscriptionTypeWithPendingSubscription(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	daoFactory := sut.daoFactory

	ctx := createAuthorizedRequestContext()

	// Create a user with Orb billing method, no orb subscription id, and a pending creation
	userDao := daoFactory.GetUserDAO()
	user := &auth_entities.User{
		Id:            "test-orb-user-id",
		Email:         "<EMAIL>",
		Tenants:       []string{professionalTenantID},
		BillingMethod: auth_entities.BillingMethod_BILLING_METHOD_ORB,
		SubscriptionCreationInfo: &auth_entities.User_SubscriptionCreationInfo{
			Status:    auth_entities.User_SubscriptionCreationInfo_PENDING,
			Id:        "test-subscription-creation",
			CreatedAt: timestamppb.Now(),
			UpdatedAt: timestamppb.Now(),
		},
	}
	_, err := userDao.Create(ctx, user)
	require.NoError(t, err)

	// Create user tenant mapping
	userTenantMappingDAO := daoFactory.GetUserTenantMappingDAO("professional-tenant")
	userMapping := &auth_entities.UserTenantMapping{
		UserId: "test-orb-user-id",
		Tenant: "professional-tenant",
	}
	_, err = userTenantMappingDAO.Create(ctx, userMapping)
	require.NoError(t, err)

	// Create a token hash for the user
	tokenHashDAO := daoFactory.GetTokenHashDAO()
	tokenHash := &auth_entities.TokenHash{
		Hash:          "2cf24dba5fb0a30e26e83b2ac5b9e29e1b161e5c1fa7425e73043362938b9824", // sha256 of "hello"
		AugmentUserId: "test-orb-user-id",
		EmailAddress:  "<EMAIL>",
		TenantId:      professionalTenantID,
	}
	_, err = tokenHashDAO.Create(ctx, tokenHash)
	require.NoError(t, err)

	// Call GetTokenInfo - this should not crash even though the subscription doesn't exist yet
	request := &pb.GetTokenInfoRequest{Token: "hello"}
	response, err := authServicer.GetTokenInfo(ctx, request)

	// Verify the call succeeds and returns a response
	require.NoError(t, err)
	require.NotNil(t, response)

	// With our fix, we should get an active subscription when subscription is nil
	// The code treats a nil subscription as active with a future end date
	activeSub, ok := response.Subscription.(*pb.GetTokenInfoResponse_ActiveSubscription)
	require.True(t, ok, "Expected active subscription but got %T", response.Subscription)
	require.NotNil(t, activeSub.ActiveSubscription)
	require.Nil(t, activeSub.ActiveSubscription.EndDate)
}

func caseTestDeduplicateUsersByEmail(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	// Inject a mock orb client
	mockOrbClient := orb.NewMockOrbClient()
	authServicer.orbClient = mockOrbClient
	userDAO := sut.daoFactory.GetUserDAO()

	ctx := createCentralAuthorizedRequestContext()

	t.Run("DryRun - No duplicates", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Create a single user with unique email
		user := &auth_entities.User{
			Id:      "unique-user-1",
			Email:   "<EMAIL>",
			Tenants: []string{tenantID},
		}
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		// Run deduplication in dry-run mode
		resp, err := authServicer.DeduplicateUsersByEmail(ctx, &pb.DeduplicateUsersByEmailRequest{
			MakeChanges: false,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Empty(t, resp.UserMerges, "Should have no merges when no duplicates exist")
	})

	t.Run("DryRun - Simple duplicate case", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Create two users with the same email
		user1 := &auth_entities.User{
			Id:      "duplicate-user-1",
			Email:   "<EMAIL>",
			Tenants: []string{tenantID},
		}
		user2 := &auth_entities.User{
			Id:      "duplicate-user-2",
			Email:   "<EMAIL>",
			Tenants: []string{tenantID},
		}

		_, err := userDAO.Create(ctx, user1)
		require.NoError(t, err)
		_, err = userDAO.Create(ctx, user2)
		require.NoError(t, err)

		// Add users to tenants
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO(tenantName)
		mapping1 := &auth_entities.UserTenantMapping{
			UserId: "duplicate-user-1",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping1)
		require.NoError(t, err)

		mapping2 := &auth_entities.UserTenantMapping{
			UserId: "duplicate-user-2",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping2)
		require.NoError(t, err)

		// Run deduplication in dry-run mode
		resp, err := authServicer.DeduplicateUsersByEmail(ctx, &pb.DeduplicateUsersByEmailRequest{
			MakeChanges: false,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Len(t, resp.UserMerges, 1, "Should have one merge for duplicate email")

		merge := resp.UserMerges[0]
		assert.Equal(t, "<EMAIL>", merge.Email)
		assert.Equal(t, "duplicate-user-1", merge.PrimaryUserId, "Primary user should be the one with lowest ID")
		assert.Contains(t, merge.MergedUserIds, "duplicate-user-2", "MergedUserIds should contain the duplicate user ID")
		assert.Equal(t, "duplicate-user-1", merge.SubscriptionSourceUserId, "Should use primary user as subscription source when no subscriptions")

		// Verify users still exist (dry-run shouldn't change anything)
		_, err = userDAO.Get(ctx, "duplicate-user-1")
		require.NoError(t, err)
		_, err = userDAO.Get(ctx, "duplicate-user-2")
		require.NoError(t, err)
	})

	t.Run("DryRun - Case insensitive email matching", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Create users with same email but different cases
		user1 := &auth_entities.User{
			Id:      "case-user-1",
			Email:   "<EMAIL>",
			Tenants: []string{tenantID},
		}
		user2 := &auth_entities.User{
			Id:      "case-user-2",
			Email:   "<EMAIL>",
			Tenants: []string{tenantID},
		}

		_, err := userDAO.Create(ctx, user1)
		require.NoError(t, err)
		_, err = userDAO.Create(ctx, user2)
		require.NoError(t, err)

		// Add users to tenants
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO(tenantName)
		mapping1 := &auth_entities.UserTenantMapping{
			UserId: "case-user-1",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping1)
		require.NoError(t, err)

		mapping2 := &auth_entities.UserTenantMapping{
			UserId: "case-user-2",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping2)
		require.NoError(t, err)

		// Run deduplication in dry-run mode
		resp, err := authServicer.DeduplicateUsersByEmail(ctx, &pb.DeduplicateUsersByEmailRequest{
			MakeChanges: false,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Len(t, resp.UserMerges, 1, "Should merge case-insensitive duplicate emails")

		merge := resp.UserMerges[0]
		assert.Equal(t, "<EMAIL>", merge.Email, "Email should be normalized to lowercase")
		assert.Equal(t, "case-user-1", merge.PrimaryUserId)
		assert.Contains(t, merge.MergedUserIds, "case-user-2", "MergedUserIds should contain the duplicate user ID")
	})
}

func caseTestDeduplicateUsersByEmailActualMerge(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	// Inject a mock orb client
	mockOrbClient := orb.NewMockOrbClient()
	authServicer.orbClient = mockOrbClient
	userDAO := sut.daoFactory.GetUserDAO()

	ctx := createCentralAuthorizedRequestContext()

	t.Run("Actual merge - Simple case", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Create two users with the same email
		user1 := &auth_entities.User{
			Id:      "merge-user-1",
			Email:   "<EMAIL>",
			Tenants: []string{tenantID},
		}
		user2 := &auth_entities.User{
			Id:      "merge-user-2",
			Email:   "<EMAIL>",
			Tenants: []string{tenantID},
		}

		_, err := userDAO.Create(ctx, user1)
		require.NoError(t, err)
		_, err = userDAO.Create(ctx, user2)
		require.NoError(t, err)

		// add users to tenants
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO(tenantName)
		mapping1 := &auth_entities.UserTenantMapping{
			UserId: "merge-user-1",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping1)
		require.NoError(t, err)

		mapping2 := &auth_entities.UserTenantMapping{
			UserId: "merge-user-2",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping2)
		require.NoError(t, err)

		// Run deduplication with actual changes
		resp, err := authServicer.DeduplicateUsersByEmail(ctx, &pb.DeduplicateUsersByEmailRequest{
			MakeChanges: true,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Len(t, resp.UserMerges, 1)

		merge := resp.UserMerges[0]
		assert.Equal(t, "<EMAIL>", merge.Email)
		assert.Equal(t, "merge-user-1", merge.PrimaryUserId)
		assert.Contains(t, merge.MergedUserIds, "merge-user-2")

		// Verify primary user still exists
		primaryUser, err := userDAO.Get(ctx, "merge-user-1")
		require.NoError(t, err)
		assert.Equal(t, "<EMAIL>", primaryUser.Email)

		// Verify merged user was deleted (has no tenants)
		mergedUser, err := userDAO.Get(ctx, "merge-user-2")
		require.NoError(t, err)
		assert.Equal(t, 0, len(mergedUser.Tenants))
	})

	t.Run("Actual merge - Users with no tenants are skipped", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Create users - one with tenants, one without
		user1 := &auth_entities.User{
			Id:      "no-tenant-user-1",
			Email:   "<EMAIL>",
			Tenants: []string{tenantID},
		}
		user2 := &auth_entities.User{
			Id:      "no-tenant-user-2",
			Email:   "<EMAIL>",
			Tenants: nil, // No tenants - should be skipped
		}

		_, err := userDAO.Create(ctx, user1)
		require.NoError(t, err)
		_, err = userDAO.Create(ctx, user2)
		require.NoError(t, err)

		// Add user with tenants to tenant
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO(tenantName)
		mapping1 := &auth_entities.UserTenantMapping{
			UserId: "no-tenant-user-1",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping1)
		require.NoError(t, err)

		// Run deduplication with actual changes
		resp, err := authServicer.DeduplicateUsersByEmail(ctx, &pb.DeduplicateUsersByEmailRequest{
			MakeChanges: true,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Empty(t, resp.UserMerges, "Should have no merges when one user has no tenants")

		// Verify both users still exist
		_, err = userDAO.Get(ctx, "no-tenant-user-1")
		require.NoError(t, err)
		_, err = userDAO.Get(ctx, "no-tenant-user-2")
		require.NoError(t, err)
	})
}

func caseTestDeduplicateUsersByEmailComplexScenarios(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	// Inject a mock orb client
	mockOrbClient := orb.NewMockOrbClient()
	authServicer.orbClient = mockOrbClient
	userDAO := sut.daoFactory.GetUserDAO()

	ctx := createCentralAuthorizedRequestContext()

	t.Run("Users with different tenants", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Create users with same email but different tenants
		user1 := &auth_entities.User{
			Id:      "tenant-user-1",
			Email:   "<EMAIL>",
			Tenants: []string{tenantID},
		}
		user2 := &auth_entities.User{
			Id:      "tenant-user-2",
			Email:   "<EMAIL>",
			Tenants: []string{professionalTenantID},
		}

		_, err := userDAO.Create(ctx, user1)
		require.NoError(t, err)
		_, err = userDAO.Create(ctx, user2)
		require.NoError(t, err)

		// Create tenant mappings
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO(tenantName)
		mapping1 := &auth_entities.UserTenantMapping{
			UserId: "tenant-user-1",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping1)
		require.NoError(t, err)

		tenantMappingDAO = sut.daoFactory.GetUserTenantMappingDAO("professional-tenant")
		mapping2 := &auth_entities.UserTenantMapping{
			UserId: "tenant-user-2",
			Tenant: "professional-tenant",
		}
		_, err = tenantMappingDAO.Create(ctx, mapping2)
		require.NoError(t, err)

		// Run deduplication in dry-run mode
		resp, err := authServicer.DeduplicateUsersByEmail(ctx, &pb.DeduplicateUsersByEmailRequest{
			MakeChanges: false,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Len(t, resp.UserMerges, 1)

		merge := resp.UserMerges[0]
		assert.Equal(t, "<EMAIL>", merge.Email)
		assert.Equal(t, "tenant-user-1", merge.PrimaryUserId)
		assert.Contains(t, merge.MergedUserIds, "tenant-user-2")
		// Should have tenant information
		assert.NotEmpty(t, merge.PrimaryUserTenant)
		assert.NotEmpty(t, merge.MergedUserTenants)
	})

	t.Run("Whitespace and email normalization", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Create users with emails that have whitespace and different cases
		user1 := &auth_entities.User{
			Id:      "whitespace-user-1",
			Email:   "  <EMAIL>  ",
			Tenants: []string{tenantID},
		}
		user2 := &auth_entities.User{
			Id:      "whitespace-user-2",
			Email:   "<EMAIL>",
			Tenants: []string{tenantID},
		}

		_, err := userDAO.Create(ctx, user1)
		require.NoError(t, err)
		_, err = userDAO.Create(ctx, user2)
		require.NoError(t, err)

		// Add users to tenants
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO(tenantName)
		mapping1 := &auth_entities.UserTenantMapping{
			UserId: "whitespace-user-1",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping1)
		require.NoError(t, err)

		mapping2 := &auth_entities.UserTenantMapping{
			UserId: "whitespace-user-2",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping2)
		require.NoError(t, err)

		// Run deduplication in dry-run mode
		resp, err := authServicer.DeduplicateUsersByEmail(ctx, &pb.DeduplicateUsersByEmailRequest{
			MakeChanges: false,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Len(t, resp.UserMerges, 1)

		merge := resp.UserMerges[0]
		assert.Equal(t, "<EMAIL>", merge.Email, "Email should be normalized (trimmed and lowercased)")
		assert.Equal(t, "whitespace-user-1", merge.PrimaryUserId)
		assert.Contains(t, merge.MergedUserIds, "whitespace-user-2")
	})

	t.Run("Actual merge with subscription transfer from higher ID to lower ID", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Create users with different subscription information
		user1 := &auth_entities.User{
			Id:                "merge-sub-user-1",
			Email:             "<EMAIL>",
			Tenants:           []string{tenantID},
			BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
			OrbCustomerId:     "orb-customer-merge-1",
			OrbSubscriptionId: "orb-sub-merge-1",
		}
		user2 := &auth_entities.User{
			Id:                "merge-sub-user-2",
			Email:             "<EMAIL>",
			Tenants:           []string{tenantID},
			BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
			OrbCustomerId:     "orb-customer-merge-2",
			OrbSubscriptionId: "orb-sub-merge-2",
		}

		_, err := userDAO.Create(ctx, user1)
		require.NoError(t, err)
		_, err = userDAO.Create(ctx, user2)
		require.NoError(t, err)

		// Add users to tenants
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO(tenantName)
		mapping1 := &auth_entities.UserTenantMapping{
			UserId: "merge-sub-user-1",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping1)
		require.NoError(t, err)

		mapping2 := &auth_entities.UserTenantMapping{
			UserId: "merge-sub-user-2",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping2)
		require.NoError(t, err)

		// User 1 has a worse subscription
		mockOrbClient.On("GetUserSubscription", mock.Anything, "orb-sub-merge-1", mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: "orb-sub-merge-1",
			ExternalPlanID:    "orb_community_plan",
			OrbStatus:         "active",
		}, nil)

		// User 2 has a better subscription
		mockOrbClient.On("GetUserSubscription", mock.Anything, "orb-sub-merge-2", mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: "orb-sub-merge-2",
			ExternalPlanID:    "orb_developer_plan",
			OrbStatus:         "active",
		}, nil)

		// Update the metadata of the "winning" subscription to point to the new primary user
		mockOrbClient.On("UpdateCustomerMetadata", mock.Anything, "orb-customer-merge-2", map[string]string{
			"augment_user_id": "merge-sub-user-1",
		}).Return(nil)

		// Cancel the worse subscription
		mockOrbClient.On("CancelOrbSubscription", mock.Anything, "orb-sub-merge-1", orb.PlanChangeImmediate, mock.Anything, mock.Anything).Return(nil)

		// Run deduplication with actual changes
		resp, err := authServicer.DeduplicateUsersByEmail(ctx, &pb.DeduplicateUsersByEmailRequest{
			MakeChanges: true,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Len(t, resp.UserMerges, 1)

		merge := resp.UserMerges[0]
		assert.Equal(t, "<EMAIL>", merge.Email)
		assert.Equal(t, "merge-sub-user-1", merge.PrimaryUserId)
		assert.Contains(t, merge.MergedUserIds, "merge-sub-user-2")
		assert.Equal(t, "merge-sub-user-2", merge.SubscriptionSourceUserId)

		// Verify primary user now has the subscription information
		primaryUser, err := userDAO.Get(ctx, "merge-sub-user-1")
		require.NoError(t, err)
		assert.Equal(t, "orb-customer-merge-2", primaryUser.OrbCustomerId)
		assert.Equal(t, "orb-sub-merge-2", primaryUser.OrbSubscriptionId)

		// Verify merged user was deleted (has no tenants)
		mergedUser, err := userDAO.Get(ctx, "merge-sub-user-2")
		require.NoError(t, err)
		assert.Equal(t, 0, len(mergedUser.Tenants))
		mockOrbClient.AssertExpectations(t)
	})
	t.Run("Actual merge with active community and inactive developer", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Create users with different subscription information
		user1 := &auth_entities.User{
			Id:                "merge-sub-user-3",
			Email:             "<EMAIL>",
			Tenants:           []string{tenantID},
			BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
			OrbCustomerId:     "orb-customer-merge-3",
			OrbSubscriptionId: "orb-sub-merge-3",
		}
		user2 := &auth_entities.User{
			Id:                "merge-sub-user-4",
			Email:             "<EMAIL>",
			Tenants:           []string{tenantID},
			BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
			OrbCustomerId:     "orb-customer-merge-4",
			OrbSubscriptionId: "orb-sub-merge-4",
		}

		_, err := userDAO.Create(ctx, user1)
		require.NoError(t, err)
		_, err = userDAO.Create(ctx, user2)
		require.NoError(t, err)

		// Add users to tenants
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO(tenantName)
		mapping1 := &auth_entities.UserTenantMapping{
			UserId: "merge-sub-user-3",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping1)
		require.NoError(t, err)

		mapping2 := &auth_entities.UserTenantMapping{
			UserId: "merge-sub-user-4",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping2)
		require.NoError(t, err)

		// User 1 has a worse subscription - inactive developer
		mockOrbClient.On("GetUserSubscription", mock.Anything, "orb-sub-merge-3", mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: "orb-sub-merge-3",
			ExternalPlanID:    "orb_developer_plan",
			OrbStatus:         "ended",
		}, nil)

		// User 2 has a better subscription - active community
		mockOrbClient.On("GetUserSubscription", mock.Anything, "orb-sub-merge-4", mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: "orb-sub-merge-4",
			ExternalPlanID:    "orb_community_plan",
			OrbStatus:         "active",
		}, nil)

		// Update the metadata of the "winning" subscription to point to the new primary user
		mockOrbClient.On("UpdateCustomerMetadata", mock.Anything, "orb-customer-merge-4", map[string]string{
			"augment_user_id": "merge-sub-user-3",
		}).Return(nil)

		// the worse subscription is already ended, so no need to cancel it

		// Run deduplication with actual changes
		resp, err := authServicer.DeduplicateUsersByEmail(ctx, &pb.DeduplicateUsersByEmailRequest{
			MakeChanges: true,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Len(t, resp.UserMerges, 1)

		merge := resp.UserMerges[0]
		assert.Equal(t, "<EMAIL>", merge.Email)
		assert.Equal(t, "merge-sub-user-3", merge.PrimaryUserId)
		assert.Contains(t, merge.MergedUserIds, "merge-sub-user-4")
		assert.Equal(t, "merge-sub-user-4", merge.SubscriptionSourceUserId)

		// Verify primary user now has the subscription information
		primaryUser, err := userDAO.Get(ctx, "merge-sub-user-3")
		require.NoError(t, err)
		assert.Equal(t, "orb-customer-merge-4", primaryUser.OrbCustomerId)
		assert.Equal(t, "orb-sub-merge-4", primaryUser.OrbSubscriptionId)

		// Verify merged user was deleted (has no tenants)
		mergedUser, err := userDAO.Get(ctx, "merge-sub-user-4")
		require.NoError(t, err)
		assert.Equal(t, 0, len(mergedUser.Tenants))
		mockOrbClient.AssertExpectations(t)
	})
	t.Run("Actual merge with active developer and trial - first user already has winning subscription", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Create users with different subscription information
		user1 := &auth_entities.User{
			Id:                "merge-sub-user-5",
			Email:             "<EMAIL>",
			Tenants:           []string{tenantID},
			BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
			OrbCustomerId:     "orb-customer-merge-5",
			OrbSubscriptionId: "orb-sub-merge-5",
		}
		user2 := &auth_entities.User{
			Id:                "merge-sub-user-6",
			Email:             "<EMAIL>",
			Tenants:           []string{tenantID},
			BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
			OrbCustomerId:     "orb-customer-merge-6",
			OrbSubscriptionId: "orb-sub-merge-6",
		}

		_, err := userDAO.Create(ctx, user1)
		require.NoError(t, err)
		_, err = userDAO.Create(ctx, user2)
		require.NoError(t, err)

		// Add users to tenants
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO(tenantName)
		mapping1 := &auth_entities.UserTenantMapping{
			UserId: "merge-sub-user-5",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping1)
		require.NoError(t, err)

		mapping2 := &auth_entities.UserTenantMapping{
			UserId: "merge-sub-user-6",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping2)
		require.NoError(t, err)

		// User 1 has a winning subscription - active developer
		mockOrbClient.On("GetUserSubscription", mock.Anything, "orb-sub-merge-5", mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: "orb-sub-merge-5",
			ExternalPlanID:    "orb_developer_plan",
			OrbStatus:         "active",
		}, nil)

		// User 2 has a worse subscription - trial
		mockOrbClient.On("GetUserSubscription", mock.Anything, "orb-sub-merge-6", mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: "orb-sub-merge-6",
			ExternalPlanID:    "orb_trial_plan",
			OrbStatus:         "active",
		}, nil)

		// Update the metadata of the "winning" subscription to point to the new primary user
		mockOrbClient.On("UpdateCustomerMetadata", mock.Anything, "orb-customer-merge-5", map[string]string{
			"augment_user_id": "merge-sub-user-5",
		}).Return(nil)

		// Cancel the worse subscription
		mockOrbClient.On("CancelOrbSubscription", mock.Anything, "orb-sub-merge-6", orb.PlanChangeImmediate, mock.Anything, mock.Anything).Return(nil)

		// Run deduplication with actual changes
		resp, err := authServicer.DeduplicateUsersByEmail(ctx, &pb.DeduplicateUsersByEmailRequest{
			MakeChanges: true,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Len(t, resp.UserMerges, 1)

		merge := resp.UserMerges[0]
		assert.Equal(t, "<EMAIL>", merge.Email)
		assert.Equal(t, "merge-sub-user-5", merge.PrimaryUserId)
		assert.Contains(t, merge.MergedUserIds, "merge-sub-user-6")
		assert.Equal(t, "merge-sub-user-5", merge.SubscriptionSourceUserId)

		// Verify primary user now has the subscription information
		primaryUser, err := userDAO.Get(ctx, "merge-sub-user-5")
		require.NoError(t, err)
		assert.Equal(t, "orb-customer-merge-5", primaryUser.OrbCustomerId)
		assert.Equal(t, "orb-sub-merge-5", primaryUser.OrbSubscriptionId)

		// Verify merged user was deleted (has no tenants)
		mergedUser, err := userDAO.Get(ctx, "merge-sub-user-6")
		require.NoError(t, err)
		assert.Equal(t, 0, len(mergedUser.Tenants))

		mockOrbClient.AssertExpectations(t)
	})
}

func caseTestDeduplicateUsersByIdpUserIdComplexScenarios(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	// Inject a mock orb client
	mockOrbClient := orb.NewMockOrbClient()
	authServicer.orbClient = mockOrbClient
	userDAO := sut.daoFactory.GetUserDAO()

	ctx := createCentralAuthorizedRequestContext()

	t.Run("Transitive connection - A-B-C chain", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// User A has IDP IDs [1, 2]
		userA := &auth_entities.User{
			Id:         "user-a",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"idp-1", "idp-2"},
			Tenants:    []string{tenantID},
		}
		// User B has IDP IDs [2, 3] - shares idp-2 with A
		userB := &auth_entities.User{
			Id:         "user-b",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"idp-2", "idp-3"},
			Tenants:    []string{tenantID},
		}
		// User C has IDP IDs [3, 4] - shares idp-3 with B
		userC := &auth_entities.User{
			Id:         "user-c",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"idp-3", "idp-4"},
			Tenants:    []string{tenantID},
		}

		_, err := userDAO.Create(ctx, userA)
		require.NoError(t, err)
		_, err = userDAO.Create(ctx, userB)
		require.NoError(t, err)
		_, err = userDAO.Create(ctx, userC)
		require.NoError(t, err)

		// Run deduplication in dry-run mode
		resp, err := authServicer.DeduplicateUsersByIdpUserId(ctx, &pb.DeduplicateUsersByIdpUserIdRequest{
			MakeChanges: false,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Len(t, resp.UserMerges, 1, "Should merge all three users into one group")

		merge := resp.UserMerges[0]
		assert.Equal(t, "user-a", merge.PrimaryUserId, "Primary user should be the one with lowest ID")
		assert.Contains(t, merge.MergedUserIds, "user-b")
		assert.Contains(t, merge.MergedUserIds, "user-c")
		assert.Len(t, merge.MergedUserIds, 2)

		// verify that the merge includes all IDP user IDs
		assert.Len(t, merge.IdpUserIds, 4)
		for _, idpUserId := range []string{"idp-1", "idp-2", "idp-3", "idp-4"} {
			assert.Contains(t, merge.IdpUserIds, idpUserId)
		}
	})

	t.Run("Multiple separate groups", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Group 1: Users D and E share idp-5
		userD := &auth_entities.User{
			Id:         "user-d",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"idp-5"},
			Tenants:    []string{tenantID},
		}
		userE := &auth_entities.User{
			Id:         "user-e",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"idp-5"},
			Tenants:    []string{tenantID},
		}

		// Group 2: Users F and G share idp-6 (separate from group 1)
		userF := &auth_entities.User{
			Id:         "user-f",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"idp-6"},
			Tenants:    []string{tenantID},
		}
		userG := &auth_entities.User{
			Id:         "user-g",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"idp-6"},
			Tenants:    []string{tenantID},
		}

		for _, user := range []*auth_entities.User{userD, userE, userF, userG} {
			_, err := userDAO.Create(ctx, user)
			require.NoError(t, err)
		}

		// Run deduplication in dry-run mode
		resp, err := authServicer.DeduplicateUsersByIdpUserId(ctx, &pb.DeduplicateUsersByIdpUserIdRequest{
			MakeChanges: false,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Len(t, resp.UserMerges, 2, "Should have two separate merge groups")

		// Verify both groups exist
		var group1, group2 *pb.DeduplicateUsersByEmailResponse_UserMerge
		for _, merge := range resp.UserMerges {
			if merge.PrimaryUserId == "user-d" {
				group1 = merge
			} else if merge.PrimaryUserId == "user-f" {
				group2 = merge
			}
		}

		require.NotNil(t, group1, "Should have group with user-d as primary")
		require.NotNil(t, group2, "Should have group with user-f as primary")

		assert.Contains(t, group1.MergedUserIds, "user-e")
		assert.Contains(t, group2.MergedUserIds, "user-g")

		assert.Len(t, group1.IdpUserIds, 1)
		assert.Contains(t, group1.IdpUserIds, "idp-5")
		assert.Len(t, group2.IdpUserIds, 1)
		assert.Contains(t, group2.IdpUserIds, "idp-6")
	})

	t.Run("User with multiple IDP IDs but no duplicates", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// User with multiple unique IDP IDs
		userH := &auth_entities.User{
			Id:         "user-h",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"idp-unique-1", "idp-unique-2", "idp-unique-3"},
			Tenants:    []string{tenantID},
		}

		_, err := userDAO.Create(ctx, userH)
		require.NoError(t, err)

		// Run deduplication in dry-run mode
		resp, err := authServicer.DeduplicateUsersByIdpUserId(ctx, &pb.DeduplicateUsersByIdpUserIdRequest{
			MakeChanges: false,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Empty(t, resp.UserMerges, "Should have no merges when no duplicates exist")
	})

	t.Run("Complex diamond pattern - A connects to B and C, B and C connect to D", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Diamond pattern:
		// A shares idp-10 with B, idp-11 with C
		// B shares idp-12 with D
		// C shares idp-13 with D
		// All should be merged together

		userA := &auth_entities.User{
			Id:         "diamond-a",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"idp-10", "idp-11"},
			Tenants:    []string{tenantID},
		}
		userB := &auth_entities.User{
			Id:         "diamond-b",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"idp-10", "idp-12"},
			Tenants:    []string{tenantID},
		}
		userC := &auth_entities.User{
			Id:         "diamond-c",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"idp-11", "idp-13"},
			Tenants:    []string{tenantID},
		}
		userD := &auth_entities.User{
			Id:         "diamond-d",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"idp-12", "idp-13"},
			Tenants:    []string{tenantID},
		}

		for _, user := range []*auth_entities.User{userA, userB, userC, userD} {
			_, err := userDAO.Create(ctx, user)
			require.NoError(t, err)
		}

		// Run deduplication in dry-run mode
		resp, err := authServicer.DeduplicateUsersByIdpUserId(ctx, &pb.DeduplicateUsersByIdpUserIdRequest{
			MakeChanges: false,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Len(t, resp.UserMerges, 1, "Should merge all four users into one group")

		merge := resp.UserMerges[0]
		assert.Equal(t, "diamond-a", merge.PrimaryUserId, "Primary user should be the one with lowest ID")
		assert.Contains(t, merge.MergedUserIds, "diamond-b")
		assert.Contains(t, merge.MergedUserIds, "diamond-c")
		assert.Contains(t, merge.MergedUserIds, "diamond-d")
		assert.Len(t, merge.MergedUserIds, 3)
	})
}

func caseTestDeduplicateUsersByIdpUserIdActualMerge(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	// Inject a mock orb client
	mockOrbClient := orb.NewMockOrbClient()
	authServicer.orbClient = mockOrbClient
	userDAO := sut.daoFactory.GetUserDAO()

	ctx := createCentralAuthorizedRequestContext()

	t.Run("Simple merge - Two users sharing one IDP ID", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Two users sharing the same IDP user ID
		user1 := &auth_entities.User{
			Id:         "simple-user-1",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"shared-idp-1"},
			Tenants:    []string{tenantID},
		}
		user2 := &auth_entities.User{
			Id:         "simple-user-2",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"shared-idp-1"},
			Tenants:    []string{tenantID},
		}

		_, err := userDAO.Create(ctx, user1)
		require.NoError(t, err)
		_, err = userDAO.Create(ctx, user2)
		require.NoError(t, err)

		// Add users to tenants
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO(tenantName)
		mapping1 := &auth_entities.UserTenantMapping{
			UserId: "simple-user-1",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping1)
		require.NoError(t, err)

		mapping2 := &auth_entities.UserTenantMapping{
			UserId: "simple-user-2",
			Tenant: tenantName,
		}
		_, err = tenantMappingDAO.Create(ctx, mapping2)
		require.NoError(t, err)

		// Run deduplication with actual changes
		resp, err := authServicer.DeduplicateUsersByIdpUserId(ctx, &pb.DeduplicateUsersByIdpUserIdRequest{
			MakeChanges: true,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Len(t, resp.UserMerges, 1)

		merge := resp.UserMerges[0]
		assert.Equal(t, "simple-user-1", merge.PrimaryUserId)
		assert.Contains(t, merge.MergedUserIds, "simple-user-2")

		// Verify primary user still exists
		primaryUser, err := userDAO.Get(ctx, "simple-user-1")
		require.NoError(t, err)
		assert.Equal(t, "<EMAIL>", primaryUser.Email)
		// Should have collected IDP user IDs from both users
		assert.Contains(t, primaryUser.IdpUserIds, "shared-idp-1")

		// Verify merged user was deleted (has no tenants)
		mergedUser, err := userDAO.Get(ctx, "simple-user-2")
		require.NoError(t, err)
		assert.Equal(t, 0, len(mergedUser.Tenants))
	})

	t.Run("Three-way transitive merge - A-B-C chain", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// User A has IDP IDs [chain-1, chain-2]
		userA := &auth_entities.User{
			Id:         "chain-user-a",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"chain-1", "chain-2"},
			Tenants:    []string{tenantID},
		}
		// User B has IDP IDs [chain-2, chain-3] - shares chain-2 with A
		userB := &auth_entities.User{
			Id:         "chain-user-b",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"chain-2", "chain-3"},
			Tenants:    []string{tenantID},
		}
		// User C has IDP IDs [chain-3, chain-4] - shares chain-3 with B
		userC := &auth_entities.User{
			Id:         "chain-user-c",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"chain-3", "chain-4"},
			Tenants:    []string{tenantID},
		}

		_, err := userDAO.Create(ctx, userA)
		require.NoError(t, err)
		_, err = userDAO.Create(ctx, userB)
		require.NoError(t, err)
		_, err = userDAO.Create(ctx, userC)
		require.NoError(t, err)

		// Add users to tenants
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO(tenantName)
		for _, userId := range []string{"chain-user-a", "chain-user-b", "chain-user-c"} {
			mapping := &auth_entities.UserTenantMapping{
				UserId: userId,
				Tenant: tenantName,
			}
			_, err = tenantMappingDAO.Create(ctx, mapping)
			require.NoError(t, err)
		}

		// Run deduplication with actual changes
		resp, err := authServicer.DeduplicateUsersByIdpUserId(ctx, &pb.DeduplicateUsersByIdpUserIdRequest{
			MakeChanges: true,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Len(t, resp.UserMerges, 1)

		merge := resp.UserMerges[0]
		assert.Equal(t, "chain-user-a", merge.PrimaryUserId)
		assert.Contains(t, merge.MergedUserIds, "chain-user-b")
		assert.Contains(t, merge.MergedUserIds, "chain-user-c")
		assert.Len(t, merge.MergedUserIds, 2)

		// Verify primary user still exists and has all IDP user IDs
		primaryUser, err := userDAO.Get(ctx, "chain-user-a")
		require.NoError(t, err)
		assert.Equal(t, "<EMAIL>", primaryUser.Email)
		// Should have collected all unique IDP user IDs
		expectedIdpIds := []string{"chain-1", "chain-2", "chain-3", "chain-4"}
		for _, expectedId := range expectedIdpIds {
			assert.Contains(t, primaryUser.IdpUserIds, expectedId)
		}

		// Verify merged users were deleted (have no tenants)
		mergedUserB, err := userDAO.Get(ctx, "chain-user-b")
		require.NoError(t, err)
		assert.Equal(t, 0, len(mergedUserB.Tenants))

		mergedUserC, err := userDAO.Get(ctx, "chain-user-c")
		require.NoError(t, err)
		assert.Equal(t, 0, len(mergedUserC.Tenants))
	})

	t.Run("Complex diamond merge with subscription transfer", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Diamond pattern with subscription information
		userA := &auth_entities.User{
			Id:         "diamond-merge-a",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"diamond-1", "diamond-2"},
			Tenants:    []string{tenantID},
			// No subscription
		}
		userB := &auth_entities.User{
			Id:         "diamond-merge-b",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"diamond-1", "diamond-3"},
			Tenants:    []string{tenantID},
			// Has subscription
			BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
			OrbCustomerId:     "diamond-orb-customer-b",
			OrbSubscriptionId: "diamond-orb-sub-b",
		}
		userC := &auth_entities.User{
			Id:         "diamond-merge-c",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"diamond-2", "diamond-4"},
			Tenants:    []string{tenantID},
			// No subscription
		}
		userD := &auth_entities.User{
			Id:         "diamond-merge-d",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"diamond-3", "diamond-4"},
			Tenants:    []string{tenantID},
			// No subscription
		}

		for _, user := range []*auth_entities.User{userA, userB, userC, userD} {
			_, err := userDAO.Create(ctx, user)
			require.NoError(t, err)
		}

		// Add users to tenants
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO(tenantName)
		for _, userId := range []string{"diamond-merge-a", "diamond-merge-b", "diamond-merge-c", "diamond-merge-d"} {
			mapping := &auth_entities.UserTenantMapping{
				UserId: userId,
				Tenant: tenantName,
			}
			_, err := tenantMappingDAO.Create(ctx, mapping)
			require.NoError(t, err)
		}

		// Mock subscription info for user B
		mockOrbClient.On("GetUserSubscription", mock.Anything, "diamond-orb-sub-b", mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: "diamond-orb-sub-b",
			ExternalPlanID:    "orb_community_plan",
			OrbStatus:         "active",
		}, nil)

		// Update the metadata of the subscription to point to the primary user
		mockOrbClient.On("UpdateCustomerMetadata", mock.Anything, "diamond-orb-customer-b", map[string]string{
			"augment_user_id": "diamond-merge-a",
		}).Return(nil)

		// Run deduplication with actual changes
		resp, err := authServicer.DeduplicateUsersByIdpUserId(ctx, &pb.DeduplicateUsersByIdpUserIdRequest{
			MakeChanges: true,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Len(t, resp.UserMerges, 1) // One merge group with all 4 users

		merge := resp.UserMerges[0]
		assert.Equal(t, "diamond-merge-a", merge.PrimaryUserId)
		assert.Contains(t, merge.MergedUserIds, "diamond-merge-b")
		assert.Contains(t, merge.MergedUserIds, "diamond-merge-c")
		assert.Contains(t, merge.MergedUserIds, "diamond-merge-d")
		assert.Equal(t, "diamond-merge-b", merge.SubscriptionSourceUserId)

		// Verify primary user now has the subscription information
		primaryUser, err := userDAO.Get(ctx, "diamond-merge-a")
		require.NoError(t, err)
		assert.Equal(t, "diamond-orb-customer-b", primaryUser.OrbCustomerId)
		assert.Equal(t, "diamond-orb-sub-b", primaryUser.OrbSubscriptionId)

		// Should have all unique IDP user IDs
		expectedIdpIds := []string{"diamond-1", "diamond-2", "diamond-3", "diamond-4"}
		for _, expectedId := range expectedIdpIds {
			assert.Contains(t, primaryUser.IdpUserIds, expectedId)
		}

		// Verify all merged users were deleted (have no tenants)
		for _, userId := range []string{"diamond-merge-b", "diamond-merge-c", "diamond-merge-d"} {
			mergedUser, err := userDAO.Get(ctx, userId)
			require.NoError(t, err)
			assert.Equal(t, 0, len(mergedUser.Tenants))
		}

		mockOrbClient.AssertExpectations(t)
	})

	t.Run("Simple merge with multiple IDP IDs per user", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// User 1 has multiple IDP IDs
		user1 := &auth_entities.User{
			Id:         "multi-idp-user-1",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"multi-1", "multi-2", "multi-3"},
			Tenants:    []string{tenantID},
		}
		// User 2 shares one IDP ID with user 1
		user2 := &auth_entities.User{
			Id:         "multi-idp-user-2",
			Email:      "<EMAIL>",
			IdpUserIds: []string{"multi-3", "multi-4", "multi-5"},
			Tenants:    []string{tenantID},
		}

		_, err := userDAO.Create(ctx, user1)
		require.NoError(t, err)
		_, err = userDAO.Create(ctx, user2)
		require.NoError(t, err)

		// Add users to tenants
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO(tenantName)
		for _, userId := range []string{"multi-idp-user-1", "multi-idp-user-2"} {
			mapping := &auth_entities.UserTenantMapping{
				UserId: userId,
				Tenant: tenantName,
			}
			_, err = tenantMappingDAO.Create(ctx, mapping)
			require.NoError(t, err)
		}

		// Run deduplication with actual changes
		resp, err := authServicer.DeduplicateUsersByIdpUserId(ctx, &pb.DeduplicateUsersByIdpUserIdRequest{
			MakeChanges: true,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Len(t, resp.UserMerges, 1)

		merge := resp.UserMerges[0]
		assert.Equal(t, "multi-idp-user-1", merge.PrimaryUserId)
		assert.Contains(t, merge.MergedUserIds, "multi-idp-user-2")

		// Verify primary user has all unique IDP user IDs
		primaryUser, err := userDAO.Get(ctx, "multi-idp-user-1")
		require.NoError(t, err)
		expectedIdpIds := []string{"multi-1", "multi-2", "multi-3", "multi-4", "multi-5"}
		assert.Len(t, primaryUser.IdpUserIds, 5)
		for _, expectedId := range expectedIdpIds {
			assert.Contains(t, primaryUser.IdpUserIds, expectedId)
		}

		// Verify merged user was deleted
		mergedUser, err := userDAO.Get(ctx, "multi-idp-user-2")
		require.NoError(t, err)
		assert.Equal(t, 0, len(mergedUser.Tenants))
	})
}

// Test case configuration for ScanLegacySelfServeTeams tests
type scanLegacySelfServeTeamsTestCase struct {
	name                   string
	users                  []testUser
	stripeSubscriptions    []testStripeSubscription
	orbSubscriptions       []testOrbSubscription
	enableOrb              bool
	contextConfig          testContextConfig
	expectedTeamCount      int
	expectedFailedTenants  int
	expectedError          bool
	expectedErrorCode      codes.Code
	expectedTeamValidation func(t *testing.T, team *pb.ScanLegacySelfServeTeamsResponse_TeamSubscription)
}

type testUser struct {
	id                string
	email             string
	stripeCustomerId  string
	orbCustomerId     string
	orbSubscriptionId string
	tenants           []string
}

type testStripeSubscription struct {
	id                 string
	status             stripe.SubscriptionStatus
	customerId         string
	priceId            string
	priceLookupKey     string
	quantity           int64
	currentPeriodStart time.Time
	currentPeriodEnd   time.Time
}

type testOrbSubscription struct {
	orbSubscriptionId string
	orbCustomerId     string
	orbStatus         string
	externalPlanId    string
}

type testContextConfig struct {
	tenantId              string
	scopes                []string
	expectPermissionError bool
}

func TestScanLegacySelfServeTeams(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	currentPeriodStart := time.Now().Add(-30 * 24 * time.Hour)
	currentPeriodEnd := time.Now().Add(30 * 24 * time.Hour)
	trialPeriodStart := time.Now().Add(-7 * 24 * time.Hour)
	trialPeriodEnd := time.Now().Add(7 * 24 * time.Hour)

	testCases := []scanLegacySelfServeTeamsTestCase{
		{
			name: "successful scan with active subscription",
			users: []testUser{
				{
					id:               "user-with-stripe-id",
					email:            "<EMAIL>",
					stripeCustomerId: "stripe-customer-id",
					tenants:          []string{"legacy-self-serve-team-id"},
				},
				{
					id:      "user-without-stripe-id",
					email:   "<EMAIL>",
					tenants: []string{"legacy-self-serve-team-id"},
				},
			},
			stripeSubscriptions: []testStripeSubscription{
				{
					id:                 "sub_123456",
					status:             stripe.SubscriptionStatusActive,
					customerId:         "stripe-customer-id",
					priceId:            "price_123456",
					priceLookupKey:     "pro_monthly_plan",
					quantity:           5,
					currentPeriodStart: currentPeriodStart,
					currentPeriodEnd:   currentPeriodEnd,
				},
			},
			contextConfig: testContextConfig{
				tenantId: "*",
				scopes:   []string{tokenexchangeproto.Scope_AUTH_R.String()},
			},
			expectedTeamCount:     1,
			expectedFailedTenants: 0,
			expectedTeamValidation: func(t *testing.T, team *pb.ScanLegacySelfServeTeamsResponse_TeamSubscription) {
				require.Equal(t, "legacy-self-serve-team-id", team.TenantId)
				require.Equal(t, "legacy-self-serve-team", team.TenantName)
				require.Equal(t, int32(2), team.UserCount)
				require.Equal(t, "user-with-stripe-id", team.AdminUserId)
				require.Equal(t, "stripe-customer-id", team.StripeCustomerId)
				require.Equal(t, "sub_123456", team.StripeSubscriptionId)
				require.Equal(t, "active", team.StripeSubscriptionStatus)
				require.Equal(t, "price_123456", team.StripeSubscriptionPriceId)
				require.Equal(t, "pro_monthly_plan", team.StripeSubscriptionPriceKey)
				require.Equal(t, int64(5), team.StripeSubscriptionQuantity)
				require.Equal(t, currentPeriodStart.Unix(), team.StripeSubscriptionCurrentPeriodStart.Seconds)
				require.Equal(t, currentPeriodEnd.Unix(), team.StripeSubscriptionCurrentPeriodEnd.Seconds)
			},
		},
		{
			name: "multiple active subscriptions scenario",
			users: []testUser{
				{
					id:               "user-1-id",
					email:            "<EMAIL>",
					stripeCustomerId: "stripe-customer-1",
					tenants:          []string{"legacy-self-serve-team-id"},
				},
				{
					id:               "user-2-id",
					email:            "<EMAIL>",
					stripeCustomerId: "stripe-customer-2",
					tenants:          []string{"legacy-self-serve-team-id"},
				},
			},
			stripeSubscriptions: []testStripeSubscription{
				{
					id:                 "sub_active_1",
					status:             stripe.SubscriptionStatusActive,
					customerId:         "stripe-customer-1",
					priceId:            "price_active_1",
					priceLookupKey:     "plan_1",
					quantity:           3,
					currentPeriodStart: currentPeriodStart,
					currentPeriodEnd:   currentPeriodEnd,
				},
				{
					id:                 "sub_active_2",
					status:             stripe.SubscriptionStatusActive,
					customerId:         "stripe-customer-2",
					priceId:            "price_active_2",
					priceLookupKey:     "plan_2",
					quantity:           5,
					currentPeriodStart: currentPeriodStart,
					currentPeriodEnd:   currentPeriodEnd,
				},
			},
			contextConfig: testContextConfig{
				tenantId: "*",
				scopes:   []string{tokenexchangeproto.Scope_AUTH_R.String()},
			},
			expectedTeamCount:     1,
			expectedFailedTenants: 0,
			expectedTeamValidation: func(t *testing.T, team *pb.ScanLegacySelfServeTeamsResponse_TeamSubscription) {
				require.Equal(t, "legacy-self-serve-team-id", team.TenantId)
				require.Equal(t, "legacy-self-serve-team", team.TenantName)
				require.Equal(t, int32(2), team.UserCount)
				require.Equal(t, "", team.AdminUserId)
				require.Equal(t, "", team.StripeCustomerId)
				require.Equal(t, "", team.StripeSubscriptionId)
				require.Equal(t, "", team.StripeSubscriptionStatus)
				require.True(t, strings.Contains(team.FindStripeSubscriptionError, "multiple active Stripe subscriptions found"))
			},
		},
		{
			name: "trial subscription scenario",
			users: []testUser{
				{
					id:               "user-with-trial-id",
					email:            "<EMAIL>",
					stripeCustomerId: "stripe-customer-trial",
					tenants:          []string{"legacy-self-serve-team-id"},
				},
			},
			stripeSubscriptions: []testStripeSubscription{
				{
					id:                 "sub_trial_123",
					status:             stripe.SubscriptionStatusTrialing,
					customerId:         "stripe-customer-trial",
					priceId:            "price_trial_123",
					priceLookupKey:     "trial_plan",
					quantity:           2,
					currentPeriodStart: trialPeriodStart,
					currentPeriodEnd:   trialPeriodEnd,
				},
			},
			contextConfig: testContextConfig{
				tenantId: "*",
				scopes:   []string{tokenexchangeproto.Scope_AUTH_R.String()},
			},
			expectedTeamCount:     1,
			expectedFailedTenants: 0,
			expectedTeamValidation: func(t *testing.T, team *pb.ScanLegacySelfServeTeamsResponse_TeamSubscription) {
				require.Equal(t, "legacy-self-serve-team-id", team.TenantId)
				require.Equal(t, "legacy-self-serve-team", team.TenantName)
				require.Equal(t, int32(1), team.UserCount)
				require.Equal(t, "user-with-trial-id", team.AdminUserId)
				require.Equal(t, "stripe-customer-trial", team.StripeCustomerId)
				require.Equal(t, "sub_trial_123", team.StripeSubscriptionId)
				require.Equal(t, "trialing", team.StripeSubscriptionStatus)
				require.Equal(t, "price_trial_123", team.StripeSubscriptionPriceId)
				require.Equal(t, "trial_plan", team.StripeSubscriptionPriceKey)
				require.Equal(t, int64(2), team.StripeSubscriptionQuantity)
				require.Equal(t, trialPeriodStart.Unix(), team.StripeSubscriptionCurrentPeriodStart.Seconds)
				require.Equal(t, trialPeriodEnd.Unix(), team.StripeSubscriptionCurrentPeriodEnd.Seconds)
			},
		},
		{
			name: "user with active Orb subscription",
			users: []testUser{
				{
					id:                "user-with-orb-subscription-id",
					email:             "<EMAIL>",
					orbCustomerId:     "orb-customer-123",
					orbSubscriptionId: "orb-subscription-123",
					tenants:           []string{"legacy-self-serve-team-id"},
				},
			},
			orbSubscriptions: []testOrbSubscription{
				{
					orbSubscriptionId: "orb-subscription-123",
					orbCustomerId:     "orb-customer-123",
					orbStatus:         "active",
					externalPlanId:    "orb_developer_plan",
				},
			},
			enableOrb: true,
			contextConfig: testContextConfig{
				tenantId: "*",
				scopes:   []string{tokenexchangeproto.Scope_AUTH_R.String()},
			},
			expectedTeamCount:     1,
			expectedFailedTenants: 0,
			expectedTeamValidation: func(t *testing.T, team *pb.ScanLegacySelfServeTeamsResponse_TeamSubscription) {
				require.Equal(t, "legacy-self-serve-team-id", team.TenantId)
				require.Equal(t, "legacy-self-serve-team", team.TenantName)
				require.Equal(t, int32(1), team.UserCount)
				require.Equal(t, "", team.AdminUserId)
				require.Equal(t, "", team.StripeCustomerId)
				require.Equal(t, "", team.StripeSubscriptionId)
				require.Equal(t, "", team.StripeSubscriptionStatus)
				require.True(t, strings.Contains(team.FindStripeSubscriptionError, "active Orb subscription found"))
			},
		},
		{
			name: "insufficient permissions - no scopes",
			contextConfig: testContextConfig{
				tenantId:              "*",
				scopes:                []string{},
				expectPermissionError: true,
			},
			expectedError:     true,
			expectedErrorCode: codes.PermissionDenied,
		},
		{
			name: "insufficient permissions - wrong tenant",
			contextConfig: testContextConfig{
				tenantId:              "wrong-tenant-id",
				scopes:                []string{tokenexchangeproto.Scope_AUTH_R.String()},
				expectPermissionError: true,
			},
			expectedError:     true,
			expectedErrorCode: codes.PermissionDenied,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			runScanLegacySelfServeTeamsTestCase(t, bigtableFixture, tc)
		})
	}
}

// runScanLegacySelfServeTeamsTestCase executes a single test case for ScanLegacySelfServeTeams
func runScanLegacySelfServeTeamsTestCase(t *testing.T, bigtableFixture *BigtableFixture, tc scanLegacySelfServeTeamsTestCase) {
	bigtableFixture.ClearTable(t)

	// Create mock tenant watcher client
	mockTenantWatcherClient := &tw_client.MockTenantWatcherClient{
		Tenants: []*tw_pb.Tenant{
			{
				Id:             "legacy-self-serve-team-id",
				Name:           "legacy-self-serve-team",
				ShardNamespace: "test-namespace",
				Cloud:          "test-cloud",
				Config: &tw_pb.Config{
					Configs: map[string]string{
						"is_legacy_self_serve_team": "true",
						"is_self_serve_team":        "true",
					},
				},
			},
		},
	}

	// Create mock stripe client
	mockStripeClient := NewMockStripeClient()

	// Setup Stripe subscriptions
	for _, sub := range tc.stripeSubscriptions {
		subscription := &stripe.Subscription{
			ID:     sub.id,
			Status: sub.status,
			Items: &stripe.SubscriptionItemList{
				Data: []*stripe.SubscriptionItem{
					{
						Price: &stripe.Price{
							ID:        sub.priceId,
							LookupKey: sub.priceLookupKey,
						},
						Quantity: sub.quantity,
					},
				},
			},
			Customer: &stripe.Customer{
				ID: sub.customerId,
			},
			CurrentPeriodStart: sub.currentPeriodStart.Unix(),
			CurrentPeriodEnd:   sub.currentPeriodEnd.Unix(),
		}
		mockStripeClient.AddDetailedSubscription(subscription)
	}

	// Create auth servicer
	authServicer := createAuthServicerForScanLegacyTeamTests(t, bigtableFixture, mockTenantWatcherClient, mockStripeClient)
	if tc.enableOrb {
		// Setup Orb subscriptions
		if len(tc.orbSubscriptions) > 0 {
			mockOrbClient := authServicer.orbClient.(*orb.MockOrbClient)
			for _, orbSub := range tc.orbSubscriptions {
				mockOrbClient.On("GetUserSubscription", mock.Anything, orbSub.orbSubscriptionId, mock.Anything).Return(&orb.OrbSubscriptionInfo{
					OrbSubscriptionID: orbSub.orbSubscriptionId,
					OrbCustomerID:     orbSub.orbCustomerId,
					OrbStatus:         orbSub.orbStatus,
					ExternalPlanID:    orbSub.externalPlanId,
				}, nil)
			}
		}
	}

	// Setup users and tenant mappings
	if len(tc.users) > 0 {
		daoFactory := NewDAOFactory(bigtableFixture.Table)
		userDAO := daoFactory.GetUserDAO()
		userTenantMappingDAO := daoFactory.GetUserTenantMappingDAO("legacy-self-serve-team")

		for _, user := range tc.users {
			userEntity := &auth_entities.User{
				Id:                user.id,
				Email:             user.email,
				StripeCustomerId:  user.stripeCustomerId,
				OrbCustomerId:     user.orbCustomerId,
				OrbSubscriptionId: user.orbSubscriptionId,
				Tenants:           user.tenants,
			}
			_, err := userDAO.Create(context.Background(), userEntity)
			require.NoError(t, err)

			// Create tenant mapping
			mapping := &auth_entities.UserTenantMapping{
				UserId: user.id,
				Tenant: "legacy-self-serve-team-id",
			}
			_, err = userTenantMappingDAO.Create(context.Background(), mapping)
			require.NoError(t, err)
		}
	}

	// Create context
	var ctx context.Context
	if tc.contextConfig.expectPermissionError {
		claims := &auth.AugmentClaims{
			TenantID:         tc.contextConfig.tenantId,
			TenantName:       "admin-tenant",
			UserID:           "admin-user-id",
			OpaqueUserID:     "admin-user-id",
			OpaqueUserIDType: auth_entities.UserId_INTERNAL_IAP.String(),
			Scope:            tc.contextConfig.scopes,
		}
		ctx = claims.NewContext(context.Background())
		ctx = addRequestInfoToContext(ctx)
	} else {
		ctx = createAdminContext()
		ctx = addRequestInfoToContext(ctx)
	}

	// Test the endpoint
	req := &pb.ScanLegacySelfServeTeamsRequest{}
	resp, err := authServicer.ScanLegacySelfServeTeams(ctx, req)

	// Verify error expectations
	if tc.expectedError {
		require.Error(t, err)
		require.Equal(t, tc.expectedErrorCode, status.Code(err))
		return
	}

	// Verify successful response
	require.NoError(t, err)
	require.NotNil(t, resp)
	require.Equal(t, tc.expectedTeamCount, len(resp.TeamSubscriptions))
	require.Equal(t, tc.expectedFailedTenants, len(resp.FailedTenants))

	// Run team validation if provided
	if tc.expectedTeamValidation != nil && len(resp.TeamSubscriptions) > 0 {
		tc.expectedTeamValidation(t, resp.TeamSubscriptions[0])
	}

	// Verify Orb mock expectations if Orb is enabled
	if tc.enableOrb && len(tc.orbSubscriptions) > 0 {
		mockOrbClient := authServicer.orbClient.(*orb.MockOrbClient)
		mockOrbClient.AssertExpectations(t)
	}
}

// Helper function to create auth servicer for tests
func createAuthServicerForScanLegacyTeamTests(t *testing.T, bigtableFixture *BigtableFixture, mockTenantWatcherClient *tw_client.MockTenantWatcherClient, mockStripeClient *MockStripeClient) *AuthGrpcServer {
	daoFactory := NewDAOFactory(bigtableFixture.Table)
	ffHandle := featureflag.NewLocalFeatureFlagHandler()
	tenantMap := NewTenantMap(
		daoFactory,
		mockTenantWatcherClient,
		"us-central.api.augmentcode.com",
		ffHandle,
		NewMockAsyncOpsPublisher(),
		&audit.AuditLogger{},
	)

	stripeConfig := &StripeConfig{
		Enabled: true,
	}

	orbConfig := &OrbConfig{
		Enabled: true,
		Plans: []PlanConfig{
			{
				ID: "orb_community_plan",
				Features: PlanFeatures{
					PlanType: PlanTypeCommunity,
				},
			},
			{
				ID: "orb_trial_plan",
				Features: PlanFeatures{
					PlanType: PlanTypePaidTrial,
				},
			},
			{
				ID: "orb_developer_plan",
				Features: PlanFeatures{
					PlanType: PlanTypePaid,
				},
			},
		},
	}

	authServicer := NewAuthGrpcServer(
		ffHandle,
		daoFactory,
		tenantMap,
		audit.NewDefaultAuditLogger(),
		ripublisher.NewRequestInsightPublisherMock(),
		NewMockAsyncOpsPublisher(),
		stripeConfig,
		orbConfig,
		mockStripeClient,
	)
	authServicer.orbClient = orb.NewMockOrbClient()
	return authServicer
}

// Helper function to create admin context
func createAdminContext() context.Context {
	claims := &auth.AugmentClaims{
		TenantID:         "*", // Wildcard to allow access to all tenants
		TenantName:       "admin-tenant",
		UserID:           "admin-user-id",
		OpaqueUserID:     "admin-user-id",
		OpaqueUserIDType: auth_entities.UserId_INTERNAL_IAP.String(),
		Scope:            []string{tokenexchangeproto.Scope_AUTH_R.String()},
	}
	return claims.NewContext(context.Background())
}

func addRequestInfoToContext(ctx context.Context) context.Context {
	requestId := uuid.New().String()
	return metadata.NewIncomingContext(ctx, metadata.Pairs("x-request-id", requestId))
}

func TestDeleteTenantSubscriptionMapping(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	testCases := []struct {
		name              string
		tenantId          string
		setupMapping      bool
		contextConfig     testContextConfig
		expectedError     bool
		expectedErrorCode codes.Code
	}{
		{
			name:         "successful deletion with existing mapping",
			tenantId:     "test-tenant-id",
			setupMapping: true,
			contextConfig: testContextConfig{
				tenantId: "*",
				scopes:   []string{tokenexchangeproto.Scope_AUTH_RW.String()},
			},
			expectedError: false,
		},
		{
			name:         "successful deletion with non-existing mapping",
			tenantId:     "non-existing-tenant-id",
			setupMapping: false,
			contextConfig: testContextConfig{
				tenantId: "*",
				scopes:   []string{tokenexchangeproto.Scope_AUTH_RW.String()},
			},
			expectedError: false,
		},
		{
			name:         "empty tenant_id",
			tenantId:     "",
			setupMapping: false,
			contextConfig: testContextConfig{
				tenantId: "*",
				scopes:   []string{tokenexchangeproto.Scope_AUTH_RW.String()},
			},
			expectedError:     true,
			expectedErrorCode: codes.Internal,
		},
		{
			name:         "insufficient permissions - AUTH_R scope",
			tenantId:     "test-tenant-id",
			setupMapping: true,
			contextConfig: testContextConfig{
				tenantId: "*",
				scopes:   []string{tokenexchangeproto.Scope_AUTH_R.String()},
			},
			expectedError:     true,
			expectedErrorCode: codes.PermissionDenied,
		},
		{
			name:         "unauthenticated request",
			tenantId:     "test-tenant-id",
			setupMapping: true,
			contextConfig: testContextConfig{
				expectPermissionError: true,
			},
			expectedError:     true,
			expectedErrorCode: codes.Unauthenticated,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			runDeleteTenantSubscriptionMappingTestCase(t, bigtableFixture, tc)
		})
	}
}

func runDeleteTenantSubscriptionMappingTestCase(t *testing.T, bigtableFixture *BigtableFixture, tc struct {
	name              string
	tenantId          string
	setupMapping      bool
	contextConfig     testContextConfig
	expectedError     bool
	expectedErrorCode codes.Code
},
) {
	bigtableFixture.ClearTable(t)

	// Create auth servicer
	daoFactory := NewDAOFactory(bigtableFixture.Table)
	authServicer := &AuthGrpcServer{
		daoFactory:  daoFactory,
		auditLogger: audit.NewDefaultAuditLogger(),
	}

	// Setup tenant subscription mapping if needed
	if tc.setupMapping {
		tenantSubscriptionMappingDAO := daoFactory.GetTenantSubscriptionMappingDAO()
		mapping := &auth_entities.TenantSubscriptionMapping{
			TenantId:             tc.tenantId,
			StripeSubscriptionId: "test-stripe-subscription-id",
			StripeCustomerId:     "test-stripe-customer-id",
			CreatedAt:            timestamppb.Now(),
		}
		_, err := tenantSubscriptionMappingDAO.Create(context.Background(), mapping)
		require.NoError(t, err)
	}

	// Create context
	var ctx context.Context
	if !tc.contextConfig.expectPermissionError {
		claims := &auth.AugmentClaims{
			TenantID:         tc.contextConfig.tenantId,
			TenantName:       "test-tenant",
			UserID:           "test-user-id",
			OpaqueUserID:     "test-user-id",
			OpaqueUserIDType: auth_entities.UserId_INTERNAL_IAP.String(),
			Scope:            tc.contextConfig.scopes,
		}
		ctx = claims.NewContext(context.Background())
		ctx = addRequestInfoToContext(ctx)
	} else {
		ctx = context.Background()
		ctx = addRequestInfoToContext(ctx)
	}

	// Test the endpoint
	req := &pb.DeleteTenantSubscriptionMappingRequest{
		TenantId: tc.tenantId,
	}
	resp, err := authServicer.DeleteTenantSubscriptionMapping(ctx, req)

	// Verify error expectations
	if tc.expectedError {
		require.Error(t, err)
		require.Equal(t, tc.expectedErrorCode, status.Code(err))
		return
	}

	// Verify successful response
	require.NoError(t, err)
	require.NotNil(t, resp)
	// Response is now empty, so we just verify it's not nil

	// If we had a mapping and the operation succeeded, verify it was deleted
	if tc.setupMapping && tc.tenantId != "" {
		tenantSubscriptionMappingDAO := daoFactory.GetTenantSubscriptionMappingDAO()
		_, err := tenantSubscriptionMappingDAO.Get(context.Background(), tc.tenantId)
		// The delete operation should succeed, and subsequent Get should fail
		if err == nil {
			t.Logf("Warning: Expected mapping to be deleted but it still exists")
		}
	}
}

func TestUpdateSubscriptionOwnerToTeam(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	testCases := []struct {
		name               string
		subscriptionIds    []string
		setupSubscriptions []struct {
			id    string
			owner interface{} // *auth_entities.Subscription_UserId or *auth_entities.Subscription_TenantId
		}
		setupUser         bool
		userTenants       []string
		setupTenant       bool
		tenantConfig      map[string]string
		contextConfig     testContextConfig
		expectedError     bool
		expectedErrorCode codes.Code
		expectedResults   []struct {
			subscriptionId string
			success        bool
			errorMessage   string
		}
	}{
		{
			name:            "successful update from user to team",
			subscriptionIds: []string{"test-subscription-id"},
			setupSubscriptions: []struct {
				id    string
				owner interface{}
			}{
				{
					id:    "test-subscription-id",
					owner: &auth_entities.Subscription_UserId{UserId: "test-user-id"},
				},
			},
			setupUser:   true,
			userTenants: []string{"test-tenant-id"},
			setupTenant: true,
			tenantConfig: map[string]string{
				"is_self_serve_team": "true",
			},
			contextConfig: testContextConfig{
				tenantId: "*",
				scopes:   []string{tokenexchangeproto.Scope_AUTH_RW.String()},
			},
			expectedError: false,
			expectedResults: []struct {
				subscriptionId string
				success        bool
				errorMessage   string
			}{
				{
					subscriptionId: "test-subscription-id",
					success:        true,
					errorMessage:   "",
				},
			},
		},
		{
			name:            "multiple subscriptions - mixed results",
			subscriptionIds: []string{"test-subscription-1", "non-existing-subscription", "test-subscription-2"},
			setupSubscriptions: []struct {
				id    string
				owner interface{}
			}{
				{
					id:    "test-subscription-1",
					owner: &auth_entities.Subscription_UserId{UserId: "test-user-id"},
				},
				{
					id:    "test-subscription-2",
					owner: &auth_entities.Subscription_TenantId{TenantId: "test-tenant-id"},
				},
			},
			setupUser:   true,
			userTenants: []string{"test-tenant-id"},
			setupTenant: true,
			tenantConfig: map[string]string{
				"is_self_serve_team": "true",
			},
			contextConfig: testContextConfig{
				tenantId: "*",
				scopes:   []string{tokenexchangeproto.Scope_AUTH_RW.String()},
			},
			expectedError: false,
			expectedResults: []struct {
				subscriptionId string
				success        bool
				errorMessage   string
			}{
				{
					subscriptionId: "test-subscription-1",
					success:        true,
					errorMessage:   "",
				},
				{
					subscriptionId: "non-existing-subscription",
					success:        false,
					errorMessage:   "Subscription not found",
				},
				{
					subscriptionId: "test-subscription-2",
					success:        false,
					errorMessage:   "Subscription owner is not a user",
				},
			},
		},
		{
			name:            "insufficient permissions - AUTH_R scope",
			subscriptionIds: []string{"test-subscription-id"},
			setupSubscriptions: []struct {
				id    string
				owner interface{}
			}{
				{
					id:    "test-subscription-id",
					owner: &auth_entities.Subscription_UserId{UserId: "test-user-id"},
				},
			},
			setupUser:   true,
			userTenants: []string{"test-tenant-id"},
			setupTenant: true,
			tenantConfig: map[string]string{
				"is_self_serve_team": "true",
			},
			contextConfig: testContextConfig{
				tenantId: "*",
				scopes:   []string{tokenexchangeproto.Scope_AUTH_R.String()},
			},
			expectedError:     true,
			expectedErrorCode: codes.PermissionDenied,
		},
		{
			name:            "unauthenticated request",
			subscriptionIds: []string{"test-subscription-id"},
			setupSubscriptions: []struct {
				id    string
				owner interface{}
			}{
				{
					id:    "test-subscription-id",
					owner: &auth_entities.Subscription_UserId{UserId: "test-user-id"},
				},
			},
			contextConfig: testContextConfig{
				expectPermissionError: true,
			},
			expectedError:     true,
			expectedErrorCode: codes.Unauthenticated,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			runUpdateSubscriptionOwnerToTeamTestCase(t, bigtableFixture, tc)
		})
	}
}

func runUpdateSubscriptionOwnerToTeamTestCase(t *testing.T, bigtableFixture *BigtableFixture, tc struct {
	name               string
	subscriptionIds    []string
	setupSubscriptions []struct {
		id    string
		owner interface{}
	}
	setupUser         bool
	userTenants       []string
	setupTenant       bool
	tenantConfig      map[string]string
	contextConfig     testContextConfig
	expectedError     bool
	expectedErrorCode codes.Code
	expectedResults   []struct {
		subscriptionId string
		success        bool
		errorMessage   string
	}
},
) {
	bigtableFixture.ClearTable(t)

	// Create mock tenant watcher client
	mockTenantWatcherClient := &tw_client.MockTenantWatcherClient{
		Tenants: []*tw_pb.Tenant{},
	}

	// Setup tenant if needed
	if tc.setupTenant {
		tenant := &tw_pb.Tenant{
			Id:             "test-tenant-id",
			Name:           "test-tenant",
			ShardNamespace: "test-namespace",
			Cloud:          "CLOUD_DEV",
			Tier:           tw_pb.TenantTier_PROFESSIONAL,
		}
		if tc.tenantConfig != nil {
			tenant.Config = &tw_pb.Config{
				Configs: tc.tenantConfig,
			}
		}
		mockTenantWatcherClient.Tenants = append(mockTenantWatcherClient.Tenants, tenant)
	}

	// Create auth servicer
	daoFactory := NewDAOFactory(bigtableFixture.Table)
	authServicer := NewAuthGrpcServer(
		featureflag.NewLocalFeatureFlagHandler(),
		daoFactory,
		NewTenantMap(
			daoFactory,
			mockTenantWatcherClient,
			"us-central.api.augmentcode.com",
			featureflag.NewLocalFeatureFlagHandler(),
			NewMockAsyncOpsPublisher(),
			audit.NewDefaultAuditLogger(),
		),
		audit.NewDefaultAuditLogger(),
		ripublisher.NewRequestInsightPublisherMock(),
		NewMockAsyncOpsPublisher(),
		&StripeConfig{Enabled: false},
		&OrbConfig{
			Enabled:            true,
			ProfessionalPlanID: "orb_developer_plan",
		},
		NewMockStripeClient(),
	)

	// Setup subscriptions if needed
	for _, sub := range tc.setupSubscriptions {
		subscriptionDAO := daoFactory.GetSubscriptionDAO()
		subscription := &auth_entities.Subscription{
			SubscriptionId: sub.id,
			CreatedAt:      timestamppb.Now(),
			UpdatedAt:      timestamppb.Now(),
		}
		// Set the owner based on the type
		switch owner := sub.owner.(type) {
		case *auth_entities.Subscription_UserId:
			subscription.Owner = owner
		case *auth_entities.Subscription_TenantId:
			subscription.Owner = owner
		}
		_, err := subscriptionDAO.Create(context.Background(), subscription)
		require.NoError(t, err)
	}

	// Setup user if needed
	if tc.setupUser {
		userDAO := daoFactory.GetUserDAO()
		user := &auth_entities.User{
			Id:        "test-user-id",
			Email:     "<EMAIL>",
			Tenants:   tc.userTenants,
			CreatedAt: timestamppb.Now(),
		}
		_, err := userDAO.Create(context.Background(), user)
		require.NoError(t, err)
	}

	// Create context
	var ctx context.Context
	if !tc.contextConfig.expectPermissionError {
		claims := &auth.AugmentClaims{
			TenantID:         tc.contextConfig.tenantId,
			TenantName:       "test-tenant",
			UserID:           "test-user-id",
			OpaqueUserID:     "test-user-id",
			OpaqueUserIDType: auth_entities.UserId_INTERNAL_IAP.String(),
			Scope:            tc.contextConfig.scopes,
		}
		ctx = claims.NewContext(context.Background())
		ctx = addRequestInfoToContext(ctx)
	} else {
		ctx = context.Background()
		ctx = addRequestInfoToContext(ctx)
	}

	// Test the endpoint
	req := &pb.UpdateSubscriptionOwnerToTeamRequest{
		SubscriptionIds: tc.subscriptionIds,
	}
	resp, err := authServicer.UpdateSubscriptionOwnerToTeam(ctx, req)

	// Verify error expectations
	if tc.expectedError {
		require.Error(t, err)
		require.Equal(t, tc.expectedErrorCode, status.Code(err))
		return
	}

	// Verify successful response
	require.NoError(t, err)
	require.NotNil(t, resp)

	// Verify results if expected
	if tc.expectedResults != nil {
		require.Equal(t, len(tc.expectedResults), len(resp.Results))
		for i, expectedResult := range tc.expectedResults {
			actualResult := resp.Results[i]
			require.Equal(t, expectedResult.subscriptionId, actualResult.SubscriptionId)
			require.Equal(t, expectedResult.success, actualResult.Success)
			if expectedResult.errorMessage != "" {
				require.Contains(t, actualResult.ErrorMessage, expectedResult.errorMessage)
			}
		}

		// For successful updates, verify the subscription owner was actually updated
		for _, expectedResult := range tc.expectedResults {
			if expectedResult.success {
				subscriptionDAO := daoFactory.GetSubscriptionDAO()
				updatedSubscription, err := subscriptionDAO.Get(context.Background(), expectedResult.subscriptionId)
				require.NoError(t, err)
				require.NotNil(t, updatedSubscription)

				// Verify the owner is now a tenant
				tenantOwner, ok := updatedSubscription.Owner.(*auth_entities.Subscription_TenantId)
				require.True(t, ok, "Expected subscription owner to be a tenant")
				require.Equal(t, "test-tenant-id", tenantOwner.TenantId)
			}
		}
	}
}

func caseDeduplicateUserTenantList(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	daoFactory := sut.daoFactory

	ctx := createCentralAuthorizedRequestContext()
	userDAO := daoFactory.GetUserDAO()

	// Create a user with duplicate tenants
	user := &auth_entities.User{
		Id:      "duplicate-tenant-user",
		Email:   "<EMAIL>",
		Tenants: []string{tenantID, tenantID, tenantID}, // Same tenant repeated
	}
	_, err := userDAO.Create(ctx, user)
	require.NoError(t, err)

	// Test dry run
	resp, err := authServicer.DeduplicateUserTenantList(ctx, &pb.DeduplicateUserTenantListRequest{
		UserId:      "duplicate-tenant-user",
		MakeChanges: false,
	})
	require.NoError(t, err)
	require.Equal(t, []string{tenantID, tenantID, tenantID}, resp.PreviousTenants)
	require.Equal(t, tenantID, resp.NewTenant)

	// Test actual changes
	resp, err = authServicer.DeduplicateUserTenantList(ctx, &pb.DeduplicateUserTenantListRequest{
		UserId:      "duplicate-tenant-user",
		MakeChanges: true,
	})
	require.NoError(t, err)
	require.Equal(t, []string{tenantID, tenantID, tenantID}, resp.PreviousTenants)
	require.Equal(t, tenantID, resp.NewTenant)

	// Verify user was updated
	updatedUser, err := userDAO.Get(ctx, "duplicate-tenant-user")
	require.NoError(t, err)
	require.Equal(t, []string{tenantID}, updatedUser.Tenants)

	// Test user with multiple different tenants (should fail)
	user2 := &auth_entities.User{
		Id:      "multi-tenant-user",
		Email:   "<EMAIL>",
		Tenants: []string{tenantID, professionalTenantID},
	}
	_, err = userDAO.Create(ctx, user2)
	require.NoError(t, err)

	_, err = authServicer.DeduplicateUserTenantList(ctx, &pb.DeduplicateUserTenantListRequest{
		UserId:      "multi-tenant-user",
		MakeChanges: false,
	})
	require.Error(t, err)
	require.Equal(t, codes.FailedPrecondition, status.Code(err))
}

func TestSyncAddresses(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer
	// Inject a mock orb client
	mockOrbClient := orb.NewMockOrbClient()
	mockOrbClient.On("UpdateCustomerBillingAddress", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)
	authServicer.orbClient = mockOrbClient
	userDAO := sut.daoFactory.GetUserDAO()

	mockStripeClient := NewMockStripeClient()
	authServicer.stripeClient = mockStripeClient

	ctx := createCentralAuthorizedRequestContext()

	t.Run("Users not included", func(t *testing.T) {
		bigtableFixture.ClearTable(t)
		user := &auth_entities.User{
			Id:      "sync-addresses-user-1",
			Email:   "<EMAIL>",
			Tenants: []string{tenantID},
		}
		_, err := userDAO.Create(ctx, user)

		user2 := &auth_entities.User{
			Id:      "sync-addresses-user-2",
			Email:   "<EMAIL>",
			Tenants: []string{tenantID},
		}
		_, err = userDAO.Create(ctx, user2)
		require.NoError(t, err)

		// only include one of the users in the request
		resp, err := authServicer.SyncAddresses(ctx, &authpb.SyncAddressesRequest{
			MakeChanges: true,
			IdsToSync:   []string{"sync-addresses-user-2"},
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Len(t, resp.UsersUpdated, 0)
		require.Len(t, resp.UsersFailed, 0)
		require.Len(t, resp.UsersNotAttempted, 1) // this person does not have Orb/Stripe customer, not attempted
		require.Len(t, resp.UsersMultiplePaymentMethods, 0)
	})

	t.Run("Users are missing Orb or Stripe Info", func(t *testing.T) {
		bigtableFixture.ClearTable(t)
		user := &auth_entities.User{
			Id:                "sync-addresses-user-1",
			Tenants:           []string{tenantID},
			OrbCustomerId:     "orb-customer-sync-1",
			OrbSubscriptionId: "orb-sub-sync-1",
			// No Stripe Customer ID
		}
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		user2 := &auth_entities.User{
			Id:      "sync-addresses-user-2",
			Tenants: []string{tenantID},
			// No Orb Customer ID
			StripeCustomerId: "stripe-customer-sync-2",
		}
		_, err = userDAO.Create(ctx, user2)
		require.NoError(t, err)

		resp, err := authServicer.SyncAddresses(ctx, &authpb.SyncAddressesRequest{
			MakeChanges: true,
			IdsToSync:   []string{},
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Len(t, resp.UsersUpdated, 0)
		require.Len(t, resp.UsersFailed, 0)
		require.Len(t, resp.UsersNotAttempted, 2) // all are missing info
		require.Len(t, resp.UsersMultiplePaymentMethods, 0)
	})

	t.Run("No payment method", func(t *testing.T) {
		bigtableFixture.ClearTable(t)
		user := &auth_entities.User{
			Id:                "sync-addresses-user-1",
			Tenants:           []string{tenantID},
			OrbCustomerId:     "orb-customer-sync-1",
			OrbSubscriptionId: "orb-sub-sync-1",
			StripeCustomerId:  "stripe-customer-sync-1",
		}
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		resp, err := authServicer.SyncAddresses(ctx, &authpb.SyncAddressesRequest{
			MakeChanges: true,
			IdsToSync:   []string{},
		})
		require.NoError(t, err)
		require.NotNil(t, resp)

		require.Len(t, resp.UsersUpdated, 0)
		require.Len(t, resp.UsersFailed, 0)
		require.Len(t, resp.UsersNotAttempted, 1) // no payment method
		require.Len(t, resp.UsersMultiplePaymentMethods, 0)
	})

	t.Run("Success, dry run", func(t *testing.T) {
		bigtableFixture.ClearTable(t)
		user := &auth_entities.User{
			Id:                "sync-addresses-user-1",
			Tenants:           []string{tenantID},
			OrbCustomerId:     "orb-customer-sync-1",
			OrbSubscriptionId: "orb-sub-sync-1",
			StripeCustomerId:  "stripe-customer-sync-1",
		}
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		// add a payment method for the customer
		mockStripeClient.SetCustomerHasPaymentMethod("stripe-customer-sync-1", true)

		resp, err := authServicer.SyncAddresses(ctx, &authpb.SyncAddressesRequest{
			MakeChanges: false,
			IdsToSync:   []string{},
		})
		require.NoError(t, err)
		require.NotNil(t, resp)

		require.Len(t, resp.UsersUpdated, 1)
		require.Len(t, resp.UsersFailed, 0)
		require.Len(t, resp.UsersNotAttempted, 0)
		require.Len(t, resp.UsersMultiplePaymentMethods, 0)

		// Assert we do not call Orb
		mockOrbClient.AssertNotCalled(t, "UpdateCustomerBillingAddress", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything)
	})

	t.Run("Success", func(t *testing.T) {
		bigtableFixture.ClearTable(t)
		user := &auth_entities.User{
			Id:                "sync-addresses-user-1",
			Tenants:           []string{tenantID},
			OrbCustomerId:     "orb-customer-sync-1",
			OrbSubscriptionId: "orb-sub-sync-1",
			StripeCustomerId:  "stripe-customer-sync-1",
		}
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		// add a payment method for the customer
		mockStripeClient.SetCustomerHasPaymentMethod("stripe-customer-sync-1", true)

		resp, err := authServicer.SyncAddresses(ctx, &authpb.SyncAddressesRequest{
			MakeChanges: true,
			IdsToSync:   []string{},
		})
		require.NoError(t, err)
		require.NotNil(t, resp)

		require.Len(t, resp.UsersUpdated, 1)
		require.Len(t, resp.UsersFailed, 0)
		require.Len(t, resp.UsersNotAttempted, 0)
		require.Len(t, resp.UsersMultiplePaymentMethods, 0)

		// Assert we call Orb
		mockOrbClient.AssertCalled(t, "UpdateCustomerBillingAddress", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything)
	})
}

func TestAuthServicer(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	testCases := []struct {
		name         string
		testCaseFunc func(t *testing.T, bigtableFixture *BigtableFixture)
	}{
		{
			name:         "Basic flow",
			testCaseFunc: caseBasicFlow,
		},
		{
			name:         "GetTokenInfo",
			testCaseFunc: caseTokenLookup,
		},
		{
			name:         "GetUsers",
			testCaseFunc: caseGetUsers,
		},
		{
			name:         "RemoveExtraSelfServeTenantsFromUsers",
			testCaseFunc: caseRemoveExtraSelfServeTenantsFromUsers,
		},
		{
			name:         "RemoveDeletedTenantsFromUsers",
			testCaseFunc: caseRemoveDeletedTenantsFromUsers,
		},
		{
			name:         "DeduplicateUsersByEmail",
			testCaseFunc: caseTestDeduplicateUsersByEmail,
		},
		{
			name:         "DeduplicateUsersByEmail - Actual merge",
			testCaseFunc: caseTestDeduplicateUsersByEmailActualMerge,
		},
		{
			name:         "DeduplicateUsersByEmail - Complex scenarios",
			testCaseFunc: caseTestDeduplicateUsersByEmailComplexScenarios,
		},
		{
			name:         "DeduplicateUserTenantList",
			testCaseFunc: caseDeduplicateUserTenantList,
		},
		{
			name:         "DeduplicateUsersByIdpUserId - Complex scenarios",
			testCaseFunc: caseTestDeduplicateUsersByIdpUserIdComplexScenarios,
		},
		{
			name:         "DeduplicateUsersByIdpUserId",
			testCaseFunc: caseTestDeduplicateUsersByIdpUserIdActualMerge,
		},
		{
			name:         "MigratePopulateIDPUserMappings",
			testCaseFunc: caseMigratePopulateIDPUserMappings,
		},
	}

	for _, tc := range testCases {
		bigtableFixture.ClearTable(t)

		t.Run(tc.name, func(t *testing.T) {
			tc.testCaseFunc(t, bigtableFixture)
		})
	}
}

func TestMigrateLegacySelfServeTeams(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	sut := createAuthServicerSUT(t, bigtableFixture)
	authServicer := sut.authServicer

	var tenants []*tw_pb.Tenant
	for _, tenantId := range []string{
		"tenant-123",
		"tenant-456",
		"tenant-fail",
		"tenant-with-sub",
		"tenant-ended-sub",
		"tenant-incorrect-sub",
	} {
		tenants = append(tenants, &tw_pb.Tenant{
			Id:             tenantId,
			Name:           fmt.Sprintf("%s-name", tenantId),
			ShardNamespace: "test-namespace",
			Cloud:          "test-cloud",
			Config: &tw_pb.Config{
				Configs: map[string]string{
					"is_self_serve_team":        "true",
					"is_legacy_self_serve_team": "true",
				},
			},
			Tier: tw_pb.TenantTier_PROFESSIONAL,
		})
	}

	mockTenantWatcherClient := &tw_client.MockTenantWatcherClient{
		Tenants: tenants,
	}

	// Inject mock clients
	mockOrbClient := orb.NewMockOrbClient()
	mockStripeClient := NewMockStripeClient()
	authServicer.orbClient = mockOrbClient
	authServicer.stripeClient = mockStripeClient
	authServicer.tenantMap = NewTenantMap(
		sut.daoFactory,
		mockTenantWatcherClient,
		"us-central.api.augmentcode.com",
		featureflag.NewLocalFeatureFlagHandler(),
		NewMockAsyncOpsPublisher(),
		audit.NewDefaultAuditLogger(),
	)

	ctx := createCentralAuthorizedRequestContext()

	// Create test admin user
	adminUser := sut.createUser(t, createUserOpts{
		email: "<EMAIL>",
		id:    "admin-user-123",
	})

	userDAO := sut.daoFactory.GetUserDAO()
	_, err := userDAO.TryUpdate(context.Background(), adminUser.Id, func(u *auth_entities.User) bool {
		u.StripeCustomerId = "cus_stripe123"
		u.Tenants = []string{"tenant-123"}
		return true
	}, DefaultRetry)
	require.NoError(t, err)

	// Create test data
	now := time.Now()
	periodStart := now.Add(-24 * time.Hour)
	periodEnd := now.Add(24 * time.Hour)

	req := &pb.MigrateLegacySelfServeTeamsRequest{
		Teams: []*pb.MigrateLegacySelfServeTeamsRequest_LegacyTeamInfo{
			{
				TenantName:               "tenant-123-name",
				AdminUserId:              adminUser.Id,
				StripeSubscriptionId:     "sub_stripe456",
				StripeCurrentPeriodStart: timestamppb.New(periodStart),
				StripeCurrentPeriodEnd:   timestamppb.New(periodEnd),
				OrbExternalPlanId:        "orb_developer_plan",
				OrbPlanVersionNumber:     3,
				Seats:                    5,
			},
		},
	}

	t.Run("successful migration", func(t *testing.T) {
		// Create UserTenantMapping for the admin user
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO("tenant-123-name")
		adminMapping := &auth_entities.UserTenantMapping{
			UserId: adminUser.Id,
			Tenant: "tenant-123-name",
		}
		_, err := tenantMappingDAO.Create(context.Background(), adminMapping)
		require.NoError(t, err)

		// Set up Stripe subscription data
		mockStripeClient.AddDetailedSubscription(&stripe.Subscription{
			ID: "sub_stripe456",
			Items: &stripe.SubscriptionItemList{
				Data: []*stripe.SubscriptionItem{
					{
						Price: &stripe.Price{
							UnitAmount: 3000,
						},
						Quantity: 5,
					},
				},
			},
		})

		// Set up mock expectations for Orb client
		mockOrbClient.On("CreateCustomer", mock.Anything, mock.MatchedBy(func(customer orb.OrbCustomer) bool {
			return customer.Email == adminUser.Email && customer.Name == adminUser.Email
		}), true, mock.AnythingOfType("*string")).Return("orb_customer_123", nil).Once()

		mockOrbClient.On("AddAlertsForCustomer", mock.Anything, "orb_customer_123", "usermessages").Return(nil).Once()

		mockOrbClient.On("CreateSubscription", mock.Anything, mock.MatchedBy(func(subscription orb.OrbSubscription) bool {
			return subscription.CustomerOrbID == "orb_customer_123" &&
				subscription.ExternalPlanID == "orb_developer_plan" &&
				*subscription.PlanVersionNumber == 3 &&
				len(subscription.PriceOverrides) == 2 &&
				subscription.PriceOverrides[0].PriceID == "test_seats_price_id" &&
				subscription.PriceOverrides[0].Quantity == 5.0 &&
				subscription.PriceOverrides[1].PriceID == "test_messages_price_id" &&
				subscription.PriceOverrides[1].Quantity == 5.0*1000.0 &&
				len(subscription.Adjustments) == 1
		}), mock.AnythingOfType("*string")).Return("orb_subscription_456", nil).Once()

		// Execute the migration
		resp, err := authServicer.MigrateLegacySelfServeTeams(ctx, req)

		// Verify response
		require.NoError(t, err)
		require.NotNil(t, resp)
		assert.Equal(t, int32(1), resp.SuccessfulMigrations)
		assert.Equal(t, int32(0), resp.FailedMigrations)
		assert.Len(t, resp.Results, 1)

		result := resp.Results[0]
		assert.True(t, result.Success)
		assert.Empty(t, result.ErrorMessage)

		// Verify user was updated
		userDAO := sut.daoFactory.GetUserDAO()
		updatedUser, err := userDAO.Get(context.Background(), adminUser.Id)
		require.NoError(t, err)
		assert.Equal(t, "orb_customer_123", updatedUser.OrbCustomerId)
		assert.Equal(t, "orb_subscription_456", updatedUser.OrbSubscriptionId)

		// Verify TenantSubscriptionMapping was created
		tsmDAO := sut.daoFactory.GetTenantSubscriptionMappingDAO()
		tsm, err := tsmDAO.Get(context.Background(), "tenant-123")
		require.NoError(t, err)
		assert.Equal(t, "orb_customer_123", tsm.OrbCustomerId)
		assert.Equal(t, "orb_subscription_456", tsm.OrbSubscriptionId)
		assert.Equal(t, "cus_stripe123", tsm.StripeCustomerId)
		assert.Equal(t, auth_entities.BillingMethod_BILLING_METHOD_ORB, tsm.BillingMethod)

		// Verify user's CustomerUIRoles was updated
		utmd := sut.daoFactory.GetUserTenantMappingDAO("tenant-123-name")
		updatedMapping, err := utmd.GetByUser(context.Background(), adminUser.Id)
		require.NoError(t, err)
		assert.Contains(t, updatedMapping.CustomerUiRoles, auth_entities.CustomerUiRole_ADMIN)

		// Verify Stripe subscription was cancelled
		_, err = mockStripeClient.GetSubscription("sub_stripe456")
		assert.Error(t, err) // Should be deleted

		// Verify all mock expectations were met
		mockOrbClient.AssertExpectations(t)
	})

	t.Run("user already has orb customer", func(t *testing.T) {
		// Create user with existing Orb customer ID
		existingUser := sut.createUser(t, createUserOpts{
			email: "<EMAIL>",
			id:    "existing-user-456",
		})

		// Create UserTenantMapping for the existing user
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO("tenant-456-name")
		existingMapping := &auth_entities.UserTenantMapping{
			UserId: existingUser.Id,
			Tenant: "tenant-456-name",
		}
		_, err := tenantMappingDAO.Create(context.Background(), existingMapping)
		require.NoError(t, err)

		// Update user to have existing Orb customer ID
		userDAO := sut.daoFactory.GetUserDAO()
		_, err = userDAO.TryUpdate(context.Background(), existingUser.Id, func(u *auth_entities.User) bool {
			u.OrbCustomerId = "existing_orb_customer"
			u.StripeCustomerId = "cus_stripe789"
			u.Tenants = []string{"tenant-456"}
			return true
		}, DefaultRetry)
		require.NoError(t, err)

		reqWithExisting := &pb.MigrateLegacySelfServeTeamsRequest{
			Teams: []*pb.MigrateLegacySelfServeTeamsRequest_LegacyTeamInfo{
				{
					TenantName:               "tenant-456-name",
					AdminUserId:              existingUser.Id,
					StripeSubscriptionId:     "sub_stripe789",
					StripeCurrentPeriodStart: timestamppb.New(periodStart),
					StripeCurrentPeriodEnd:   timestamppb.New(periodEnd),
					OrbExternalPlanId:        "orb_developer_plan",
					OrbPlanVersionNumber:     3,
					Seats:                    3,
				},
			},
		}

		// Set up Stripe subscription data
		mockStripeClient.AddDetailedSubscription(&stripe.Subscription{
			ID: "sub_stripe789",
			Items: &stripe.SubscriptionItemList{
				Data: []*stripe.SubscriptionItem{
					{
						Price: &stripe.Price{
							UnitAmount: 3000,
						},
						Quantity: 3,
					},
				},
			},
		})

		// Set up mock expectations - should NOT create customer, but should create subscription
		mockOrbClient.On("CreateSubscription", mock.Anything, mock.MatchedBy(func(subscription orb.OrbSubscription) bool {
			return subscription.CustomerOrbID == "existing_orb_customer" && subscription.ExternalPlanID == "orb_developer_plan"
		}), mock.AnythingOfType("*string")).Return("orb_subscription_789", nil).Once()

		// Execute the migration
		resp, err := authServicer.MigrateLegacySelfServeTeams(ctx, reqWithExisting)

		// Verify response
		require.NoError(t, err)
		require.NotNil(t, resp)
		assert.Equal(t, int32(1), resp.SuccessfulMigrations)
		assert.Equal(t, int32(0), resp.FailedMigrations)

		result := resp.Results[0]
		assert.True(t, result.Success)

		// Verify all mock expectations were met
		mockOrbClient.AssertExpectations(t)
	})

	t.Run("migration failure", func(t *testing.T) {
		failUser := sut.createUser(t, createUserOpts{
			email: "<EMAIL>",
			id:    "fail-user-789",
		})

		userDAO := sut.daoFactory.GetUserDAO()
		_, err = userDAO.TryUpdate(context.Background(), failUser.Id, func(u *auth_entities.User) bool {
			u.StripeCustomerId = "cus_fail"
			u.Tenants = []string{"tenant-fail"}
			return true
		}, DefaultRetry)
		require.NoError(t, err)

		// Create UserTenantMapping for the fail user
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO("tenant-fail-name")
		failMapping := &auth_entities.UserTenantMapping{
			UserId: failUser.Id,
			Tenant: "tenant-fail-name",
		}
		_, err := tenantMappingDAO.Create(context.Background(), failMapping)
		require.NoError(t, err)

		reqWithFailure := &pb.MigrateLegacySelfServeTeamsRequest{
			Teams: []*pb.MigrateLegacySelfServeTeamsRequest_LegacyTeamInfo{
				{
					TenantName:               "tenant-fail-name",
					AdminUserId:              failUser.Id,
					StripeSubscriptionId:     "sub_fail",
					StripeCurrentPeriodStart: timestamppb.New(periodStart),
					StripeCurrentPeriodEnd:   timestamppb.New(periodEnd),
					OrbExternalPlanId:        "plan_fail",
					OrbPlanVersionNumber:     4,
					Seats:                    2,
				},
			},
		}

		// Set up mock to fail on customer creation
		mockOrbClient.On("CreateCustomer", mock.Anything, mock.Anything, true, mock.AnythingOfType("*string")).Return("", fmt.Errorf("orb customer creation failed")).Once()

		// Execute the migration
		resp, err := authServicer.MigrateLegacySelfServeTeams(ctx, reqWithFailure)

		// Verify response
		require.NoError(t, err)
		require.NotNil(t, resp)
		assert.Equal(t, int32(0), resp.SuccessfulMigrations)
		assert.Equal(t, int32(1), resp.FailedMigrations)

		result := resp.Results[0]
		assert.False(t, result.Success)
		assert.Contains(t, result.ErrorMessage, "orb customer creation failed")

		// Verify all mock expectations were met
		mockOrbClient.AssertExpectations(t)
	})

	t.Run("user with active subscription - correct configuration", func(t *testing.T) {
		// Create user with existing Orb customer and subscription IDs
		userWithSub := sut.createUser(t, createUserOpts{
			email: "<EMAIL>",
			id:    "user-with-sub-123",
		})

		// Create UserTenantMapping for the user
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO("tenant-with-sub-name")
		userMapping := &auth_entities.UserTenantMapping{
			UserId: userWithSub.Id,
			Tenant: "tenant-with-sub-name",
		}
		_, err := tenantMappingDAO.Create(context.Background(), userMapping)
		require.NoError(t, err)

		// Update user to have existing Orb customer and subscription IDs
		userDAO := sut.daoFactory.GetUserDAO()
		_, err = userDAO.TryUpdate(context.Background(), userWithSub.Id, func(u *auth_entities.User) bool {
			u.StripeCustomerId = "cus_stripe_with_sub"
			u.Tenants = []string{"tenant-with-sub"}
			u.OrbCustomerId = "existing_orb_customer_123"
			u.OrbSubscriptionId = "existing_orb_subscription_123"
			return true
		}, DefaultRetry)
		require.NoError(t, err)

		reqWithExistingSub := &pb.MigrateLegacySelfServeTeamsRequest{
			Teams: []*pb.MigrateLegacySelfServeTeamsRequest_LegacyTeamInfo{
				{
					TenantName:               "tenant-with-sub-name",
					AdminUserId:              userWithSub.Id,
					StripeSubscriptionId:     "sub_stripe_with_sub",
					StripeCurrentPeriodStart: timestamppb.New(periodStart),
					StripeCurrentPeriodEnd:   timestamppb.New(periodEnd),
					OrbExternalPlanId:        "orb_developer_plan",
					OrbPlanVersionNumber:     3,
					Seats:                    5,
				},
			},
		}

		// Mock GetUserSubscription to return an active subscription with correct configuration
		mockOrbClient.On("GetUserSubscription", mock.Anything, "existing_orb_subscription_123", mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: "existing_orb_subscription_123",
			OrbStatus:         "active",
			ExternalPlanID:    "orb_developer_plan",
			CurrentFixedQuantities: &orb.FixedQuantities{
				Seats: 5,
			},
		}, nil).Once()

		// Should NOT create customer or subscription since existing one is correct
		// Should still cancel Stripe subscription
		// No CreateCustomer or CreateSubscription calls expected

		// Execute the migration
		resp, err := authServicer.MigrateLegacySelfServeTeams(ctx, reqWithExistingSub)

		// Verify response
		require.NoError(t, err)
		require.NotNil(t, resp)
		assert.Equal(t, int32(1), resp.SuccessfulMigrations)
		assert.Equal(t, int32(0), resp.FailedMigrations)

		result := resp.Results[0]
		assert.True(t, result.Success)
		assert.Empty(t, result.ErrorMessage)

		// Verify user was NOT updated (already has correct IDs)
		updatedUser, err := userDAO.Get(context.Background(), userWithSub.Id)
		require.NoError(t, err)
		assert.Equal(t, "existing_orb_customer_123", updatedUser.OrbCustomerId)
		assert.Equal(t, "existing_orb_subscription_123", updatedUser.OrbSubscriptionId)

		// Verify all mock expectations were met
		mockOrbClient.AssertExpectations(t)
	})

	t.Run("user with active subscription - incorrect configuration", func(t *testing.T) {
		// Create user with existing Orb customer and subscription IDs
		userWithIncorrectSub := sut.createUser(t, createUserOpts{
			email: "<EMAIL>",
			id:    "user-incorrect-sub-456",
		})

		// Create UserTenantMapping for the user
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO("tenant-incorrect-sub-name")
		userMapping := &auth_entities.UserTenantMapping{
			UserId: userWithIncorrectSub.Id,
			Tenant: "tenant-incorrect-sub-name",
		}
		_, err := tenantMappingDAO.Create(context.Background(), userMapping)
		require.NoError(t, err)

		// Update user to have existing Orb customer and subscription IDs
		userDAO := sut.daoFactory.GetUserDAO()
		_, err = userDAO.TryUpdate(context.Background(), userWithIncorrectSub.Id, func(u *auth_entities.User) bool {
			u.StripeCustomerId = "cus_stripe_incorrect"
			u.Tenants = []string{"tenant-incorrect-sub"}
			u.OrbCustomerId = "existing_orb_customer_456"
			u.OrbSubscriptionId = "existing_orb_subscription_456"
			return true
		}, DefaultRetry)
		require.NoError(t, err)

		reqWithIncorrectSub := &pb.MigrateLegacySelfServeTeamsRequest{
			Teams: []*pb.MigrateLegacySelfServeTeamsRequest_LegacyTeamInfo{
				{
					TenantName:               "tenant-incorrect-sub-name",
					AdminUserId:              userWithIncorrectSub.Id,
					StripeSubscriptionId:     "sub_stripe_incorrect",
					StripeCurrentPeriodStart: timestamppb.New(periodStart),
					StripeCurrentPeriodEnd:   timestamppb.New(periodEnd),
					OrbExternalPlanId:        "orb_developer_plan",
					OrbPlanVersionNumber:     3,
					Seats:                    5,
				},
			},
		}

		// Mock GetUserSubscription to return an active subscription with WRONG configuration (different plan)
		mockOrbClient.On("GetUserSubscription", mock.Anything, "existing_orb_subscription_456", mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: "existing_orb_subscription_456",
			OrbStatus:         "active",
			ExternalPlanID:    "orb_pro_plan", // Wrong plan
			CurrentFixedQuantities: &orb.FixedQuantities{
				Seats: 5,
			},
		}, nil).Once()

		// Execute the migration
		resp, err := authServicer.MigrateLegacySelfServeTeams(ctx, reqWithIncorrectSub)

		// Verify response - should fail due to incorrect configuration
		require.NoError(t, err)
		require.NotNil(t, resp)
		assert.Equal(t, int32(0), resp.SuccessfulMigrations)
		assert.Equal(t, int32(1), resp.FailedMigrations)

		result := resp.Results[0]
		assert.False(t, result.Success)
		assert.Contains(t, result.ErrorMessage, "Orb subscription already exists, but subscription is not configured correctly")

		// Verify all mock expectations were met
		mockOrbClient.AssertExpectations(t)
	})

	t.Run("user with ended subscription", func(t *testing.T) {
		// Create user with existing Orb customer and subscription IDs
		userWithEndedSub := sut.createUser(t, createUserOpts{
			email: "<EMAIL>",
			id:    "user-ended-sub-202",
		})

		// Create UserTenantMapping for the user
		tenantMappingDAO := sut.daoFactory.GetUserTenantMappingDAO("tenant-ended-sub-name")
		userMapping := &auth_entities.UserTenantMapping{
			UserId: userWithEndedSub.Id,
			Tenant: "tenant-ended-sub-name",
		}
		_, err := tenantMappingDAO.Create(context.Background(), userMapping)
		require.NoError(t, err)

		// Update user to have existing Orb customer and subscription IDs
		userDAO := sut.daoFactory.GetUserDAO()
		_, err = userDAO.TryUpdate(context.Background(), userWithEndedSub.Id, func(u *auth_entities.User) bool {
			u.StripeCustomerId = "cus_stripe_ended"
			u.Tenants = []string{"tenant-ended-sub"}
			u.OrbCustomerId = "existing_orb_customer_202"
			u.OrbSubscriptionId = "existing_orb_subscription_202"
			return true
		}, DefaultRetry)
		require.NoError(t, err)

		reqWithEndedSub := &pb.MigrateLegacySelfServeTeamsRequest{
			Teams: []*pb.MigrateLegacySelfServeTeamsRequest_LegacyTeamInfo{
				{
					TenantName:               "tenant-ended-sub-name",
					AdminUserId:              userWithEndedSub.Id,
					StripeSubscriptionId:     "sub_stripe_ended",
					StripeCurrentPeriodStart: timestamppb.New(periodStart),
					StripeCurrentPeriodEnd:   timestamppb.New(periodEnd),
					OrbExternalPlanId:        "orb_developer_plan",
					OrbPlanVersionNumber:     3,
					Seats:                    5,
				},
			},
		}

		// Set up Stripe subscription data
		mockStripeClient.AddDetailedSubscription(&stripe.Subscription{
			ID: "sub_stripe_ended",
			Items: &stripe.SubscriptionItemList{
				Data: []*stripe.SubscriptionItem{
					{
						Price: &stripe.Price{
							UnitAmount: 3000,
						},
						Quantity: 5,
					},
				},
			},
		})

		// Mock GetUserSubscription to return an ended subscription (should be replaced)
		mockOrbClient.On("GetUserSubscription", mock.Anything, "existing_orb_subscription_202", mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: "existing_orb_subscription_202",
			OrbStatus:         "ended", // Ended status - should be replaced
			ExternalPlanID:    "orb_developer_plan",
		}, nil).Once()

		// Should create a new subscription since existing one is ended
		mockOrbClient.On("CreateSubscription", mock.Anything, mock.MatchedBy(func(subscription orb.OrbSubscription) bool {
			return subscription.CustomerOrbID == "existing_orb_customer_202" &&
				subscription.ExternalPlanID == "orb_developer_plan" &&
				*subscription.PlanVersionNumber == 3 &&
				len(subscription.PriceOverrides) == 2 &&
				subscription.PriceOverrides[0].PriceID == "test_seats_price_id" &&
				subscription.PriceOverrides[0].Quantity == 5.0 &&
				subscription.PriceOverrides[1].PriceID == "test_messages_price_id" &&
				subscription.PriceOverrides[1].Quantity == 5.0*1000.0 &&
				len(subscription.Adjustments) == 1
		}), mock.AnythingOfType("*string")).Return("new_orb_subscription_202", nil).Once()

		// Execute the migration
		resp, err := authServicer.MigrateLegacySelfServeTeams(ctx, reqWithEndedSub)

		// Verify response
		require.NoError(t, err)
		require.NotNil(t, resp)
		assert.Equal(t, int32(1), resp.SuccessfulMigrations)
		assert.Equal(t, int32(0), resp.FailedMigrations)

		result := resp.Results[0]
		assert.True(t, result.Success)
		assert.Empty(t, result.ErrorMessage)

		// Verify user was updated with new subscription ID
		updatedUser, err := userDAO.Get(context.Background(), userWithEndedSub.Id)
		require.NoError(t, err)
		assert.Equal(t, "existing_orb_customer_202", updatedUser.OrbCustomerId)
		assert.Equal(t, "new_orb_subscription_202", updatedUser.OrbSubscriptionId)

		// Verify all mock expectations were met
		mockOrbClient.AssertExpectations(t)
	})
}

func caseMigratePopulateIDPUserMappings(t *testing.T, bigtableFixture *BigtableFixture) {
	sut := createAuthServicerSUT(t, bigtableFixture)

	ctx := createCentralAuthorizedRequestContext()

	// Create users
	users := make([]*auth_entities.User, 0)
	num_users := 2
	for i := 0; i < num_users; i++ {
		users = append(users, sut.createUser(t, createUserOpts{idpUserID: fmt.Sprintf("idp-%d", i)}))
	}

	idpUserMappingDAO := sut.daoFactory.GetIDPUserMappingDAO()
	count := 0
	err := idpUserMappingDAO.FindAll(ctx, func(_ *auth_entities.IdpUserMapping) bool {
		count++
		return true
	})
	require.NoError(t, err)
	require.Equal(t, 0, count)

	// Run MigratePopulateIDPUserMappings
	_, err = sut.authServicer.MigratePopulateIDPUserMappings(ctx, &pb.MigratePopulateIDPUserMappingsRequest{})
	require.NoError(t, err)

	err = idpUserMappingDAO.FindAll(ctx, func(_ *auth_entities.IdpUserMapping) bool {
		count++
		return true
	})
	require.NoError(t, err)
	require.Equal(t, num_users, count)
}
