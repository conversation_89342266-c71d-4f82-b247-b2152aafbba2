local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';
local pubsubLib = import 'services/lib/pubsub/pubsub_lib.jsonnet';

function(env, namespace, cloud, namespace_config)
  local appName = 'checkpoint-indexer';

  // mutual TLS is enabled if the namespace config has the forceMtls flag set
  // MTLS ensures that the client and server certificates are valid
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);

  // create dynamic feature flag config
  local dynamicFeatureFlagConfig = dynamicFeatureFlagLib.createLaunchDarklySecret(env=env, cloud=cloud, namespace=namespace, appName=appName);

  // creates a client certificate so that the pod can authenticiate to grpc servers (incl. itself for health checks)
  // in the same namespace
  local clientCert = certLib.createClientCert(
    name='%s-client-cert' % appName,
    namespace=namespace,
    appName=appName,
    volumeName='client-certs',
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
  );

  // creates a client certificate so that the pod can authenticiate to grpc servers running in the central namespace
  local centralClientCert = certLib.createCentralClientCert(
    name='%s-central-client-cert' % appName,
    namespace=namespace,
    env=env,
    appName=appName,
    volumeName='central-client-certs',
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
  );

  // creates a server certificate for MTLS
  local serverCert = certLib.createServerCert(name='%s-server-certificate' % appName,
                                              namespace=namespace,
                                              appName=appName,
                                              dnsNames=grpcLib.grpcServiceNames(appName),
                                              volumeName='certs');

  // creates a service account for the pod
  // a service account is needed to access GCP resources or kubernetes resources
  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true, overridePrefix='chkp-idx-',
  );


  local checkpointIndexSubscriber = pubsubLib.namespaceSubscriber(
    env=env,
    namespace=namespace,
    appName=appName,
    cloud=cloud,
    publisherAppName='checkpoint-indexing',
    serviceAccount=serviceAccount,
    deadLetterMaxDeliveryAttempts=5,
    spec={
      // indexing can take longer than 10 minutes on large checkpoints, but the pubsub max is 10 minutes
      ackDeadlineSeconds: 10 * 60,
      retryPolicy: {
        minimumBackoff: '60s',
        maximumBackoff: '300s',
      },
    },
  );

  local checkpointIndexerResponseTopicName = pubsubLib.getTopicName(namespace=namespace, appName=appName, topicSuffix='checkpoint-indexing');
  local topicIamPolicy = pubsubLib.publisherTopicIAMPolicy(
    env=env,
    namespace=namespace,
    appName=appName,
    publisherAppName=appName,
    serviceAccount=serviceAccount,
    cloud=cloud,
    isDeadLetterTopic=false,
    topicSuffix='checkpoint-indexing',
  );

  local cpuCount = if namespace_config.flags.usePremiumCpuHighmem then 7 else 4;

  // configuration that will be passed to the server as a JSON file
  local config = {
    bind_address: '0.0.0.0:50051',
    server_mtls_config: if mtls then serverCert.config else null,
    central_client_mtls_config: if mtls then centralClientCert.config else null,
    client_mtls_config: if mtls then clientCert.config else null,
    metrics_server_bind_address: '0.0.0.0',
    metrics_server_port: 9090,
    dynamic_feature_flags_endpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
    auth_config: {
      token_exchange_endpoint: endpointsLib.getTokenExchangeGrpcUrl(env=env, namespace=namespace, cloud=cloud),
      token_exchange_request_timeout_s: 10.0,
    },
    content_manager_endpoint: 'content-manager-svc:50051',
    content_manager_request_timeout_s: 60.0,
    tenant_watcher_endpoint: endpointsLib.getTenantWatcherGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    tenant_watcher_request_timeout_s: 10.0,
    checkpoint_cache_size_bytes: 256 * 1024 * 1024,
    cache_tti_seconds: 3 * 24 * 60 * 60,  // 3 days -- enough for the weekend
    embeddings_cache_size_bytes: 8 * 1024 * 1024 * 1024,
    subscription_name: checkpointIndexSubscriber.subscriptionName,
    topic_name: checkpointIndexerResponseTopicName,

    // Index factory configuration
    // Number of threads to use for training the index
    // leave one CPU free for health check etc and one because scann adds one
    training_threads: cpuCount - 2,
    // Number of dimensions per block
    dims_per_block: 4,
    // Threshold for asymmetric hashing, usually 0.2 is a good value
    ah_threshold: 0.2,
    // Number of training samples to use to train the code book
    training_sample_size: 64 * 1024,
    // Threshold of missing blobs to disable index creation
    max_missing_blobs: 500,
    // threshold of embeddings in a given checkpoint before index creation is disabled to prevent OOM
    max_embeddings_to_index: if namespace_config.flags.usePremiumCpuHighmem then 5000000 else 2500000,
    // threshold of blobs in a checkpoint before index creation is disabled to avoid backlog
    max_blobs_to_index: 500000,
  };

  // a config map is a Kubernetes object that contains configuration data it is "mounted" into a pod
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);


  // creates a service for the pod
  // a service is needed to expose the pod to the outside world
  local services = grpcLib.grpcService(appName=appName, namespace=namespace);

  // creates a container that runs the server
  local container = {
    name: appName,

    target: {
      name: '//services/checkpoint_indexer/server:image',
      dst: 'checkpoint-indexer',
    },

    // the arguments that are passed to the server
    args: [
      '--config-file',
      configMap.filename,
      '--launch-darkly-secrets-file',
      dynamicFeatureFlagConfig.secretsFilePath,
    ],

    // the ports that the pod exposes
    ports: [
      {
        containerPort: 50051,
        name: 'grpc-svc',
      },
    ],

    // the environment variables that are passed to the server
    env: telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)) + dynamicFeatureFlagConfig.env,

    // the volumes that are mounted into the pod
    volumeMounts: [
      configMap.volumeMountDef,
      // the dynamic feature flag config is mounted into the pod
      centralClientCert.volumeMountDef,
      // the server certificate is mounted into the pod
      serverCert.volumeMountDef,
      // the client certificate is mounted into the pod
      clientCert.volumeMountDef,
      // the dynamic feature flag config is mounted into the pod
      dynamicFeatureFlagConfig.volumeMountDef,
    ],
    // the health check is used to determine if the pod is ready to receive traffic
    readinessProbe: grpcLib.grpcHealthCheck(appName + '-svc', tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
      periodSeconds: 30,
    },
    // the liveness check is used to determine if the pod is alive
    livenessProbe: grpcLib.grpcHealthCheck(appName + '-svc', tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
      periodSeconds: 30,
    },
    // the resource limits are used to determine how much CPU and memory the pod can use
    resources: {
      limits: {
        cpu: if namespace_config.flags.usePremiumCpuHighmem then 7 else '3500m',
        memory: if namespace_config.flags.usePremiumCpuHighmem then '52Gi' else '26Gi',
      },
    },
  };
  local resource = if namespace_config.flags.usePremiumCpuHighmem then 'premiumCpuHighmem' else 'premiumCpu';
  local tolerations = nodeLib.tolerations(resource=resource, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=resource, env=env, cloud=cloud, appName=appName);
  local pod = {
    // the service account is used to access GCP resources or kubernetes resources
    serviceAccountName: serviceAccount.name,
    containers: [
      container,
    ],
    affinity: affinity,
    tolerations: tolerations,
    priorityClassName: cloudInfo.envToPriorityClass(env),
    volumes: [
      configMap.podVolumeDef,
      // the dynamic feature flag config is mounted into the pod
      dynamicFeatureFlagConfig.podVolumeDef,
      // the client certificate is mounted into the pod
      centralClientCert.podVolumeDef,
      // the server certificate is mounted into the pod
      serverCert.podVolumeDef,
      // the client certificate is mounted into the pod
      clientCert.podVolumeDef,
    ],
  };

  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      // the minimum amount of time that a pod needs to be ready before the deployment is considered successful
      minReadySeconds: if env == 'DEV' then 0 else 60,
      // the number of pods that are running at the same time
      replicas: if env == 'DEV' then 1 else 2,
      // the strategy is used to determine how the deployment is rolled out
      strategy: {
        // the strategy is used to determine how the deployment is rolled out
        type: 'RollingUpdate',
        // the maximum number of pods that can be created above the desired number of pods
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod,
      },
    },
  };

  local scaledObject = {
    apiVersion: 'keda.sh/v1alpha1',
    kind: 'ScaledObject',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      scaleTargetRef: {
        apiVersion: 'apps/v1',
        kind: 'Deployment',
        name: appName,
      },
      // TODO: configurable
      minReplicaCount: 1,
      maxReplicaCount: 4,
      advanced: {
        // Keda passes these to the HPA.
        // - https://keda.sh/docs/2.14/concepts/scaling-deployments/
        // - https://kubernetes.io/docs/tasks/run-application/horizontal-pod-autoscale/#configurable-scaling-behavior
        horizontalPodAutoscalerConfig: {
          behavior: {
            scaleDown: {
              // This is a rolling maximum over the last 10 minutes
              // to ensure that the indexer can do useful work before scaling down.
              stabilizationWindowSeconds: 600,
              policies: [
                {
                  // Reduce the number of indexers by 50% every 5 minutes.
                  type: 'Percent',
                  value: 50,
                  periodSeconds: 300,
                },
              ],
            },
            scaleUp: {
              stabilizationWindowSeconds: 120,
              policies: [
                {
                  // Increase the number of indexers by 1 every 5 minutes.
                  type: 'Pods',
                  value: 1,
                  periodSeconds: 300,
                },
              ],
              selectPolicy: 'Max',  // default; select the policy that affects the most pods
            },
          },
        },
      },
      triggers: [
        {
          type: 'prometheus',
          metadata: {
            serverAddress: 'http://gmp-frontend.monitoring.svc.cluster.local:9090',
            metricName: 'au_checkpoint_indexer_pubsub_message_processing_latency_sum',
            // if the average indexer is more than 30% busy, get more indexers
            // Threshold defines the TARGET VALUE for the HPA. The documentation elaborates that the target value for the resource is:
            //     desiredReplicas = ceil[currentReplicas * ( currentMetricValue / desiredMetricValue )]
            // To get robust upscaling as soon as we exceed 30% utilization, move 20% of that out of the denominator and into the metric query (the 0.2 below)
            threshold: '0.1',
            query:
              // Define a query that represents the utilization of the average indexer in this namespace
              //
              // latency_sum is the sum of all indexing (which is single-threaded), so rate (i.e. increase per second) gives a utilization measure between 0 and 1 with units "seconds per second".
              '(avg(rate(au_checkpoint_indexer_pubsub_message_processing_latency_sum{namespace="%s"}[10m]))) - 0.2' % namespace,
          },
        },
      ],
    },
  };
  lib.flatten([
    configMap.objects,
    serviceAccount.objects,
    serverCert.objects,
    centralClientCert.objects,
    clientCert.objects,
    scaledObject,
    dynamicFeatureFlagConfig.k8s_objects,
    deployment,
    services,
    checkpointIndexSubscriber.objects,
    topicIamPolicy,
  ])
